# OneAM Password Encoding Documentation

## 🔐 Cách OneAM xử lý Password

### 1. OneAM Password Encoding Process

OneAM sử dụng **double HMAC-SHA256** để encode password:

```java
// OneAM UserService.encodePassword()
public static String encodePassword(String password, int level) {
    if (level == 1) {
        return Encode.hmacSHA256ToHex(ConfigUtils.get("app.key.password.1"), password);
    }
    if (level == 12) {
        password = Encode.hmacSHA256ToHex(ConfigUtils.get("app.key.password.1"), password);
    }
    if (level == 2 || level == 12) {
        return Encode.hmacSHA256ToHex(ConfigUtils.get("app.key.password.2"), password);
    }
}
```

### 2. Password Encoding Levels

- **Level 1**: `HMAC-SHA256(key1, plainPassword)`
- **Level 2**: `HMAC-SHA256(key2, result_from_level_1)`  
- **Level 12**: `HMAC-SHA256(key2, HMAC-SHA256(key1, plainPassword))`

### 3. OneAM Database Storage

- Password trong `ONEAM.TB_USER.S_PASSWORD` đã được encode với **level 2**
- Khi user đăng nhập, OneAM encode password input với **level 2** rồi so sánh

## 🔄 Migration Strategy

### 1. Migration Process

1. **Lấy password từ OneAM**: Password đã được encode (level 2)
2. **Double encode cho Keycloak**: Encode thêm 1 lần nữa (level 2)
3. **Lưu vào Keycloak**: Password được double-encode

### 2. Login Process

1. **User nhập password**: Plain text
2. **OneAM encode**: `HMAC-SHA256(key2, HMAC-SHA256(key1, password))`
3. **Keycloak encode**: `HMAC-SHA256(key2, OneAM_encoded_password)`
4. **So sánh**: Keycloak_encoded vs stored_password

## 🛠️ Configuration

### 1. OneAM Keys

Cần lấy 2 keys từ OneAM config:

```bash
# Tìm trong OneAM config files
grep -r "app.key.password" /root/projects/onepay/portal/oneam-service/etc/
```

### 2. Environment Variables

```bash
# Set trong oneam-keys.env
ONEAM_PASSWORD_KEY_1=your-actual-key-1
ONEAM_PASSWORD_KEY_2=your-actual-key-2
```

### 3. Migration Tool Usage

```bash
# Load keys và run migration
source oneam-keys.env
./run-migration.sh
```

## 🔍 Debug Password Encoding

Migration tool sẽ log password encoding process:

```
Debug - OneAM Original Password: a1b2c3d4e5f6...
Debug - Double Encoded Password: x9y8z7w6v5u4...
```

## ⚠️ Important Notes

1. **Keys phải chính xác**: Sai key sẽ không login được
2. **Double encoding**: Cần encode 2 lần để match với OneAM
3. **Test trước**: Nên test với 1 user trước khi migrate hàng loạt
4. **Backup**: Luôn backup Keycloak trước khi migrate

## 🧪 Testing

### 1. Test Password Encoding

```go
// Test với password "123456"
originalPassword := "123456"
oneamEncoded := encodePasswordOneAM(originalPassword, 2)
doubleEncoded := encodePasswordForLogin(oneamEncoded)

fmt.Printf("Original: %s\n", originalPassword)
fmt.Printf("OneAM Level 2: %s\n", oneamEncoded)  
fmt.Printf("Double Encoded: %s\n", doubleEncoded)
```

### 2. Verify Login

1. Migrate 1 test user
2. Try login với password gốc
3. Check Keycloak logs
4. Verify authentication flow

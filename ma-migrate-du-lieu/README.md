# OneAM to Keycloak User Migration Tool

Công cụ migrate users từ OneAM Oracle database sang Keycloak cho Merchant Portal.

## 🎯 <PERSON><PERSON><PERSON> đích

- Migrate tất cả users từ OneAM database sang Keycloak
- Bảo toàn thông tin user (email, mobile, address, etc.)
- Mapping OneAM user ID với Keycloak user ID
- Tạo backup và audit log

## 📋 Yêu cầu hệ thống

- Go 1.23+
- Oracle Instant Client
- Kết nối đến OneAM Oracle database
- Kết nối đến Keycloak server
- Quyền admin trên Keycloak realm

## 🚀 Cách sử dụng

### 1. <PERSON><PERSON><PERSON> hình
```bash
# Load OneAM password keys
source oneam-keys.env

# Chỉnh sửa config nếu cần
nano config.env
```

### 2. Chạy migration
```bash
# Chạy migration trực tiếp
go run main.go

# Hoặc build và chạy
go build -o migrate main.go
./migrate
```

### 3. Cài đặt dependencies
```bash
# Cài đặt Go dependencies
go mod tidy
unzip instantclient-basic-linux.x64-********.0dbru.zip
export LD_LIBRARY_PATH=/path/to/instantclient_21_1:$LD_LIBRARY_PATH
```

### 3. Cấu hình
Chỉnh sửa file `config.env`:
```bash
# Keycloak Configuration
KEYCLOAK_BASE_URL=https://your-keycloak-server/auth
KEYCLOAK_REALM=merchant-portal
KEYCLOAK_CLIENT_ID=ma-migration-tool
KEYCLOAK_CLIENT_SECRET=your-client-secret

# OneAM Database Configuration
ONEAM_DB_HOST=your-oracle-host
ONEAM_DB_PORT=1521
ONEAM_DB_USER=oneam
ONEAM_DB_PASSWORD=oneam
ONEAM_DB_SERVICE=orcl
```

## 🔧 Sử dụng

### Chạy migration tự động
```bash
./run-migration.sh
```

### Chạy migration thủ công
```bash
# Build tool
go build -o ma-migrator main.go

# Chạy migration
./ma-migrator
```

### Dry run (test không tạo users)
```bash
# Set MIGRATION_DRY_RUN=true trong config.env
export MIGRATION_DRY_RUN=true
./ma-migrator
```

## 📊 Cấu trúc dữ liệu

### OneAM Database (Oracle)
- **Table**: `TB_USER`
- **Key fields**: `S_ID`, `S_EMAIL`, `S_PASSWORD`, `S_FIRST_NAME`, `S_LAST_NAME`
- **Additional fields**: `S_MOBILE`, `S_PROVINCE`, `S_DISTRICT`, `S_ADDRESS1`

### Keycloak Mapping
- **Username**: OneAM `S_EMAIL`
- **Email**: OneAM `S_EMAIL`
- **Attributes**: Mobile, Province, District, Address
- **Required Actions**: `VERIFY_EMAIL`, `UPDATE_PASSWORD`

## 🔄 Migration Process

1. **Connect to OneAM Oracle database**
2. **Query all users from TB_USER table**
3. **Check if user already exists in Keycloak**
4. **Create new user in Keycloak with attributes**
5. **Set required actions for password reset**
6. **Generate migration report**

## 📁 Output Files

```
ma-migrate-du-lieu/
├── backups/
│   └── 20241229_143022/
│       ├── migration.log
│       └── summary.txt
├── logs/
│   └── migration.log
└── ma-migrator
```

## 🛡️ Security Considerations

- **Client Secret**: Được lưu trong config.env (không commit vào git)
- **Database Credentials**: Sử dụng environment variables
- **Temporary Passwords**: Users sẽ phải đổi password lần đầu
- **Audit Log**: Tất cả actions được log

## 🔍 Troubleshooting

### Lỗi kết nối Oracle
```bash
# Kiểm tra Oracle client
sqlplus oneam/oneam@10.38.130.31:1112/orcl

# Kiểm tra LD_LIBRARY_PATH
echo $LD_LIBRARY_PATH
```

### Lỗi kết nối Keycloak
```bash
# Test Keycloak API
curl -X GET "https://dev3-secure.onepay.vn/auth/realms/merchant-portal"
```

### Lỗi permissions
```bash
# Kiểm tra quyền admin trên Keycloak
# Đảm bảo client có quyền manage users
```

## 📈 Performance

- **Batch Size**: 100 users per batch
- **Delay**: 100ms between requests
- **Retry**: 3 attempts for failed requests
- **Expected Time**: ~1 minute per 1000 users

## 🔄 Rollback

Nếu cần rollback:
1. **Delete users từ Keycloak Admin Console**
2. **Restore từ backup nếu có**
3. **Re-run migration với updated data**

## 📞 Support

- **Logs**: Check `./logs/migration.log`
- **Backup**: Check `./backups/` directory
- **Issues**: Contact OnePay Development Team

## 🎯 Next Steps

Sau khi migration hoàn thành:
1. **Verify users trong Keycloak Admin Console**
2. **Test login với migrated users**
3. **Configure authentication flows**
4. **Update user passwords**
5. **Setup OTP authentication**

package main

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	_ "github.com/godror/godror" // Oracle driver
)

// --- 1. <PERSON><PERSON><PERSON> h<PERSON> ---
const (
	keycloakBaseURL = "https://dev13-ma.opdev.vn/auth-ma" // Thay đổi theo Keycloak server thực tế
	realmName       = "merchant-portal"
	clientID        = "ma-migration-tool"
	clientSecret    = "VGRnWj0pDyf54Stg4WtGl5zHmmZJQZwR" // THAY THẾ BẰNG SECRET THẬT CỦA BẠN!
)

// --- OneAM Password Encoding Keys ---
// Hardcoded keys từ OneAM systemd service
const (
	oneamPasswordKey1 = "23BF1DC999734E6A6860BD56273AA455"
	oneamPasswordKey2 = "23BF1DC999734E6A6860BD56273AA455"
)

// --- 2. <PERSON><PERSON><PERSON><PERSON> sở dữ liệu Oracle (OneAM) ---
const (
	dbHost     = "*************" // OneAM database server
	dbPort     = 1114
	dbUser     = "oneam"
	dbPassword = "oneam"
	dbService  = "orcl"
	dbTable    = "TB_USER"
)

// Tên cột trong bảng TB_USER của OneAM
const (
	colUserID         = "S_ID"
	colUsername       = "S_EMAIL" // OneAM sử dụng email làm username
	colPassword       = "S_PASSWORD"
	colFirstName      = "S_FIRST_NAME"
	colLastName       = "S_LAST_NAME"
	colEmail          = "S_EMAIL"
	colMobile         = "S_MOBILE"
	colCountryCode    = "S_COUNTRY_CODE"
	colProvince       = "S_PROVINCE"
	colDistrict       = "S_DISTRICT"
	colAddress1       = "S_ADDRESS1"
	colAddress2       = "S_ADDRESS2"
	colStatus         = "S_STATUS"          // Trạng thái user (ACTIVE, INACTIVE, etc.)
	colCreateDate     = "D_CREATE"          // Ngày tạo
	colLastChange     = "D_UPDATE"          // Ngày cập nhật cuối
	colLastLogin      = "D_LAST_LOGIN"      // Lần đăng nhập cuối
	colAuthLevel      = "N_AUTH_LEVEL"      // Mức độ xác thực
	colValidateEmail  = "D_VALIDATE_EMAIL"  // Ngày xác thực email
	colValidateMobile = "D_VALIDATE_MOBILE" // Ngày xác thực mobile
)

// Cấu trúc để map dữ liệu người dùng từ DB Oracle OneAM
type OneAMUser struct {
	UserID         string
	Username       string
	Password       sql.NullString
	FirstName      sql.NullString
	LastName       sql.NullString
	Email          sql.NullString
	Mobile         sql.NullString
	CountryCode    sql.NullString
	Province       sql.NullString
	District       sql.NullString
	Address1       sql.NullString
	Address2       sql.NullString
	Status         sql.NullString // S_STATUS
	CreateDate     sql.NullTime   // D_CREATE
	LastChange     sql.NullTime   // D_UPDATE
	LastLogin      sql.NullTime   // D_LAST_LOGIN
	AuthLevel      sql.NullInt64  // N_AUTH_LEVEL
	ValidateEmail  sql.NullTime   // D_VALIDATE_EMAIL
	ValidateMobile sql.NullTime   // D_VALIDATE_MOBILE
}

// Cấu trúc cho payload tạo/cập nhật người dùng Keycloak
type KeycloakUserPayload struct {
	ID              string              `json:"id,omitempty"`
	Username        string              `json:"username"`
	FirstName       string              `json:"firstName,omitempty"`
	LastName        string              `json:"lastName,omitempty"`
	Email           string              `json:"email,omitempty"`
	EmailVerified   bool                `json:"emailVerified"`
	Enabled         bool                `json:"enabled"`
	Attributes      map[string][]string `json:"attributes,omitempty"`
	Credentials     []Credential        `json:"credentials,omitempty"`
	RealmRoles      []string            `json:"realmRoles,omitempty"`
	ClientRoles     map[string][]string `json:"clientRoles,omitempty"`
	RequiredActions []string            `json:"requiredActions,omitempty"`
}

type Credential struct {
	Type           string `json:"type"`
	Value          string `json:"value"`
	CredentialData string `json:"credentialData,omitempty"`
	Temporary      bool   `json:"temporary"`
}

// Cấu trúc để map dữ liệu người dùng từ Keycloak (khi GET)
type KeycloakUser struct {
	ID               string              `json:"id"`
	Username         string              `json:"username"`
	FirstName        string              `json:"firstName,omitempty"`
	LastName         string              `json:"lastName,omitempty"`
	Email            string              `json:"email,omitempty"`
	Enabled          bool                `json:"enabled"`
	CreatedTimestamp int64               `json:"createdTimestamp"`
	Attributes       map[string][]string `json:"attributes,omitempty"`
	RequiredActions  []string            `json:"requiredActions,omitempty"`
	RealmRoles       []string            `json:"realmRoles,omitempty"`
	ClientRoles      map[string][]string `json:"clientRoles,omitempty"`
}

func main() {
	fmt.Println("=== OneAM to Keycloak User Migration Tool ===")
	fmt.Println("Bắt đầu quá trình migrate users từ OneAM sang Keycloak...")

	// 0. OneAM password keys đã được hardcode
	fmt.Println("✅ OneAM password keys loaded")

	// 1. Test database connection first
	fmt.Println("\n--- Test OneAM Database Connection ---")
	fmt.Printf("Connecting to: %s:%d/%s\n", dbHost, dbPort, dbService)
	fmt.Printf("User: %s\n", dbUser)

	oneamUsers, err := getUsersFromOneAM()
	if err != nil {
		fmt.Printf("❌ Lỗi khi kết nối OneAM database: %v\n", err)
		fmt.Println("Hãy kiểm tra:")
		fmt.Println("- Oracle database có chạy không")
		fmt.Printf("- Network connection đến %s:%d\n", dbHost, dbPort)
		fmt.Println("- Oracle Instant Client đã cài đặt chưa")
		return
	}
	fmt.Printf("✅ Đã kết nối OneAM database thành công - Tìm thấy %d users\n", len(oneamUsers))

	// Show sample users
	if len(oneamUsers) > 0 {
		fmt.Println("\n--- Sample Users ---")
		for i, user := range oneamUsers {
			if i >= 3 { // Show only first 3 users
				break
			}
			fmt.Printf("User %d: %s (%s) - %s %s\n",
				i+1, user.Username, user.Email.String,
				user.FirstName.String, user.LastName.String)
		}
	}

	// 2. Test Keycloak connection
	fmt.Println("\n--- Test Keycloak Connection ---")
	fmt.Printf("Testing Keycloak URL: %s\n", keycloakBaseURL)
	fmt.Printf("Realm: %s\n", realmName)
	fmt.Printf("Client ID: %s\n", clientID)

	adminToken, err := getKeycloakAdminToken(clientID, clientSecret, realmName, keycloakBaseURL)
	if err != nil {
		fmt.Printf("❌ Không thể kết nối Keycloak: %v\n", err)
		fmt.Println("Hãy kiểm tra:")
		fmt.Println("- Keycloak server có chạy không")
		fmt.Println("- URL: " + keycloakBaseURL)
		fmt.Println("- Client ID và Secret có đúng không")
		fmt.Println("- Service Account có được enable không")
		return
	}
	fmt.Println("✅ Đã kết nối Keycloak thành công")

	// 3. Migrate users sang Keycloak
	fmt.Println("\n--- Bắt đầu migrate users sang Keycloak ---")
	successCount := 0
	failedCount := 0
	skippedCount := 0

	// Full migration - migrate tất cả users
	testUsers := oneamUsers
	fmt.Printf("🚀 Full migration: Migrate tất cả %d users\n", len(testUsers))

	for i, user := range testUsers {
		fmt.Printf("\n[%d/%d] Đang xử lý user: %s (%s)\n", i+1, len(testUsers), user.Username, user.Email.String)

		// Kiểm tra user đã tồn tại chưa
		existingUser, err := getUserFromKeycloak(user.Username, adminToken)
		if err == nil && existingUser != nil {
			fmt.Printf("⚠️  User %s đã tồn tại trong Keycloak, bỏ qua\n", user.Username)
			skippedCount++
			continue
		}

		// Tạo user mới
		if createUserInKeycloak(user, adminToken) {
			fmt.Printf("✅ Đã tạo user thành công: %s\n", user.Username)
			successCount++
		} else {
			fmt.Printf("❌ Thất bại khi tạo user: %s\n", user.Username)
			failedCount++
		}

		// Delay để tránh rate limit
		time.Sleep(100 * time.Millisecond)
	}

	// Báo cáo kết quả
	fmt.Println("\n=== KẾT QUẢ MIGRATION ===")
	fmt.Printf("✅ Thành công: %d\n", successCount)
	fmt.Printf("⚠️  Đã tồn tại (bỏ qua): %d\n", skippedCount)
	fmt.Printf("❌ Thất bại: %d\n", failedCount)
	fmt.Printf("Tỷ lệ thành công: %.2f%%\n", float64(successCount)/float64(len(testUsers))*100)

	/*
		// 3. Migrate users sang Keycloak
		fmt.Println("\n--- Bắt đầu migrate users sang Keycloak ---")
		successCount := 0
		failedCount := 0
		skippedCount := 0

		for i, user := range oneamUsers {
			fmt.Printf("\n[%d/%d] Đang xử lý user: %s (%s)\n", i+1, len(oneamUsers), user.Username, user.Email.String)

			// Kiểm tra user đã tồn tại chưa
			existingUser, err := getUserFromKeycloak(user.Username, adminToken)
			if err == nil && existingUser != nil {
				fmt.Printf("⚠️  User %s đã tồn tại trong Keycloak, bỏ qua\n", user.Username)
				skippedCount++
				continue
			}

			// Tạo user mới
			if createUserInKeycloak(user, adminToken) {
				fmt.Printf("✅ Đã tạo user thành công: %s\n", user.Username)
				successCount++
			} else {
				fmt.Printf("❌ Thất bại khi tạo user: %s\n", user.Username)
				failedCount++
			}

			// Delay để tránh rate limit
			time.Sleep(100 * time.Millisecond)
		}

		// Báo cáo kết quả
		fmt.Println("\n=== KẾT QUẢ MIGRATION ===")
		fmt.Printf("✅ Thành công: %d\n", successCount)
		fmt.Printf("⚠️  Đã tồn tại (bỏ qua): %d\n", skippedCount)
		fmt.Printf("❌ Thất bại: %d\n", failedCount)
		fmt.Printf("Tỷ lệ thành công: %.2f%%\n", float64(successCount)/float64(len(oneamUsers))*100)
	*/
}

// Lấy admin token từ Keycloak
func getKeycloakAdminToken(clientID, clientSecret, realm, baseURL string) (string, error) {
	url := fmt.Sprintf("%s/realms/%s/protocol/openid-connect/token", baseURL, realm)
	fmt.Printf("🔍 Keycloak Token URL: %s\n", url)

	// Keycloak yêu cầu form-encoded data, không phải JSON
	data := fmt.Sprintf("grant_type=client_credentials&client_id=%s&client_secret=%s",
		clientID, clientSecret)
	fmt.Printf("🔍 Request payload: %s\n", data)

	resp, err := http.Post(url, "application/x-www-form-urlencoded", bytes.NewBufferString(data))
	if err != nil {
		return "", fmt.Errorf("HTTP request failed: %v", err)
	}
	defer resp.Body.Close()

	body, _ := ioutil.ReadAll(resp.Body)
	fmt.Printf("🔍 Response status: %d\n", resp.StatusCode)
	fmt.Printf("🔍 Response body: %s\n", string(body))

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}

	var result map[string]interface{}
	json.Unmarshal(body, &result)

	if token, ok := result["access_token"].(string); ok {
		return token, nil
	}

	return "", fmt.Errorf("không thể lấy access token từ response")
}

// Lấy users từ OneAM database
func getUsersFromOneAM() ([]OneAMUser, error) {
	// Kết nối Oracle database
	dsn := fmt.Sprintf("user=%s password=%s connectString=%s:%d/%s",
		dbUser, dbPassword, dbHost, dbPort, dbService)

	db, err := sql.Open("godror", dsn)
	if err != nil {
		return nil, fmt.Errorf("không thể kết nối Oracle: %v", err)
	}
	defer db.Close()

	// Test connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("không thể ping Oracle: %v", err)
	}
	fmt.Printf("✅ Database ping successful\n")

	// Test query đơn giản trước
	fmt.Printf("🔍 Testing simple query first...\n")
	testQuery := "SELECT COUNT(*) FROM ONEAM.TB_USER"
	var totalCount int
	err = db.QueryRow(testQuery).Scan(&totalCount)
	if err != nil {
		fmt.Printf("❌ Error counting users: %v\n", err)
	} else {
		fmt.Printf("📊 Total users in TB_USER: %d\n", totalCount)
	}

	// Query users từ OneAM - TEST CHỈ <EMAIL>
	query := fmt.Sprintf(`
		SELECT %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
		FROM ONEAM.%s 
		WHERE %s = '<EMAIL>'
		ORDER BY %s`,
		colUserID, colUsername, colPassword, colFirstName, colLastName,
		colEmail, colMobile, colCountryCode, colProvince, colDistrict,
		colAddress1, colAddress2, colStatus, colCreateDate, colLastChange,
		colLastLogin, colAuthLevel, colValidateEmail, colValidateMobile,
		dbTable, colEmail, colCreateDate)

	fmt.Printf("🔍 Executing query: %s\n", query)

	rows, err := db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("lỗi query OneAM: %v", err)
	}
	defer rows.Close()

	fmt.Printf("✅ Query executed successfully\n")

	var users []OneAMUser
	rowCount := 0
	for rows.Next() {
		rowCount++
		var user OneAMUser
		err := rows.Scan(
			&user.UserID, &user.Username, &user.Password,
			&user.FirstName, &user.LastName, &user.Email, &user.Mobile,
			&user.CountryCode, &user.Province, &user.District,
			&user.Address1, &user.Address2, &user.Status,
			&user.CreateDate, &user.LastChange, &user.LastLogin,
			&user.AuthLevel, &user.ValidateEmail, &user.ValidateMobile,
		)
		if err != nil {
			fmt.Printf("Lỗi scan row: %v\n", err)
			continue
		}
		users = append(users, user)
	}

	fmt.Printf("📊 Processed %d rows, found %d valid users\n", rowCount, len(users))
	return users, nil
}

// Kiểm tra user đã tồn tại trong Keycloak
func getUserFromKeycloak(username, adminToken string) (*KeycloakUser, error) {
	url := fmt.Sprintf("%s/admin/realms/%s/users?username=%s", keycloakBaseURL, realmName, username)

	req, _ := http.NewRequest("GET", url, nil)
	req.Header.Set("Authorization", "Bearer "+adminToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("HTTP %d", resp.StatusCode)
	}

	body, _ := ioutil.ReadAll(resp.Body)
	var users []KeycloakUser
	json.Unmarshal(body, &users)

	if len(users) > 0 {
		return &users[0], nil
	}

	return nil, nil
}

// Tạo user trong Keycloak
func createUserInKeycloak(user OneAMUser, adminToken string) bool {
	url := fmt.Sprintf("%s/admin/realms/%s/users", keycloakBaseURL, realmName)

	// Chuẩn bị attributes
	attributes := make(map[string][]string)
	if user.Mobile.Valid {
		attributes["mobile"] = []string{user.Mobile.String}
	}
	if user.CountryCode.Valid {
		attributes["countryCode"] = []string{user.CountryCode.String}
	}
	if user.Province.Valid {
		attributes["province"] = []string{user.Province.String}
	}
	if user.District.Valid {
		attributes["district"] = []string{user.District.String}
	}
	if user.Address1.Valid {
		attributes["address1"] = []string{user.Address1.String}
	}
	if user.Address2.Valid {
		attributes["address2"] = []string{user.Address2.String}
	}
	if user.Status.Valid {
		attributes["oneam_status"] = []string{user.Status.String}
	}
	if user.AuthLevel.Valid {
		attributes["oneam_auth_level"] = []string{fmt.Sprintf("%d", user.AuthLevel.Int64)}
	}
	if user.ValidateEmail.Valid {
		attributes["oneam_validate_email"] = []string{user.ValidateEmail.Time.Format("2006-01-02 15:04:05")}
	}
	if user.ValidateMobile.Valid {
		attributes["oneam_validate_mobile"] = []string{user.ValidateMobile.Time.Format("2006-01-02 15:04:05")}
	}
	if user.LastLogin.Valid {
		attributes["oneam_last_login"] = []string{user.LastLogin.Time.Format("2006-01-02 15:04:05")}
	}

	// Thêm OneAM user ID để tracking
	attributes["oneam_user_id"] = []string{user.UserID}

	// Xác định user có enabled hay không dựa trên status
	enabled := false
	if user.Status.Valid {
		// A = Active → enabled = true
		// D hoặc null = Disabled → enabled = false
		enabled = user.Status.String == "A"
	}
	fmt.Printf("Debug - User Status: %s, Enabled: %t\n", user.Status.String, enabled)

	// Chuẩn bị payload
	payload := KeycloakUserPayload{
		Username:        user.Username,
		Email:           user.Email.String,
		FirstName:       user.FirstName.String,
		LastName:        user.LastName.String,
		Enabled:         enabled,
		EmailVerified:   true, // User từ OneAM đã verify email rồi
		Attributes:      attributes,
		RequiredActions: []string{}, // Không yêu cầu action nào - user đã verify trong OneAM
	}

	// Thêm password vào Keycloak credentials (như invoice)
	if user.Password.Valid && user.Password.String != "" {
		payload.Credentials = []Credential{
			{
				Type:      "password",
				Value:     user.Password.String, // OneAM password đã encode 2 lần
				Temporary: false,
			},
		}
		fmt.Printf("Debug - OneAM Password: %s\n", user.Password.String)
	}

	jsonData, _ := json.Marshal(payload)
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	req.Header.Set("Authorization", "Bearer "+adminToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("Lỗi HTTP request: %v\n", err)
		return false
	}
	defer resp.Body.Close()

	if resp.StatusCode == 201 {
		return true
	}

	// Đọc error message
	body, _ := ioutil.ReadAll(resp.Body)
	fmt.Printf("Keycloak error (HTTP %d): %s\n", resp.StatusCode, string(body))
	return false
}

// --- OneAM Password Encoding Functions ---

// hmacSHA256ToHex implements HMAC-SHA256 và trả về hex string
// Key phải là hex string và sẽ được decode thành bytes (như OneAM)
func hmacSHA256ToHex(hexKey, data string) string {
	// Decode hex key to bytes (như OneAM làm)
	keyBytes, err := hex.DecodeString(hexKey)
	if err != nil {
		fmt.Printf("Error decoding hex key: %v\n", err)
		return ""
	}

	h := hmac.New(sha256.New, keyBytes)
	h.Write([]byte(data))
	// Chuyển sang chữ HOA để khớp với OneAM
	return strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
}

// encodePasswordOneAM implements OneAM password encoding logic
// Level 1: HMAC-SHA256(key1, password)
// Level 2: HMAC-SHA256(key2, result_from_level_1)
func encodePasswordOneAM(password string, level int) string {
	if level == 1 {
		return hmacSHA256ToHex(oneamPasswordKey1, password)
	}

	if level == 12 {
		// Level 12: Apply key1 first, then key2
		step1 := hmacSHA256ToHex(oneamPasswordKey1, password)
		return hmacSHA256ToHex(oneamPasswordKey2, step1)
	}

	if level == 2 {
		// Level 2: Apply key2 directly (assuming password already has key1 applied)
		return hmacSHA256ToHex(oneamPasswordKey2, password)
	}

	return ""
}

// encodePasswordForLogin encodes password for login verification
// This simulates what OneAM does when user logs in
func encodePasswordForLogin(plainPassword string) string {
	// OneAM login process: level 2 encoding
	return encodePasswordOneAM(plainPassword, 2)
}

// OneAM password keys đã được hardcode, không cần load từ environment

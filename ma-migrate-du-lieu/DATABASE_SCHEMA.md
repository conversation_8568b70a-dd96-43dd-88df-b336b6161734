# OneAM Database Schema Documentation

## 📋 Tổng quan

OneAM sử dụng Oracle database với schema `ONEAM` và bảng chính `ONEAM.TB_USER` để lưu trữ thông tin người dùng.

## 🗄️ Bảng ONEAM.TB_USER

### C<PERSON>u trúc bảng

| Column Name | Data Type | Description | Keycloak Mapping |
|-------------|-----------|-------------|------------------|
| `N_ID` | NUMBER | Primary key (auto-generated) | - |
| `S_ID` | VARCHAR2 | User ID (UUID-like) | `attributes.oneam_user_id` |
| `S_PASSWORD` | VARCHAR2 | Hashed password | `credentials[0].value` |
| `S_EMAIL` | VARCHAR2 | Email address | `username` + `email` |
| `S_MOBILE` | VARCHAR2 | Mobile phone number | `attributes.mobile` |
| `S_FIRST_NAME` | VARCHAR2 | First name | `firstName` |
| `S_LAST_NAME` | VARCHAR2 | Last name | `lastName` |
| `S_COUNTRY_CODE` | VARCHAR2 | Country code | `attributes.countryCode` |
| `S_PROVINCE` | VARCHAR2 | Province | `attributes.province` |
| `S_DISTRICT` | VARCHAR2 | District | `attributes.district` |
| `S_ADDRESS1` | VARCHAR2 | Address line 1 | `attributes.address1` |
| `S_ADDRESS2` | VARCHAR2 | Address line 2 | `attributes.address2` |
| `S_STATUS` | VARCHAR2 | User status (ACTIVE/INACTIVE) | `enabled` (ACTIVE = true) |
| `D_CREATE` | DATE | Creation date | - |
| `D_UPDATE` | DATE | Last update date | - |
| `D_LAST_LOGIN` | DATE | Last login date | `attributes.oneam_last_login` |
| `N_LOGIN_FAIL` | NUMBER | Login failure count | - |
| `D_LOGIN_FAIL` | DATE | Last login failure date | - |
| `N_AUTH_LEVEL` | NUMBER | Authentication level | `attributes.oneam_auth_level` |
| `D_VALIDATE_EMAIL` | DATE | Email validation date | `attributes.oneam_validate_email` |
| `D_VALIDATE_MOBILE` | DATE | Mobile validation date | `attributes.oneam_validate_mobile` |
| `N_CHANGE_PASSWORD` | NUMBER | Password change flag | - |

### Sample Data

```sql
-- Sample user record
SELECT 
    S_ID,
    S_EMAIL,
    S_FIRST_NAME,
    S_LAST_NAME,
    S_MOBILE,
    S_STATUS,
    D_CREATE,
    N_AUTH_LEVEL
FROM TB_USER 
WHERE S_EMAIL = '<EMAIL>';
```

## 🔄 Migration Mapping

### OneAM → Keycloak User

```go
type OneAMUser struct {
    UserID        string         // S_ID
    Username      string         // S_EMAIL
    Password      sql.NullString // S_PASSWORD
    FirstName     sql.NullString // S_FIRST_NAME
    LastName      sql.NullString // S_LAST_NAME
    Email         sql.NullString // S_EMAIL
    Mobile        sql.NullString // S_MOBILE
    CountryCode   sql.NullString // S_COUNTRY_CODE
    Province      sql.NullString // S_PROVINCE
    District      sql.NullString // S_DISTRICT
    Address1      sql.NullString // S_ADDRESS1
    Address2      sql.NullString // S_ADDRESS2
    Status        sql.NullString // S_STATUS
    CreateDate    sql.NullTime   // D_CREATE
    LastChange    sql.NullTime   // D_UPDATE
    LastLogin     sql.NullTime   // D_LAST_LOGIN
    AuthLevel     sql.NullInt64  // N_AUTH_LEVEL
    ValidateEmail sql.NullTime   // D_VALIDATE_EMAIL
    ValidateMobile sql.NullTime  // D_VALIDATE_MOBILE
}
```

### Keycloak User Attributes

```json
{
  "username": "<EMAIL>",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "enabled": true,
  "attributes": {
    "mobile": ["+84123456789"],
    "countryCode": ["VN"],
    "province": ["Ho Chi Minh"],
    "district": ["District 1"],
    "address1": ["123 Main Street"],
    "address2": ["Apt 456"],
    "oneam_user_id": ["uuid-123-456"],
    "oneam_status": ["ACTIVE"],
    "oneam_auth_level": ["2"],
    "oneam_validate_email": ["2024-01-15 10:30:00"],
    "oneam_validate_mobile": ["2024-01-15 10:35:00"],
    "oneam_last_login": ["2024-01-20 14:20:00"]
  },
  "requiredActions": ["VERIFY_EMAIL", "UPDATE_PASSWORD"]
}
```

## 🔍 Query Examples

### Get all active users
```sql
SELECT * FROM TB_USER 
WHERE S_STATUS = 'ACTIVE' 
AND S_EMAIL IS NOT NULL 
AND S_EMAIL != '';
```

### Get users with validation info
```sql
SELECT 
    S_ID,
    S_EMAIL,
    S_FIRST_NAME,
    S_LAST_NAME,
    S_STATUS,
    D_VALIDATE_EMAIL,
    D_VALIDATE_MOBILE,
    N_AUTH_LEVEL
FROM TB_USER 
WHERE S_EMAIL IS NOT NULL;
```

### Count users by status
```sql
SELECT 
    S_STATUS,
    COUNT(*) as USER_COUNT
FROM TB_USER 
GROUP BY S_STATUS;
```

## 🛡️ Security Considerations

### Password Handling
- **OneAM**: Stores hashed passwords in `S_PASSWORD`
- **Keycloak**: Migrated as temporary credentials
- **User Action**: Must change password on first login

### Status Mapping
- **OneAM ACTIVE** → **Keycloak enabled: true**
- **OneAM INACTIVE** → **Keycloak enabled: false**
- **OneAM null/empty** → **Keycloak enabled: true** (default)

### Validation Status
- **Email Validated**: `D_VALIDATE_EMAIL IS NOT NULL`
- **Mobile Validated**: `D_VALIDATE_MOBILE IS NOT NULL`
- **Keycloak**: Set `VERIFY_EMAIL` required action if not validated

## 📊 Migration Statistics

### Expected Data Volume
- **Total Users**: ~10,000+ (estimated)
- **Active Users**: ~8,000+ (estimated)
- **Users with Email**: ~9,500+ (estimated)
- **Users with Mobile**: ~9,000+ (estimated)

### Migration Performance
- **Batch Size**: 100 users per batch
- **Processing Time**: ~1 minute per 1000 users
- **Total Estimated Time**: ~10-15 minutes for full migration

## 🔧 Troubleshooting

### Common Issues

1. **Connection Failed**
   ```bash
   # Test Oracle connection
   sqlplus oneam/oneam@10.38.130.31:1112/orcl
   ```

2. **Empty Results**
   ```sql
   -- Check if users exist
   SELECT COUNT(*) FROM TB_USER;
   SELECT COUNT(*) FROM TB_USER WHERE S_EMAIL IS NOT NULL;
   ```

3. **Invalid Data**
   ```sql
   -- Check for invalid emails
   SELECT S_EMAIL FROM TB_USER 
   WHERE S_EMAIL IS NOT NULL 
   AND S_EMAIL NOT LIKE '%@%';
   ```

### Validation Queries

```sql
-- Check data quality
SELECT 
    COUNT(*) as TOTAL_USERS,
    COUNT(S_EMAIL) as USERS_WITH_EMAIL,
    COUNT(S_MOBILE) as USERS_WITH_MOBILE,
    COUNT(CASE WHEN S_STATUS = 'ACTIVE' THEN 1 END) as ACTIVE_USERS
FROM TB_USER;
```

## 📈 Post-Migration Verification

### Keycloak Admin Console Checks
1. **User Count**: Verify total user count matches
2. **Active Users**: Check enabled users
3. **Attributes**: Verify custom attributes are set
4. **Required Actions**: Check VERIFY_EMAIL, UPDATE_PASSWORD

### Sample Verification Queries
```sql
-- Get migration statistics
SELECT 
    S_STATUS,
    COUNT(*) as COUNT,
    COUNT(CASE WHEN D_VALIDATE_EMAIL IS NOT NULL THEN 1 END) as EMAIL_VALIDATED,
    COUNT(CASE WHEN D_VALIDATE_MOBILE IS NOT NULL THEN 1 END) as MOBILE_VALIDATED
FROM TB_USER 
WHERE S_EMAIL IS NOT NULL
GROUP BY S_STATUS;
```

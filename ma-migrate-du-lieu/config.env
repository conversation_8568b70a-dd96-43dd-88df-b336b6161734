# OneAM to Keycloak Migration Configuration
# Update these values according to your environment

# Keycloak Configuration
KE<PERSON>CLOAK_BASE_URL=https://dev3-secure.onepay.vn/auth
KEYCLOAK_REALM=merchant-portal
KEYCLOAK_CLIENT_ID=ma-migration-tool
KEYCLOAK_CLIENT_SECRET=your-client-secret-here

# OneAM Oracle Database Configuration
ONEAM_DB_HOST=************
ONEAM_DB_PORT=1112
ONEAM_DB_USER=oneam
ONEAM_DB_PASSWORD=oneam
ONEAM_DB_SERVICE=orcl
ONEAM_DB_TABLE=TB_USER

# Migration Settings
MIGRATION_BATCH_SIZE=100
MIGRATION_DELAY_MS=100
MIGRATION_RETRY_COUNT=3
MIGRATION_DRY_RUN=false

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/migration.log
BACKUP_DIR=./backups

# Email Configuration (for notifications)
NOTIFICATION_EMAIL=<EMAIL>
SMTP_HOST=smtp.onepay.vn
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password

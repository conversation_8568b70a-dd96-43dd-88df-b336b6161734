# Keycloak Data Structure After Migration

## 📊 **Tổng quan dữ liệu sau migration**

Sau khi migrate từ OneAM sang Keycloak, dữ liệu sẽ được lưu trữ trong các bảng sau:

## 🗄️ **1. User Entity (KC_USER)**

### **Basic User Information**
| Field | Source | Description |
|-------|--------|-------------|
| `id` | Auto-generated | Keycloak user ID |
| `username` | `S_ID` | OneAM User ID (UUID-like) |
| `email` | `S_EMAIL` | Email address |
| `first_name` | `S_FIRST_NAME` | First name |
| `last_name` | `S_LAST_NAME` | Last name |
| `enabled` | `S_STATUS` | true if ACTIVE, false otherwise |
| `email_verified` | `false` | User cần verify email |
| `created_timestamp` | Current time | Migration timestamp |

### **Example User Record**
```sql
INSERT INTO user_entity (
    id, username, email, first_name, last_name, 
    enabled, email_verified, created_timestamp, realm_id
) VALUES (
    'uuid-123', 'oneam-user-456', '<EMAIL>', 
    'John', 'Doe', true, false, NOW(), 'merchant-portal'
);
```

## 🏷️ **2. User Attributes (KC_USER_ATTRIBUTE)**

### **OneAM Specific Attributes**
| Attribute Name | Source Column | Value Example |
|----------------|---------------|---------------|
| `oneam_user_id` | `S_ID` | `oneam-user-456` |
| `mobile` | `S_MOBILE` | `+84901234567` |
| `countryCode` | `S_COUNTRY_CODE` | `VN` |
| `province` | `S_PROVINCE` | `Ho Chi Minh` |
| `district` | `S_DISTRICT` | `District 1` |
| `address1` | `S_ADDRESS1` | `123 Main Street` |
| `address2` | `S_ADDRESS2` | `Apt 456` |
| `oneam_status` | `S_STATUS` | `ACTIVE` |
| `oneam_auth_level` | `N_AUTH_LEVEL` | `1` |
| `oneam_validate_email` | `D_VALIDATE_EMAIL` | `2024-01-15 10:30:00` |
| `oneam_validate_mobile` | `D_VALIDATE_MOBILE` | `2024-01-15 10:30:00` |
| `oneam_last_login` | `D_LAST_LOGIN` | `2024-01-15 10:30:00` |

### **Example Attributes**
```sql
-- OneAM User ID
INSERT INTO user_attribute (user_id, name, value) 
VALUES ('uuid-123', 'oneam_user_id', 'oneam-user-456');

-- Mobile
INSERT INTO user_attribute (user_id, name, value) 
VALUES ('uuid-123', 'mobile', '+84901234567');

-- Address
INSERT INTO user_attribute (user_id, name, value) 
VALUES ('uuid-123', 'province', 'Ho Chi Minh');
```

## 🔐 **3. User Credentials (KC_CREDENTIAL)**

### **Password Credential**
| Field | Value | Description |
|-------|-------|-------------|
| `type` | `password` | Credential type |
| `value` | Double-encoded | OneAM password + Keycloak encoding |
| `temporary` | `false` | Not temporary |
| `created_date` | Current time | Migration timestamp |

### **Example Credential**
```sql
INSERT INTO credential (
    id, user_id, type, value, temporary, created_date
) VALUES (
    'cred-uuid', 'user-uuid', 'password', 
    'double-encoded-password-hash', false, NOW()
);
```

## ⚡ **4. Required Actions (KC_USER_REQUIRED_ACTION)**

### **Default Required Actions**
| Action | Description |
|--------|-------------|
| `VERIFY_EMAIL` | User cần verify email address |
| `UPDATE_PASSWORD` | User cần đổi password lần đầu |

### **Example Required Actions**
```sql
INSERT INTO user_required_action (user_id, action, created_date)
VALUES ('user-uuid', 'VERIFY_EMAIL', NOW());

INSERT INTO user_required_action (user_id, action, created_date)
VALUES ('user-uuid', 'UPDATE_PASSWORD', NOW());
```

## 🔍 **5. Kiểm tra dữ liệu sau migration**

### **A. Qua Keycloak Admin Console**
1. Login vào Keycloak Admin Console
2. Chọn realm `merchant-portal`
3. Vào `Users` → Xem danh sách users
4. Click vào user → Xem `Attributes` tab
5. Xem `Credentials` tab

### **B. Qua SQL Query (Keycloak Database)**
```sql
-- Xem users đã migrate
SELECT u.username, u.email, u.enabled, u.created_timestamp
FROM user_entity u 
WHERE u.realm_id = 'merchant-portal'
ORDER BY u.created_timestamp DESC;

-- Xem OneAM attributes
SELECT u.username, ua.name, ua.value
FROM user_entity u
JOIN user_attribute ua ON u.id = ua.user_id
WHERE u.realm_id = 'merchant-portal'
AND ua.name LIKE 'oneam_%';
```

### **C. Qua REST API**
```bash
# Chạy script kiểm tra
./check-migrated-users.sh
```

## 📈 **6. Thống kê sau migration**

### **Expected Data Counts**
- **Users**: Số lượng users từ OneAM
- **Attributes**: ~10 attributes per user
- **Credentials**: 1 password credential per user
- **Required Actions**: 2 actions per user

### **Data Quality Checks**
- ✅ All users có `oneam_user_id`
- ✅ All users có password credential
- ✅ All users có required actions
- ✅ Enabled status match OneAM status
- ✅ Attributes được map đúng

## 🚨 **7. Troubleshooting**

### **Common Issues**
1. **Missing OneAM attributes**: Check migration tool logs
2. **Password not working**: Check double encoding
3. **User not enabled**: Check OneAM status mapping
4. **Missing required actions**: Check migration payload

### **Debug Commands**
```bash
# Check migration logs
tail -f migration.log

# Check Keycloak logs
tail -f /opt/keycloak-26.3.4/logs/keycloak.log

# Test user login
curl -X POST "${KEYCLOAK_URL}/realms/${REALM}/protocol/openid-connect/token" \
  -d "username=${USERNAME}&password=${PASSWORD}&grant_type=password"
```

## 📋 **8. Migration Checklist**

- [ ] OneAM keys configured
- [ ] Database connection working
- [ ] Keycloak admin token obtained
- [ ] Test migration with 1 user
- [ ] Verify user attributes
- [ ] Test login functionality
- [ ] Full migration completed
- [ ] Data quality verified
- [ ] User acceptance testing

#!/bin/bash

# Script to enable MA_UPDATE_PASSWORD required action in Keycloak

echo "=== Enabling MA_UPDATE_PASSWORD Required Action ==="

# Get admin token
echo "Getting admin token..."
TOKEN_RESPONSE=$(curl -s -X POST \
  "http://localhost:8080/auth-ma/realms/master/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin" \
  -d "password=admin" \
  -d "grant_type=password" \
  -d "client_id=admin-cli")

echo "Token response: $TOKEN_RESPONSE"

ADMIN_TOKEN=$(echo "$TOKEN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$ADMIN_TOKEN" ]; then
    echo "Failed to get admin token"
    exit 1
fi

echo "Admin token obtained successfully"

# Get current required actions
echo "Getting current required actions..."
curl -s -X GET \
  "http://localhost:8080/auth-ma/admin/realms/merchant-portal/authentication/required-actions" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json"

# Enable MA_UPDATE_PASSWORD action
echo "Enabling MA_UPDATE_PASSWORD action..."
curl -s -X PUT \
  "http://localhost:8080/auth-ma/admin/realms/merchant-portal/authentication/required-actions/MA_UPDATE_PASSWORD" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "alias": "MA_UPDATE_PASSWORD",
    "name": "MA Update Reset Password Form",
    "providerId": "MA_UPDATE_PASSWORD",
    "enabled": true,
    "defaultAction": false,
    "priority": 10,
    "config": {}
  }'

echo "MA_UPDATE_PASSWORD action enabled"

# Verify the action is enabled
echo "Verifying MA_UPDATE_PASSWORD action..."
curl -s -X GET \
  "http://localhost:8080/auth-ma/admin/realms/merchant-portal/authentication/required-actions/MA_UPDATE_PASSWORD" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json"

echo "=== Done ==="

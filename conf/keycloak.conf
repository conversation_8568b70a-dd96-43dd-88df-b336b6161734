# ------------------------------
# Hostname configuration
# ------------------------------
hostname=localhost
hostname-strict=false
hostname-strict-https=false
proxy=edge
http-relative-path=/auth-ma


# ------------------------------
# Database configuration
# ------------------------------
db=postgres
db-url=***********************************************************************
db-username=postgres
db-password=4n8c8f5t

# ------------------------------
# HTTP configuration
# ------------------------------
http-enabled=true
http-port=8080

# ------------------------------
# Logging
# ------------------------------
log-level=INFO


# ------------------------------
# Development mode
# ------------------------------
development=true


# Disable theme cache (for live theme development)
spi-theme-cache-themes=false
spi-theme-cache-templates=false
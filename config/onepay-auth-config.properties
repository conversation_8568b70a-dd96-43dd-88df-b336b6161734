# OnePay Keycloak Authentication Configuration
# This file contains configuration for the custom authentication providers

# OTP Configuration
otp.length=6
otp.ttl.seconds=300
otp.resend.interval.seconds=60
otp.max.attempts=3

# Device Alert Configuration
device.alert.enabled=true

# Cookie Configuration (Theme-specific)
cookie.name.invoice=op_td_i
cookie.name.invoice20=op_td_i20
cookie.name=op_td
cookie.max.age.days=90
cookie.path=/
cookie.secure=true
cookie.http.only=true
cookie.same.site=Lax
cookie.max.per.user=5

# Database Configuration
database.url=******************************************************
database.user=postgres
database.password=4n8c8f5t

# Email Configuration
email.smtp.host=smtp.onepay.vn
email.smtp.port=587
email.smtp.username=<EMAIL>
email.smtp.password=your_email_password
email.smtp.auth=true
email.smtp.starttls.enable=true

# SMS Configuration (if needed)
sms.provider.url=https://api.sms-provider.com
sms.provider.token=your_sms_token

# Security Configuration
security.require.https=true
security.session.timeout=1800
security.max.failed.attempts=5
security.lockout.duration=300

# Logging Configuration
logging.level.com.onepay.keycloak.auth=DEBUG
logging.level.org.keycloak.authentication=DEBUG

-- Clean up conflicting required actions
-- DELETE FROM required_action_provider WHERE realm_id = (SELECT id FROM realm WHERE name = 'merchant-portal') 
-- AND alias IN ('ma-update-password', 'ma-update-password-form', 'onepay-update-password');

-- Remove required actions from users
-- DELETE FROM user_required_action WHERE required_action IN ('ma-update-password', 'ma-update-password-form', 'onepay-update-password');

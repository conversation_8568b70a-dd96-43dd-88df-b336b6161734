upstream mmvn_web {
    #ip_hash;
    server ************:8580 backup;
    server ************:8580 ;
    keepalive 32;
}

upstream mavn_web {
    #ip_hash;
    server ************:8780 ;
    server ************:8780 backup;
    keepalive 32;
}

upstream ma_web {
    server ************:8480;
    server ************:8480 backup;
    keepalive 32;
}

#upstream madm2_frontend {
#    server ************:8180;
#    server ************:8180 backup;
#    keepalive 32;
#}

upstream madm2-frontend-ssl {
    server 10-36-75-116.onepay.local:443 backup;
    server 10-36-75-117.onepay.local:443;
    keepalive 32;
}


upstream madm2-frontend-prod {
    server 10-36-75-116.onepay.local:8180;
    server 10-36-75-117.onepay.local:8180 backup;
    keepalive 32;
}

upstream madm2-frontend-prod1 {
    server 10-36-75-117.onepay.local:8180;
    #server 10-36-75-116.onepay.local:8180 backup;
    keepalive 32;
}

upstream madm2-frontend-stg {
    server 10-36-75-58.onepay.local:443;
    keepalive 32;
}

#ldp-config
upstream ldp-config-prod {
    server 10-36-75-116.onepay.local:443;
    keepalive 32;
}

upstream ldp-config-stg {
    server 10-36-75-117.onepay.local:443;
    keepalive 32;
}

#payment-link
upstream api-15772-15773 {
    server 10.36.157.72:443 ;
    server 10.36.157.73:443 backup;
    keepalive 32;
}

upstream api-15773-15772 {
    server 10.36.157.72:443 backup;
    server 10.36.157.73:443 ;
    keepalive 32;
}

upstream api-157172-stg72 {
    server 10.36.157.172:443;
    keepalive 32;
}

upstream ma_eventbus {
    server ************:8380;
    server ************:8380 backup;
    keepalive 32;
}

upstream ma-eventbus-ssl {
    server 10-36-75-116.onepay.local:443 backup;
    server 10-36-75-117.onepay.local:443;
    keepalive 32;
}

upstream ma-eventbus-stg {
    server 10-36-75-58.onepay.local:443;
    keepalive 32;
}

upstream onecomm_pay_api {
    server ************:8680;
    server ************:8680 backup;
    keepalive 32;
}

upstream accounts_web {
    server ************:80;
    server ************:80 backup;
    keepalive 32;
}

upstream openam_web {
    #server ************:8880 backup;
    server ************:8880 ;
    keepalive 32;
}

upstream onlinebooking {
    ip_hash;
    #server ************:8180 backup;
    server ************:8180;
    keepalive 32;
}

upstream portal_web {
    #ip_hash;
    server ************:80;
    server ************:80 backup;
    keepalive 32;
}

upstream smssp {
    server ************:80;
    server ************:80 backup;
    keepalive 32;
}

upstream oneshop_web {
    server ************:80 backup;
    server ************:80;
    keepalive 32;
}

upstream tsp_internet {
    server ************:80;
    server ************:80 backup;
    keepalive 32;
}

#upstream tspwebclient {
#    server ************:18380;
#    server ************:18380 backup;
#    keepalive 32;
#}

upstream vpcpay_api {
    server ************:80;
    server ************:80 backup;
    keepalive 32;
}

upstream payment_api_frontend {
    server ************:8681;
    server ************:8681 backup;
    keepalive 32;
}

upstream msp {
    server ************:80;
    server ************:80 backup;
    keepalive 32;
}

upstream iportal_service {
    server ************:80;
    server ************:80 backup;
    keepalive 32;
}

upstream iportal-service-ssl-prod {
    server 10-36-75-116.onepay.local:443;
    server 10-36-75-117.onepay.local:443 backup;
    keepalive 32;
}

upstream iportal-service-ssl-stg {
    server 10-36-75-116.onepay.local:443 backup;
    server 10-36-75-117.onepay.local:443;
    keepalive 32;
}

#geo $iportal_root {
    #default /usr/share/nginx/secure.opdev.vn/iportal/;
#}

map "$remote_addr;$http_cookie" $iportal_root {
    default /usr/share/nginx/secure.opdev.vn/iportal/;
    "~^(**************|**************|**********|**************|***********);.*env=stg" /usr/share/nginx/secure.opdev.vn/iportal_rc/;
    "~^(**********|*************);.*env=stg" ************:443;
}

# map "$remote_addr;$http_cookie" $miniapp_root {
#    default /usr/share/nginx/secure.opdev.vn/iportal/miniapp/;
# }

map "$remote_addr;$http_cookie" $iportal_service_ssl {
    default iportal-service-ssl-prod;
    "~^(**************|**************|**********|**************|**************|***********);.*env=stg" iportal-service-ssl-stg;
    "~^(**********|*************);.*env=stg" ************:443;
}

map "$remote_addr;$http_cookie" $merchant_monitor_mm {
    default /usr/share/nginx/secure.opdev.vn/merchant_monitor_mm_prod/;
    "~^(**************|**************|**********|**************);.*env=stg" /usr/share/nginx/secure.opdev.vn/merchant_monitor_mm_stg/;
    "~^(**********|*************);.*env=stg" ************:443;
}

map "$remote_addr;$http_cookie" $merchant_monitor_ss {
    default /usr/share/nginx/secure.opdev.vn/merchant_monitor_ss_prod/;
    "~^(**************|**************|**********|**************);.*env=stg" /usr/share/nginx/secure.opdev.vn/merchant_monitor_ss_stg/;
    "~^(**********|*************);.*env=stg" ************:443;
}

#ma web frontend

map "$remote_addr;$http_cookie" $madm2_frontend {
    "~^(**************|**************);.*env=stg"    madm2-frontend-stg;
    "~^(**************|**************);.*env=prod"   madm2-frontend-prod;
    "~^(**************|**************);.*env=prod1"  madm2-frontend-prod1;
    default madm2-frontend-prod;
}
map "$remote_addr;$http_cookie" $is_stg {
    "~^(**************|**************);.*env=stg"    1;
    default 0;
}
map "$remote_addr;$http_cookie" $is_prod1 {
    "~^(**************|**************);.*env=prod1"    1;
    default 0;
}

map "$remote_addr;$http_cookie" $ldp_config {
    default ldp-config-prod;
    "~^(**************);.*env=stg"    ldp-config-stg;
    "~^(**************);.*env=prod"   ldp-config-prod;
    "~^(**************);.*env=prod1"  ldp-config-prod;
}

map "$remote_addr;$http_cookie" $ma_ldp_config_root {
    default /usr/share/nginx/ma_ldp_config/;
    "~^(**************|**************|**********|**************);.*env=stg" /usr/share/nginx/ma_ldp_config_rc/;
}

#payment-link
map "$remote_addr;$http_cookie" $api_15772_15773_157172 {
    default api-15772-15773;
    "~^(**************|**********);.*env=prod1" api-15773-15772;
    "~^(**************|**********);.*env=prod" api-15773-15772;
    "~^(**************|**********);.*env=stg" api-157172-stg72;
}

#miniapp
map "$remote_addr;$http_cookie" $pci_14442_14443_14452 {
    default pci-14442-14443;
    "~^(**************|**********);.*env=prod1" pci-14443-14442;
    "~^(**************|**********);.*env=prod" pci-14443-14442;
    "~^(**************|**********);.*env=stg" pci-14452-stg42;
}

geo $env_root {
    default /usr/share/nginx/secure.opdev.vn/;
}

map "$remote_addr;$http_cookie" $bank_amount_root {
    default        /usr/share/nginx/secure.opdev.vn;
    "~^(**************|**********);.*env=stg" /usr/share/nginx/secure.opdev.vn_rc;
}

map "$remote_addr;$http_cookie" $ma_root {
    default /usr/share/nginx/ma.opdev.vn;
    "~^(**************|**************|**********|**************);.*env=stg" /usr/share/nginx/ma.opdev.vn_rc/;
}

upstream pci-16774-16775 {
    server ************:445;
    server ************:445 backup;
    keepalive 32;
}

upstream pci-14442-14443 {
    server ************:443;
    server ************:443 backup;
    keepalive 32;
}

upstream pci-14443-14442 {
    server ************:443 backup;
    server ************:443;
    keepalive 32;
}

upstream miniapp-14442-14443 {
    server ************:80;
    keepalive 32;
}

upstream pci-14452-stg42 {
    server ************:443;
    keepalive 32;
}

map $http_referer $http_referer_hostname {
        ~(^.*://[^?/]+.*\?).*$ $1;
}

#Ngam dinh goi bang IP
server {
    listen 443 ssl;
    #ssl           on;
    ssl_certificate /etc/nginx/conf.d/_.opdev.vn-2024.crt;
    ssl_certificate_key /etc/nginx/conf.d/_.opdev.vn-2024.key;

    #ssl_protocols  TLSv1 TLSv1.1 TLSv1.2;
    #ssl_protocols TLSv1.1  TLSv1.2;
    ssl_protocols TLSv1.2;
    ssl_ciphers HIGH:!aNULL:!eNULL:!PSK:!RC4:!MD5:!3DES;
    #ssl_ciphers "EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA:ECDHE-RSA-DES-CBC3-SHA:EDH-RSA-DES-CBC3-SHA:AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA256:AES128-SHA256:AES256-SHA:AES128-SHA:DES-CBC3-SHA:HIGH:!aNULL:!eNULL:!EXPORT:!DES:!MD5:!PSK:!RC4";
    ssl_prefer_server_ciphers on;

    proxy_set_header Host $host;
    proxy_read_timeout 2m;

    location /administrator {
        rewrite ^(.*)$ https://dev13-secure.opdev.vn$1;
        allow **************;
        #####deny all;
    }

    location / {
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
        gzip on;
        gzip_min_length 1000;
        gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css image/x-icon;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
    }
}

server {
    listen 443 ssl;
    server_name dev13-secure.opdev.vn;
    proxy_read_timeout 3m;
    #ssl                  on;
    ssl_certificate /etc/nginx/conf.d/_.opdev.vn-2024.crt;
    ssl_certificate_key /etc/nginx/conf.d/_.opdev.vn-2024.key;

    ssl_session_timeout 5m;

    #ssl_protocols  TLSv1 TLSv1.1 TLSv1.2;
    #ssl_protocols TLSv1.1 TLSv1.2;
    ssl_protocols TLSv1.2;
    ssl_ciphers HIGH:!aNULL:!eNULL:!PSK:!RC4:!MD5:!3DES;
    #ssl_ciphers "EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA:ECDHE-RSA-DES-CBC3-SHA:EDH-RSA-DES-CBC3-SHA:AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA256:AES128-SHA256:AES256-SHA:AES128-SHA:DES-CBC3-SHA:HIGH:!aNULL:!eNULL:!EXPORT:!DES:!MD5:!PSK:!RC4";
    ssl_prefer_server_ciphers on;

    ssl_dhparam /etc/nginx/conf.d/dhparams.pem;
    # Set HSTS to 365 days
    #add_header Strict-Transport-Security 'max-age=31536000; includeSubDomains';
    add_header Strict-Transport-Security 'max-age=31536000';
    #add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    #add_header Content-Security-Policy "default-src 'self';" always;
    #ssl_stapling on;
    #ssl_stapling_verify on;
    #resolver ******* ******* valid=300s;
    #resolver_timeout 10s;

    root /usr/share/nginx/secure.opdev.vn;

    proxy_http_version 1.1;
    proxy_set_header Connection "";

    proxy_set_header Accept-Encoding "";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    ### Most PHP, Python, Rails, Java App can use this header ###
    #proxy_set_header X-Forwarded-Proto https;##
    #This is better##
    #proxy_set_header        X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Proto https;
    add_header Front-End-Https on;

    ### By default we don't want to redirect it ####
    proxy_redirect off;
    client_max_body_size 100M;

    large_client_header_buffers 4 32k;

    access_log /var/log/nginx/access.log no_query;

    location = /favicon.ico {
        #alias /var/www/icons/favicon.ico;
    }

    error_page 404 /custom_404.html;
    location = /custom_404.html {
        root /usr/share/nginx/html;
        internal;
    }

# keycloak
    location /auth {

        proxy_set_header Host api.onepay.local;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_ssl_certificate /etc/nginx/conf.d/_.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/_.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;

        proxy_http_version 1.1;
        proxy_pass https://pci-14442-14443;
    }
    #---------------------------------------------------------------------
    # ************ servers
    #---------------------------------------------------------------------
    location /mavn {
        #if ( $remote_addr ~ "^(**************)$") {
        #    proxy_pass http://************:8780;
        #}
        #proxy_pass http://mavn_web;
        return 302 https://dev13-ma.opdev.vn;
    }

    location ~ ^/(mmvn|ma|ofd)($|/.*) {
        proxy_pass http://mmvn_web;
    }

    location ~ ^/onecomm-pay/Vpcdps.op$ {
        proxy_pass http://onecomm_pay_api;
    }

    location ~ ^/onecomm-pay/refund.op$ {
        proxy_pass http://onecomm_pay_api;
        include /etc/nginx/conf.d/allow_refund_ips;
        #####deny all;
    }

    location ~ ^/onecomm-pay/(verifycardapi|verifyauthapi).op$ {
        #if ( $remote_addr ~ "^(**************|*************|**************|*************)$") {
        if ( $remote_addr ~ "^(**************|**************|**************|*************)$") {
            #proxy_pass http://************:8680;
            proxy_pass http://localhost:8080;
        }
        proxy_pass http://onecomm_pay_api;
    }

    location /accounts {
        add_header Pragma "no-cache";
        add_header Cache-Control "no-store, no-cache, must-revalidate, post-check=0, pre-check=0";
        try_files $uri $uri/ /index.html =404;
        gzip on;
        gzip_min_length 1000;
        gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css image/x-icon image/svg+xml;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
    }

    location /accounts/api/v1 {
        proxy_set_header Host proxy.secure.opdev.vn;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        #rewrite /accounts/api/v1/(.*) /$1 break;
        if ( $remote_addr ~ "^(**************|**************)$") {
            proxy_pass http://************:80;
        }
        proxy_pass http://accounts_web;
    }

    #location /accounts/madm2/api/v1 {
    #    proxy_set_header        Host            proxy.secure.opdev.vn;
    #    proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
    #    proxy_set_header        X-Forwarded-Proto $scheme;
    #    rewrite /accounts/madm2(.*) /accounts$1 break;
    #    proxy_pass http://accounts_web;
    #}
    location /accounts/madm2 {
        rewrite /.* https://dev13-ma.opdev.vn/accounts/;
    }

    # Merchant Admin ===============================================================================
    location ~ ^/madm$ {
        #rewrite ^(.*)$ https://$server_name$1/;
        rewrite ^/madm(.*) https://dev13-ma.opdev.vn;
    }

    location /madm/ {
        rewrite ^/madm(.*) https://dev13-ma.opdev.vn;
        #gzip  on;
        #gzip_min_length 1000;
        #gzip_types    text/plain application/javascript application/x-javascript text/javascript text/xml text/css;
        #gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        #gzip_proxied any;
        #gzip_buffers 16 8k;
        #gzip_vary on;
        #expires modified +90d;
    }

    location ~ ^/madm/scripts/.*/app.js$ {
        proxy_pass http://ma_web;
        proxy_cache cache;
        proxy_cache_valid 200 1y;
        proxy_cache_valid 404 1m;
        gzip on;
        gzip_min_length 1000;
        gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css image/x-icon image/svg+xml;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
    }

    location ~ ^/madm/api/v1/file/(.+)/download$ {
        rewrite ^/madm/api/v1(/.*) $1 break;
        proxy_pass http://ma_web;
        proxy_cache cache;
        proxy_cache_valid 200 7d;
        proxy_cache_use_stale error timeout invalid_header updating
        http_500 http_502 http_503 http_504;
    }

    location ~ ^/madm/api/v1/app/data {
        rewrite ^/madm/api/v1/(.*) /madm/$1 break;
        expires 0;
    }

    location ~ ^/madm/(login|api/v1/.*)$ {
        rewrite ^/madm(/api/v1)?(/.*) $2 break;
        expires 0;
        proxy_pass http://ma_web;
    }

    #Merchant Management mm ==========================================================================
    location ~ ^/mm$ {
        rewrite ^(.*)$ https://$server_name$1/;
    }

    location /mm/ {
        gzip on;
        gzip_min_length 1000;
        gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css image/x-icon image/svg+xml;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
        expires modified +90d;
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        #add_header Content-Security-Policy "default-src 'self'; font-src ;img-src  data:; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header 'Referrer-Policy' 'origin';
        add_header Permissions-Policy "geolocation=(),midi=(),sync-xhr=(),microphone=(),camera=(),magnetometer=(),gyroscope=(),fullscreen=(self),payment=()";
    }

    location ~ ^/mm/scripts/.*/app.js$ {
        #proxy_pass http://ma_web;
        proxy_pass http://************:8480;
        proxy_cache cache;
        proxy_cache_valid 200 1y;
        proxy_cache_valid 404 1m;
        gzip on;
        gzip_min_length 1000;
        gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css image/x-icon image/svg+xml;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
    }

    location ~ ^/mm/api/v1/file/(.+)/download$ {
        rewrite ^/mm/api/v1(/.*) $1 break;
        #proxy_pass http://ma_web;
        proxy_pass http://************:8480;
        proxy_cache cache;
        proxy_cache_valid 200 7d;
        proxy_cache_use_stale error timeout invalid_header updating
        http_500 http_502 http_503 http_504;
    }

    location ~ ^/mm/api/v1/app/data {
        rewrite ^/mm/api/v1/(.*) /mm/$1 break;
        expires 0;
    }

    location ~ ^/mm/(login|api/v1/.*)?$ {
        access_log /var/log/nginx/access.log no_query;
        rewrite ^/mm(/api/v1)?(/.*) $2 break;
        expires 0;
        #proxy_pass http://ma_web;
        proxy_pass http://************:8480;
    }

    #madm2 backcompatible ==========================================================================
    location /madm2 {
        rewrite ^/madm2(.*) https://dev13-ma.opdev.vn$1;
        #gzip  on;
        #gzip_min_length 1000;
        #gzip_types    text/plain application/javascript application/x-javascript text/javascript text/xml text/css;
        #gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        #gzip_proxied any;
        #gzip_buffers 16 8k;
        #gzip_vary on;
        #alias  /usr/share/nginx/secure.opdev.vn/madm2;
        #try_files $uri @madm2_index;
    }

    #location @madm2_index {
    #    add_header Cache-Control no-cache;
    #    expires -1;
    #    try_files /madm2/index.html =404;
    #}

    #location /madm2/api/v1/ {
    #    gzip  on;
    #    gzip_min_length 1000;
    #    gzip_types    application/json;
    #    gzip_disable "MSIE [1-6]\.(?!.*SV1)";
    #    gzip_proxied any;
    #    gzip_buffers 16 8k;
    #    gzip_vary on;
    #    rewrite ^/madm2/api/v1/(.*) /$1 break;
    #    proxy_pass http://madm2_frontend;
    #}
    #location /madm2/login {
    #    expires 0;
    #    rewrite ^/madm2/(login) /$1 break;
    #    proxy_pass http://madm2_frontend;
    #}
    #location /madm2/api/v1/app/data {
    #    expires 0;
    #    rewrite ^/madm2/api/v1/app/(.*) /$1 break;
    #    root  /usr/share/nginx/secure.opdev.vn/madm2/assets;
    #}
    #location ~ ^/assets/(.*) {
    #    rewrite ^/assets/(.*) /madm2/assets/$1 break;
    #    root /usr/share/nginx/secure.opdev.vn/;
    #}
    location /eventbus {
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_read_timeout 60s;
        proxy_pass http://ma_eventbus;
    }

    #location /payment/ {
    #    #For static contents
    #    #root /usr/share/nginx/secure.opdev.vn;
    #}
    #location ~ "^/payment/(payments|authorizations)/[-_0-9a-zA-Z]+$" {
    #    proxy_http_version 1.1;
    #    proxy_set_header Upgrade $http_upgrade;
    #    proxy_set_header Connection "upgrade";
    #    proxy_read_timeout 60s;
    #    proxy_pass http://tspwebclient;
    #}
    #---------------------------------------------------------------------
    # ************/109 servers
    #---------------------------------------------------------------------
    #location ~ ^/(portal|mp|microtec|fraud)$  {
    location ~ ^/(portal|mp|microtec)$ {
        rewrite ^(/.*)$ $1/ redirect;
    }

    #location ~ ^/(portal|mp|microtec|fraud)/.*  {
    location ~ ^/(portal|mp|microtec)/.* {
        if ($remote_addr ~ "^(**************)$") {
            #proxy_set_header        Host            proxy.secure.opdev.vn;
            #proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            #proxy_set_header        X-Forwarded-Proto $scheme;
            proxy_pass http://************;
            ### force timeouts if one of backend is died ##
            #proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        }
        proxy_set_header Host proxy.secure.opdev.vn;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-Id $request_id;
        proxy_pass http://portal_web;
        ### force timeouts if one of backend is died ##
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    }

    location /sms/api/v1 {
        proxy_set_header Host proxy.secure.opdev.vn;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://smssp;
    }
    #---------------------------------------------------------------------
    # online booking ************
    #---------------------------------------------------------------------
    location ~ ^/administrator/?$ {
        rewrite ^(.+)$ https://$server_name/administrator/index.php;
        allow **************;
        #####deny all;
    }

    location ~ ^/(index.php|administrator/index.php|box.js)$ {
        #if ($blocked_ua) { return 403; }
        #if ( $remote_addr ~ "^(**************)$") {
        #    proxy_pass http://************:8180;
        #}
        proxy_pass http://onlinebooking;
        proxy_intercept_errors on;
        proxy_hide_header X-Powered-By;
    }
    #---------------------------------------------------------------------
    # topup ************
    #---------------------------------------------------------------------
    #OpenAM ---------------------------------------------------------
    location /openam {
        proxy_pass http://openam_web;
    }

    #location /oauth2 {
    #    rewrite ^/(.+) /openam/$1 break;
    #    proxy_pass http://openam_web;
    #}

    location /oauth2/access_token {
        rewrite ^/(.+)$ /oneshop/rest/v1/$1 break;
        proxy_set_header Host proxy.secure.opdev.vn;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://oneshop_web;
    }

    location /oneshop/rest/v1/ {
        proxy_set_header Host proxy.secure.opdev.vn;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://oneshop_web;
    }

    #location /tsp/api/v1/ {
    #    proxy_set_header        Host            proxy.secure.opdev.vn;
    #    proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
    #    proxy_set_header        X-Forwarded-Proto $scheme;
    #    proxy_pass http://tsp_internet;
    #    #if ( $remote_addr ~ "^(**************|************)$") {
    #    #    proxy_pass http://************:80;
    #    #}
    #    allow **************;
    #    #Adayroi
    #    allow ***************;
    #    allow **************;
    #    allow **************;
    #    allow **************;
    #    allow *************;
    #    #Mcdonalds
    #    allow ***************;
    #    allow ***************;
    #    allow ***************;
    #    allow ***************;
    #    allow ***************;
    #    #Thuan test
    #    allow *************;
    #    #####deny all;
    #}

    location /tsp/api/v2/ {
        proxy_ssl_certificate /etc/nginx/conf.d/_.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/_.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
        proxy_ssl_name pci.onepay.local;
        proxy_set_header Host pci.onepay.local;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection "";

        proxy_pass https://pci-16774-16775;

        #proxy_set_header Host proxy.secure.opdev.vn;
        #proxy_pass http://tsp_internet;
        #if ( $remote_addr ~ "^(**************)$") {
        #    #proxy_pass http://************:80;
        #    proxy_pass https://pci-16774-16775;
        #}
        allow **************;
        allow ************;
        #Adayroi
        allow **************;
        allow **************;
        allow **************;
        allow ***************;
        allow **************;
        allow **************;
        allow *************;
        #Mcdonalds
        allow ***************;
        allow ***************;
        allow ***************;
        allow ***************;
        allow ***************;
        #F8FIT
        allow ************;
        allow ************;
        allow *************;
        allow *************;
        allow *************;
        allow *************;
        allow *************;
        #LETSGO
        allow *************;
        allow **************;
        allow **************;
        allow **************;
        #Thuan test
        allow *************;
        #Nam test
        allow **************;
        #####deny all;
    }

    #Thanh toan tu kios CGV
    location /vpcpay/api/v1/payments {
        proxy_set_header Host proxy.secure.opdev.vn;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_pass http://vpcpay_api;
        #allow **************;
        ######deny all;
    }

    #Payment API
    location /payment/api/v1/ {
        proxy_pass http://payment_api_frontend;
    }

    location /msp/api/v1/ {
        proxy_set_header Host proxy.secure.opdev.vn;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://msp;
    }

    ################################################# PORTAL 2  ######################################
    location /portal2 {
        rewrite ^/portal2($|/.*) https://dev13-secure.opdev.vn/iportal($1);
    }

    location /env {
       root $env_root;
       try_files $uri $uri/ /env/index.html;
    }

    location /iportal {

        add_header X-XSS-Protection "1; mode=block";
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        #add_header Content-Security-Policy "default-src 'self'; font-src ;img-src  data:; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header 'Referrer-Policy' 'origin';
        add_header Permissions-Policy "geolocation=(),midi=(),sync-xhr=(),microphone=(),camera=(),magnetometer=(),gyroscope=(),fullscreen=(self),payment=()";

                #root $iportal_root;
        alias $iportal_root;
        try_files $uri$args $uri$args/ /iportal/index.html;
        #index index.html index.htm;
        #try_files $uri $uri/ /index.html;

        gzip on;
        gzip_min_length 1000;
        gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css image/x-icon image/svg+xml;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
    }

    location /iportal/merchantmonitorhung {
        alias $merchant_monitor_ss;
        try_files $uri$args $uri$args/ /iportal/merchantmonitorhung/index.html;
        gzip  on;
        gzip_min_length 1000;
        gzip_types    text/plain application/javascript application/x-javascript text/javascript text/xml text/css image/x-icon image/svg+xml;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
    }

    location ~ /iportal/merchantmonitorhung/api/v1/user-profile {
        rewrite ^/iportal/merchantmonitorhung/api/v1/user-profile(.*)$ /iportal/api/v1/user-profile$1 last;
                proxy_pass https://$iportal_service_ssl;
    }

    location ~ ^/iportal/merchantmonitorhung/api/v1/app/data/(.*)$ {
                rewrite ^/iportal/merchantmonitorhung/api/v1/app/data/(.*)$ /iportal/api/v1/app/data/$1 last;
        root $iportal_root/assets;
    }

    location ~ /iportal/merchantmonitorhung/api/v1 {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        expires 0;
        # ssl for call other server
        proxy_set_header Host api.onepay.local;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_ssl_certificate /etc/nginx/conf.d/_.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/_.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
                rewrite ^/iportal/merchantmonitorhung/api/v1/(.*)$ /iportal/api/v1/merchantmonitorhung/$1 break;
                proxy_pass https://$iportal_service_ssl;
    }

    #miniapp
    location /iportal/miniapp {
        access_log /var/log/nginx/access.log no_query;
        # rewrite ^/iportal/+miniapp(.*)$ /iportal/miniapp$1 permanent;
        # try_files $uri$args $uri$args/ /iportal/miniapp/index.html;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        expires 0;
        proxy_set_header Host api.onepay.local;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_ssl_certificate /etc/nginx/conf.d/_.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/_.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
        proxy_pass https://$pci_14442_14443_14452;
    }

    location ~ /iportal/miniapp/api/v1/user-profile {
        rewrite ^/iportal/miniapp/api/v1/user-profile(.*)$ /iportal/api/v1/user-profile$1 last;
                proxy_pass https://$iportal_service_ssl;
    }

    # location ~ ^/iportal/miniapp/api/v1/app/data/(.*)$ {
    #    rewrite ^/iportal/miniapp/api/v1/app/data/(.*)$ /iportal/api/v1/app/data/$1 last;
    #    root $miniapp_root/assets;
    # }

    location ~ /iportal/miniapp/api/v1 {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        expires 0;
        # ssl for call other server
        proxy_set_header Host api.onepay.local;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_ssl_certificate /etc/nginx/conf.d/_.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/_.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
                rewrite ^/iportal/miniapp/api/v1/(.*)$ /iportal/api/v1/miniapp/$1 break;
                proxy_pass https://$iportal_service_ssl;
    }

    location /iportal/merchantmonitorthanh {
        alias $merchant_monitor_mm;
        try_files $uri$args $uri$args/ /iportal/merchantmonitorthanh/index.html;
        gzip  on;
        gzip_min_length 1000;
        gzip_types    text/plain application/javascript application/x-javascript text/javascript text/xml text/css image/x-icon image/svg+xml;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
    }

    location ~ /iportal/merchantmonitorthanh/api/v1/user-profile {
        rewrite ^/iportal/merchantmonitorthanh/api/v1/user-profile(.*)$ /iportal/api/v1/user-profile$1 last;
                proxy_pass https://$iportal_service_ssl;
    }

    location ~ ^/iportal/merchantmonitorthanh/api/v1/app/data/(.*)$ {
        rewrite ^/iportal/merchantmonitorthanh/api/v1/app/data/(.*)$ /iportal/api/v1/app/data/$1 last;
        root $iportal_root/assets;
    }

    location ~ /iportal/merchantmonitorthanh/api/v1 {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        expires 0;
        # ssl for call other server
        proxy_set_header Host api.onepay.local;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_ssl_certificate /etc/nginx/conf.d/_.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/_.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
                rewrite ^/iportal/merchantmonitorthanh/api/v1/(.*)$ /iportal/api/v1/merchantmonitorthanh/$1 break;
                proxy_pass https://$iportal_service_ssl;
    }

    location ~ /iportal/api/v1/app/data {
        rewrite ^/iportal/api/v1/app/(.*) /$1 break;
        root $iportal_root/assets;
    }



   # location ~ ^/iportal/api/v1/miniapp/ {
        # Rewrite URL
    #    rewrite ^/iportal/api/v1/miniapp/(.*) /iportal/miniapp/api/v1/$1 last;
        # proxy_pass https://$pci_14442_14443_14452;
     #   proxy_pass https://iportal_service_ssl;
    # }

    location ~ ^/iportal/(login2|login|api/v1/.+|api/v2/.+)$ {
        #proxy_set_header        Host            proxy.secure.opdev.vn;
        #proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
        #proxy_set_header        X-Forwarded-Proto $scheme;
        #proxy_read_timeout 600;
        #proxy_connect_timeout 600;
        #proxy_send_timeout 600;
        #expires 0;

        ## for https
        #if ( $remote_addr ~ "^(************|**************|**************)$") {
        #    proxy_pass http://127.0.0.1:8080;
        #    break;
        #}

        ##rewrite ^/iportal(/.*)$ $1 break;
        #proxy_pass http://iportal_service;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        expires 0;
        # ssl for call other server
        proxy_set_header Host api.onepay.local;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_ssl_certificate /etc/nginx/conf.d/_.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/_.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;

        #if ( $remote_addr ~ "^(**************)$") {
        #                       proxy_pass https://iportal-service-ssl-stg;
        #    break;
        #}
        #if ( $remote_addr ~ "^(************|**************)$") {
        #    proxy_pass https://10-36-75-117.onepay.local:443;
        #    break;
        #}
        proxy_pass https://$iportal_service_ssl;
    }

    location /iportal-mobile {
        alias /usr/share/nginx/secure.opdev.vn/iportal-mobile/;
        try_files $uri$args $uri$args/ /iportal-mobile/index.html;
        gzip on;
        gzip_min_length 1000;
        gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css image/x-icon image/svg+xml;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
    }

    location ~ /iportal-mobile/api/v1/app/data {
        rewrite ^/iportal-mobile/api/v1/app/(.*) /$1 break;
        root /usr/share/nginx/secure.opdev.vn/iportal-mobile/assets;
    }
    location ~ ^/iportal-mobile/(login|api/v1/.+)$ {
        #proxy_set_header        Host            proxy.secure.opdev.vn;
        #proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
        #proxy_set_header        X-Forwarded-Proto $scheme;
        #expires 0;
        ## for https
        #if ( $remote_addr ~ "^(************|**************|**************)$") {
        #    rewrite ^/iportal-mobile/(.*)$ /iportal/$1 break;
        #    proxy_pass http://127.0.0.1:8080;
        #    break;
        #}

        #rewrite ^/iportal-mobile/(.*)$ /iportal/$1 break;
        #proxy_pass http://iportal_service;

        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        expires 0;
        # ssl for call other server
        proxy_set_header Host api.onepay.local;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_ssl_certificate /etc/nginx/conf.d/_.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/_.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;

        #if ( $remote_addr ~ "^(************|**************|**************)$") {
        #    proxy_pass https://10-36-75-117.onepay.local:443;
        #    break;
        #}
        rewrite ^/iportal-mobile/(.*)$ /iportal/$1 break;
        proxy_pass https://$iportal_service_ssl
;
    }
    #location ~ ^/iportal-mobile/assets/(.*) {
    #    #rewrite ^/iportal-mobile/assets/(.*) /iportal-mobile/assets/$1 break;
    #    root /usr/share/nginx/secure.opdev.vn;
    #}

    location /paygate/bank-amount/bank-amount.json {
        root $bank_amount_root;
    }
}

#Redirect to http
server {
    listen 443 ssl;
    server_name onepay.com.vn www.onepay.com.vn;
    #ssl                  on;
    ssl_certificate /etc/nginx/conf.d/onepay_com_vn.crt;
    ssl_certificate_key /etc/nginx/conf.d/onepay_com_vn.key;

    ssl_session_timeout 1m;

    #ssl_protocols TLSv1.1 TLSv1.2;
    ssl_protocols TLSv1.2;
    #ssl_ciphers HIGH:!aNULL:!eNULL:!PSK:!RC4:!MD5;
    ssl_ciphers "EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA:ECDHE-RSA-DES-CBC3-SHA:EDH-RSA-DES-CBC3-SHA:AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA256:AES128-SHA256:AES256-SHA:AES128-SHA:DES-CBC3-SHA:HIGH:!aNULL:!eNULL:!EXPORT:!DES:!MD5:!PSK:!RC4:!3DES";
    ssl_prefer_server_ciphers on;

    access_log /var/log/nginx/access.log no_query;


    location ~ ^/($|home($|/.*)) {
        rewrite ^(.*) http://onepay.com.vn$1;
    }
}

server {
    listen 443 ssl;
    server_name dev13-ma.opdev.vn;
    proxy_read_timeout 3m;
    #ssl                  on;
    ssl_certificate /etc/nginx/conf.d/_.opdev.vn-2024.crt;
    ssl_certificate_key /etc/nginx/conf.d/_.opdev.vn-2024.key;

    ssl_session_timeout 5m;

    #ssl_protocols TLSv1.1 TLSv1.2;
    ssl_protocols TLSv1.2;
    ssl_ciphers HIGH:!aNULL:!eNULL:!PSK:!RC4:!MD5:!3DES;
    ssl_prefer_server_ciphers on;

    ssl_dhparam /etc/nginx/conf.d/dhparams.pem;
    # Set HSTS to 365 days
    #add_header Strict-Transport-Security 'max-age=31536000; includeSubDomains';
    add_header Strict-Transport-Security 'max-age=31536000';

    proxy_http_version 1.1;
    proxy_set_header Connection "";

    proxy_set_header Accept-Encoding "";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    ### Most PHP, Python, Rails, Java App can use this header ###
    #proxy_set_header X-Forwarded-Proto https;##
    #This is better##
    #proxy_set_header        X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Proto https;
    add_header Front-End-Https on;

    ### By default we don't want to redirect it ####
    proxy_redirect off;

    #root /usr/share/nginx/ma.opdev.vn;
    root $ma_root;

    #location = /favicon.ico {
    #    alias /var/www/icons/favicon.ico;
    #}

    access_log /var/log/nginx/access.log no_query;

    location / {
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        #add_header Content-Security-Policy "default-src 'self'; font-src ;img-src  data:; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header 'Referrer-Policy' 'origin';
        add_header Permissions-Policy "geolocation=(),midi=(),sync-xhr=(),microphone=(),camera=(),magnetometer=(),gyroscope=(),fullscreen=(self),payment=()";

        index index.html index.htm;
        try_files $uri $uri/ /index.html;
        gzip on;
        gzip_min_length 1000;
        gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css image/x-icon;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
    }
    #ldp-config
    location /ldp-config {
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        #add_header Content-Security-Policy "default-src 'self'; font-src ;img-src  data:; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header 'Referrer-Policy' 'origin';
        add_header Permissions-Policy "geolocation=(),midi=(),sync-xhr=(),microphone=(),camera=(),magnetometer=(),gyroscope=(),fullscreen=(self),payment=()";

        #root $ma_ldp_config_root;
        alias $ma_ldp_config_root;
        try_files $uri$args $uri$args/ /index.html;
        #index index.html index.htm;
        #try_files $uri $uri/ /index.html;

        gzip on;
        gzip_min_length 1000;
        gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css image/x-icon image/svg+xml;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
    }


    location /env {
       root $env_root;
       try_files $uri $uri/ /env/index.html;
    }

    location /api/v1 {
        gzip on;
        gzip_min_length 1000;
        gzip_types application/json;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;

                if ($is_stg) {
                        rewrite ^/api/v1/(.*) /ma/api/v1/$1 break;
                        proxy_pass http://127.0.0.1:8080;
                        break;
                }

                if ($is_prod1) {
                        rewrite ^/api/v1/(.*) /$1 break;
                        proxy_pass http://madm2-frontend-prod1;
                        break;
                }
        rewrite ^/api/v1/(.*) /$1 break;
        proxy_pass http://$madm2_frontend;
    }

    location ~ ^/login$ {
        expires 0;
        #rewrite ^(/.*) $1 break;
                if ($is_stg) {
                rewrite ^/login(.*) /ma/api/v1/login$1 break;
                proxy_pass http://127.0.0.1:8080;
                break;
        }

                if ($is_prod1) {
                        proxy_pass http://madm2-frontend-prod1;
                        break;
                }

        proxy_pass http://$madm2_frontend;
    }

    #location ~ ^/assets/(.*) {
    #    rewrite ^/assets/(.*) /madm2/assets/$1 break;
    #}

    location /api/v1/app/data {
        expires 0;
        rewrite ^/api/v1/app/(.*) /assets/$1 break;
        #root  /usr/share/nginx/secure.opdev.vn/madm2/assets;
    }

    location ~ ^/eventbus/(.*) {
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_read_timeout 60s;
        # for https
        #if ($remote_addr ~ "^(27.67.187.72|**************)$") {
        #    rewrite ^/eventbus/(.*) /ma/eventbus/$1 break;
        #    proxy_pass http://127.0.0.1:8080;
        #    break;
        #}

                if ($is_stg) {
            rewrite ^/eventbus/(.*) /ma/eventbus/$1 break;
                        proxy_pass http://127.0.0.1:8080;
                        break;
                }

        proxy_pass http://ma_eventbus;
    }

    location /accounts {
        add_header Pragma "no-cache";
        add_header Cache-Control "no-store, no-cache, must-revalidate, post-check=0, pre-check=0";
        add_header X-Frame-Options "SAMEORIGIN" always;
        try_files $uri $uri/ /index.html =404;
        gzip on;
        gzip_min_length 1000;
        gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css image/x-icon image/svg+xml;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
    }

    location /accounts/api/v1 {
        proxy_set_header Host proxy.secure.opdev.vn;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        #rewrite /accounts/api/v1/(.*) /$1 break;
        proxy_pass http://accounts_web;
    }

    location /accounts/ma/api/v1 {
        proxy_set_header Host proxy.secure.opdev.vn;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        rewrite /accounts/ma/api/v1/(.*) /accounts/api/v1/$1 break;
        proxy_pass http://accounts_web;
    }

    location /ldp-config/api/v1 {
        gzip on;
        gzip_min_length 1000;
        gzip_types application/json;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;

        rewrite ^/api/v1/(.*) /$1 break;
        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
        proxy_set_header Host api.onepay.local;
        proxy_pass https://$ldp_config;
    }

    #payment-link
    location /paymentlink {
        access_log /var/log/nginx/access.log no_query;
        proxy_pass https://$api_15772_15773_157172;
    }

    location /plba/api/v1 {
        gzip on;
        gzip_min_length 1000;
        gzip_types application/json;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;

        rewrite ^/api/v1/(.*) /$1 break;
        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify off;
        proxy_ssl_server_name off;
        proxy_set_header Host api.onepay.local;
        proxy_pass https://$api_15772_15773_157172;
    }

}


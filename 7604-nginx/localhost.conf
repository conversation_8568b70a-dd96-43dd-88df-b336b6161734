server {
    listen       8080;
    server_name  localhost;
    allow 127.0.0.1;
    #######deny all;
    proxy_http_version 1.1;
    proxy_set_header Connection "";
    proxy_redirect     off;

    ## for call https to other server
    proxy_set_header Host api.onepay.local;

    proxy_ssl_certificate           /etc/nginx/conf.d/_.onepay.local.cer;
    proxy_ssl_certificate_key       /etc/nginx/conf.d/_.onepay.local.key;
    proxy_ssl_trusted_certificate   /etc/nginx/conf.d/onepayrootca.cer;
    proxy_ssl_name                  api.onepay.local;
    proxy_ssl_verify                on;
    proxy_ssl_server_name           on;

   
    location /nginx_status {
        stub_status on;
        allow 127.0.0.1;
        #######deny all;
    }


    location ~ ^/iportal/(login|api/v1/.+)$ {
        proxy_pass https://$iportal_service_ssl;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        expires 0;
        # ssl for call other server
        proxy_set_header Host api.onepay.local;
        proxy_set_header X-Forwarded-For $remote_addr;

        proxy_ssl_certificate           /etc/nginx/conf.d/_.onepay.local.cer;
        proxy_ssl_certificate_key       /etc/nginx/conf.d/_.onepay.local.key;
        proxy_ssl_trusted_certificate   /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name                  api.onepay.local;
        proxy_ssl_verify                on;
        proxy_ssl_server_name           on;
    }

    location /ma/api/v1 {
        gzip  on;
        gzip_min_length 1000;
        gzip_types    application/json;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
        proxy_set_header Host api.onepay.local;
        proxy_set_header X-Forwarded-For $remote_addr;

        proxy_ssl_certificate           /etc/nginx/conf.d/_.onepay.local.cer;
        proxy_ssl_certificate_key       /etc/nginx/conf.d/_.onepay.local.key;
        proxy_ssl_trusted_certificate   /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name                  api.onepay.local;
        proxy_ssl_verify                on;
        proxy_ssl_server_name           on;
        #rewrite ^/ma/api/v1/(.*) /$1 break;
        proxy_pass https://madm2-frontend-stg;
    }

    location ~ ^/ma/eventbus/(.*) {
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_read_timeout 60s;
        proxy_set_header Host api.onepay.local;
        proxy_set_header X-Forwarded-For $remote_addr;

        proxy_ssl_certificate           /etc/nginx/conf.d/_.onepay.local.cer;
        proxy_ssl_certificate_key       /etc/nginx/conf.d/_.onepay.local.key;
        proxy_ssl_trusted_certificate   /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name                  api.onepay.local;
        proxy_ssl_verify                on;
        proxy_ssl_server_name           on;

        proxy_pass https://ma-eventbus-stg;
    }

    #location /tsp/api/v2/ {
    #    proxy_ssl_name pci.onepay.local;
    #    proxy_set_header Host pci.onepay.local;
    #    proxy_set_header Connection "";
    #    proxy_pass https://pci-16774-16775;
    #}

    location ~ ^/onecomm-pay/(verifycardapi|verifyauthapi).op$ {
        proxy_ssl_name pci.onepay.local;
        proxy_set_header Host pci.onepay.local;
        proxy_set_header Connection "";
        proxy_pass https://pci-16774-16775;
    }

}

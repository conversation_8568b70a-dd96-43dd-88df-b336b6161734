# HTTP proxy
#
#Ngam dinh goi bang IP
server {
    listen       80;
    proxy_set_header Host $host;
    proxy_read_timeout 2m;

    location ~ ^/(index.php|administrator|box.js)($|/) {
        rewrite ^(.*)$ https://dev13-secure.opdev.vn$1;
    }

    location ~ ^/onepay/.+html$ {
        rewrite ^/onepay/(.+) http://onepay.com.vn/home/<USER>
    }

    location /nginx_status {
        # Turn on stats
        #stub_status on;
        access_log   off;
        # only allow access from *********** #
        allow ***********;
        #######deny all;
    }

    location / {
        return 302 https://$host$request_uri;
    }
}

server {
    listen       80;
    server_name  secure.onepay.vn ma.onepay.vn;
    #Redirect to https
    #if ($http_x_forwarded_proto != "https") {
    #    rewrite ^(.*)$ https://$server_name$1;
    #}
   
    access_log /var/log/nginx/access.log no_query;


    return 302 https://$host$request_uri;
}

server {
    listen 80;
    server_name onepay.com.vn www.onepay.com.vn;
    root /usr/share/nginx/onepay.com.vn;

    access_log /var/log/nginx/access.log no_query;
   

    location /oriflame/ {
        index index.html;
    }

    location ~ ^/oriflame/sendmail/$ {
        proxy_pass http://**********:8180;
    }

    location /paygate/sites/ {

    }

    #location ~ ^($|/$|/home($|/)) {
    #    rewrite ^(/.*) https://www.onepay.vn$1;
    #}
    location ~ ^($|/$|/home($|/)) {
        rewrite ^(/.*) https://www.onepay.vn;
    }
}

server {
    listen 80;
    server_name khenpoappey.org www.khenpoappey.org;

    root /usr/share/nginx/khenpoappey.org;

    location / {
        index index.html;
    }
}

server {
    listen 80;
    server_name mpayvn.vn mpayvn.com mobilepayment.vn www.mpayvn.vn www.mpayvn.com www.mobilepayment.vn;
    root /usr/share/nginx/mpayvn.vn;

    location ~* \.(otf|eot|ttf|woff|woff2)$ {
        add_header Access-Control-Allow-Origin *;
    }

    location / {

    }
}

#server {
#    listen 80;
#    server_name onebill.vn www.onebill.vn;
#    proxy_read_timeout 2m;
#    proxy_redirect off;

    ### Set headers ####
#    proxy_set_header        Accept-Encoding   "";
#    proxy_set_header        Host            $host;
#    proxy_set_header        X-Real-IP       $remote_addr;
#    proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;

    ### Most PHP, Python, Rails, Java App can use this header ###
    #proxy_set_header X-Forwarded-Proto https;##
    #This is better##
#    proxy_set_header        X-Forwarded-Proto $scheme;
#    add_header              Front-End-Https   off;

#    location / {
#        proxy_pass http://************:8180;
#    }
#}

# Keycloak Migration Summary - MA Portal (Merchant Account)

## 📋 **Tổng quan dự án**

Migration hệ thống xác thực từ **OneAM** sang **Keycloak** cho <PERSON> Portal (Merchant Portal), đảm bảo:
- ✅ Giữ nguyên UX/UI như OneAM
- ✅ Tương thích ngược với dữ liệu OneAM (password encoding, user data)
- ✅ SSO flow với OAuth2/OIDC chuẩn
- ✅ Email OTP với device trust (Don't ask again on this device)

---

## 🎯 **Các công việc đã hoàn thành**

### 1️⃣ **Migration Tool (Go) - Chuyển dữ liệu từ OneAM sang Keycloak**

**File:** `/root/projects/onepay/portal/ma-keycloak-26.3.4/ma-migrate-du-lieu/main.go`

#### ✅ **Đã fix:**
- **Password Encoding Logic**: 
  - <PERSON> đầu: Triple encoding (sai) ❌
  - Đ<PERSON> sửa: Lấy password đã double-encoded từ OneAM sang Keycloak trực tiếp ✅
  - Encoding: HMAC-SHA256 với `key1` và `key2`, output **UPPERCASE HEX**
  
- **Key Decoding**: 
  - Thêm `hex.DecodeString()` để decode hex key thành bytes trước khi HMAC
  - Sử dụng `strings.ToUpper()` để đảm bảo output hex là uppercase

- **User Attributes**:
  - Set `EmailVerified: true` để không bắt user verify email lại
  - Set `RequiredActions: []string{}` để không force user đổi password

#### 📝 **Cách chạy:**
```bash
cd /root/projects/onepay/portal/ma-keycloak-26.3.4/ma-migrate-du-lieu
go run main.go
```

---

### 2️⃣ **Custom Keycloak Authenticator - Password Validation**

**File:** `/root/projects/onepay/portal/ma-keycloak-26.3.4/src/main/java/com/onepay/keycloak/auth/MaUserAuthenticator.java`

#### ✅ **Đã implement:**
- **OneAM Password Encoding** (Level 12 - Double HMAC-SHA256):
  ```
  Step 1: HMAC-SHA256(key1, password) → hash1
  Step 2: HMAC-SHA256(key2, hash1) → final_hash (UPPERCASE)
  ```

- **Password Validation**:
  - Encode password user nhập với logic OneAM
  - So sánh với password stored trong Keycloak bằng `user.credentialManager().isValid()`

- **Key Features**:
  - Hex key decoding: `hexStringToBytes()` để decode hex key trước khi HMAC
  - Output uppercase: `bytesToHex()` với format `%02X`
  - Detailed logging cho mỗi bước encoding

---

### 3️⃣ **Custom Keycloak Authenticator - Email OTP**

**File:** `/root/projects/onepay/portal/ma-keycloak-26.3.4/src/main/java/com/onepay/keycloak/auth/email/EmailOtpCombinedAuthenticator.java`

#### ✅ **Đã implement:**
- **OTP Generation & Validation**:
  - 6-digit OTP code
  - TTL: 5 minutes
  - Max attempts: 5 lần
  - Resend interval: 1 minute

- **Device Trust (Cookie)**:
  - Cookie name: `op_td_ma_{username}`
  - Cookie lifetime: 180 days
  - Format: `Base64(username|device_fingerprint|expiry)`
  - Device fingerprint: MD5(User-Agent first 100 chars)

- **OTP Login History**:
  - Lưu vào `tb_otp_login_history` khi user tick "Don't ask again on this device"
  - Giới hạn: 10 cookies/user (deactivate cookies cũ nhất)
  - Skip OTP logic: Check cookie + browser/OS similarity

#### 📊 **Database Schema:**
```sql
CREATE TABLE tb_otp_login_history (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    device_cookie TEXT,
    device_fingerprint VARCHAR(255),
    browser_name VARCHAR(100),
    browser_version VARCHAR(50),
    os_name VARCHAR(100),
    os_version VARCHAR(50),
    device_type VARCHAR(50),
    ip_address VARCHAR(100),
    user_agent TEXT,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

### 4️⃣ **Keycloak Theme - MA Portal UI/UX**

**Folder:** `/root/projects/onepay/portal/ma-keycloak-26.3.4/themes/merchant-portal/`

#### ✅ **Login Screens:**
- **`login/login.ftl`**: Login form (username/password)
- **`login/email-otp-combined.ftl`**: OTP input form với checkbox "Don't ask again" **checked by default**
- **`login/login-reset-password.ftl`**: Forgot password form

#### ✅ **Email Templates:**
- **`email/html/code-email.ftl`**: OTP email (HTML)
- **`email/text/code-email.ftl`**: OTP email (plain text)
- **`email/html/password-reset.ftl`**: Password reset email (HTML)
- **`email/text/password-reset.ftl`**: Password reset email (plain text)
- **`email/html/device-alert.ftl`**: Device alert email (HTML)
- **`email/text/device-alert.ftl`**: Device alert email (plain text)
- **`email/html/template.ftl`**: Base email layout

#### ✅ **Translation Messages:**
- **`login/messages/messages_vi.properties`**: Vietnamese
- **`login/messages/messages_en.properties`**: English

#### ✅ **Assets:**
- Logo OnePay, icons, illustrations
- CSS: Bootstrap + custom styles

---

### 5️⃣ **Keycloak Config Manager**

**File:** `/root/projects/onepay/portal/ma-keycloak-26.3.4/src/main/java/com/onepay/keycloak/auth/config/AuthConfigManager.java`

#### ✅ **Đã config:**
- Database connection: PostgreSQL (`keycloak-ma` schema)
- Cookie names: `op_td_ma`
- Email subjects: "OnePay MA - Two-Step Verification Code", "OnePay MA - Reset Password"
- Theme detection: Check `/auth-ma` hoặc `/ma` trong request path

**Config File:** `/root/projects/onepay/portal/ma-keycloak-26.3.4/src/main/resources/onepay-auth-config.properties`

---

### 6️⃣ **ma-service - Backend Integration với Keycloak**

**File:** `/root/projects/onepay/portal/ma-service/src/main/java/com/onepay/ma/service/handler/user/impl/UserSessionKeycloakHandlerImpl.java`

#### ✅ **OAuth2 Authorization Code Flow:**

1. **Exchange Code for Token**:
   - Endpoint: `POST /auth-ma/realms/merchant-portal/protocol/openid-connect/token`
   - Body: `grant_type=authorization_code&code=...&client_id=...&client_secret=...&redirect_uri=...`

2. **Get User Info**:
   - Endpoint: `GET /auth-ma/realms/merchant-portal/protocol/openid-connect/userinfo`
   - Header: `Authorization: Bearer {access_token}`

3. **User Session Creation**:
   - Tìm user trong database bằng email
   - Sync profile từ Keycloak (name, phone, address)
   - Load permissions & merchants
   - Tạo `UserSession` object

#### ✅ **Config:**

**File:** `/root/projects/onepay/portal/ma-service/src/main/resources/server.properties`

```properties
# Keycloak Config
keycloak.server_url=http://localhost/auth-ma
keycloak.realm=merchant-portal
keycloak.client_id=merchant-portal-client
keycloak.client_secret=<CLIENT_SECRET_HERE>
keycloak.redirect_uri=https://dev13-ma.opdev.vn/login
keycloak.token_uri=http://localhost/auth-ma/realms/merchant-portal/protocol/openid-connect/token
keycloak.userinfo_uri=http://localhost/auth-ma/realms/merchant-portal/protocol/openid-connect/userinfo
keycloak.logout_uri=http://localhost/auth-ma/realms/merchant-portal/protocol/openid-connect/logout
keycloak.authorize_uri=http://localhost/auth-ma/realms/merchant-portal/protocol/openid-connect/auth
```

**File:** `/root/projects/onepay/portal/ma-service/src/main/resources/spring/spring-config.xml`

```xml
<bean id="keycloakConfig" class="com.onepay.ma.service.models.KeycloakConfig">
    <property name="serverUrl" value="${keycloak.server_url}"/>
    <property name="realm" value="${keycloak.realm}"/>
    <property name="clientId" value="${keycloak.client_id}"/>
    <property name="clientSecret" value="${keycloak.client_secret}"/>
    <property name="redirectUri" value="${keycloak.redirect_uri}"/>
    <property name="tokenUri" value="${keycloak.token_uri}"/>
    <property name="userinfoUri" value="${keycloak.userinfo_uri}"/>
    <property name="logoutUri" value="${keycloak.logout_uri}"/>
    <property name="authorizeUri" value="${keycloak.authorize_uri}"/>
</bean>
```

#### ✅ **Routes:**

**File:** `/root/projects/onepay/portal/ma-service/src/main/java/com/onepay/ma/service/util/RoutePool.java`

```java
public static final String USER_SESSION_LOGIN_CALLBACK = "/login";  // OAuth2 callback
```

**File:** `/root/projects/onepay/portal/ma-service/src/main/java/com/onepay/ma/service/server/ServiceServer.java`

```java
// OAuth2 Callback Route
router.route(HttpMethod.GET, RoutePool.USER_SESSION_LOGIN_CALLBACK)
      .handler(UserSessionPostHandler.createKeycloak());
```

---

### 7️⃣ **Nginx Configuration**

**File:** `/root/projects/onepay/portal/ma-keycloak-26.3.4/7604-nginx/https.conf`

#### ✅ **Redirect URI:**
- Từ: `/accounts/welcome` (OneAM)
- Sang: `/login` (Keycloak OAuth2 callback)

```nginx
location ~ ^/(accounts|accounts/)$ {
    set $redirect_uri "https://$server_name/login";
    return 302 "http://localhost/auth-ma/realms/merchant-portal/protocol/openid-connect/auth?client_id=merchant-portal-client&redirect_uri=$redirect_uri&response_type=code&scope=openid%20email%20profile";
}
```

---

## 🔧 **Các lỗi đã fix**

### 1. **Password Encoding Issues**
- ❌ **Lỗi**: Triple encoding trong migration tool
- ✅ **Fix**: Lấy password OneAM trực tiếp, không encode thêm

- ❌ **Lỗi**: Hex output lowercase, OneAM dùng uppercase
- ✅ **Fix**: `strings.ToUpper()` trong Go, `%02X` trong Java

- ❌ **Lỗi**: Dùng hex key trực tiếp làm bytes
- ✅ **Fix**: Decode hex key thành bytes trước khi HMAC

### 2. **Keycloak Admin Account Deleted**
- ❌ **Lỗi**: User lỡ xóa admin account
- ✅ **Fix**: Tạo lại bằng SQL:
  ```sql
  INSERT INTO user_entity (id, created_timestamp, username, realm_id, enabled, email_verified)
  VALUES ('keycloak-ma-user-id', EXTRACT(EPOCH FROM NOW()) * 1000, 'keycloak-ma', 
          (SELECT id FROM realm WHERE name = 'master'), true, true);
  
  INSERT INTO credential (id, user_id, type, secret_data, credential_data, priority, created_date)
  VALUES ('keycloak-ma-password-id', 'keycloak-ma-user-id', 'password', 
          '{"value":"<HASHED_PASSWORD>","salt":""}', 
          '{"hashIterations":27500,"algorithm":"pbkdf2-sha256"}', 
          10, EXTRACT(EPOCH FROM NOW()) * 1000);
  ```

### 3. **Theme Template Errors**
- ❌ **Lỗi**: Template not found `email-otp-combined.ftl`
- ✅ **Fix**: Deploy theme đúng folder structure

- ❌ **Lỗi**: Auto-reload loop do legacy JS
- ✅ **Fix**: Comment out `login.js`, `otp.js` trong `template.ftl`

- ❌ **Lỗi**: `displayMessage` undefined trong `login-reset-password.ftl`
- ✅ **Fix**: Sửa conditional check: `message?has_content && (message.type != 'warning' && message.type != 'info')`

### 4. **Email Template Errors**
- ❌ **Lỗi**: Template not found `text/password-reset.ftl`
- ✅ **Fix**: Tạo plain text version cho tất cả email templates

- ❌ **Lỗi**: Variable `${otpCode}` không tồn tại
- ✅ **Fix**: Đổi thành `${code}` (match với Java code)

- ❌ **Lỗi**: Email templates không đúng folder structure
- ✅ **Fix**: Tổ chức lại: `email/html/` và `email/text/`

### 5. **ma-service Spring Context Errors**
- ❌ **Lỗi**: `Could not resolve placeholder 'one_am.user'`
- ✅ **Fix**: Uncomment OneAM config trong `server.properties` (vì handlers khác vẫn dùng)

- ❌ **Lỗi**: `KeycloakConfig` class not found
- ✅ **Fix**: Tạo model class và thêm import

- ❌ **Lỗi**: Incompatible types khi pass `sqlConnection`
- ✅ **Fix**: Inline session creation logic trong `flatMap` chain

### 6. **OAuth2 Client Configuration**
- ❌ **Lỗi**: `client_not_found` - `merchant-portal-client`
- ✅ **Fix**: Tạo lại client trong Keycloak Admin Console

- ❌ **Lỗi**: Client không có Credentials tab
- ✅ **Fix**: Bật "Client authentication" để chuyển từ Public sang Confidential client

- ❌ **Lỗi**: `ma-service` dùng sai `client_id` (`ma-migration-tool`)
- ✅ **Fix**: Đổi sang `merchant-portal-client` trong `server.properties`

### 7. **Routing Issues**
- ❌ **Lỗi**: Keycloak redirect đến `/accounts/welcome?code=xxx` nhưng `ma-service` không handle
- ✅ **Fix**: Thêm route `GET /login` cho OAuth2 callback

- ❌ **Lỗi**: Nginx redirect URI không match với `ma-service`
- ✅ **Fix**: Đổi redirect URI từ `/accounts/welcome` → `/login`

### 8. **Checkbox "Don't ask again" không default tick**
- ❌ **Lỗi**: User quên tick → không lưu `tb_otp_login_history`
- ✅ **Fix**: Thêm `checked` attribute vào checkbox trong `email-otp-combined.ftl`

---

## 📦 **Build & Deploy Instructions**

### 1. **Build Keycloak Provider JAR + Theme**
```bash
cd /root/projects/onepay/portal/ma-keycloak-26.3.4
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
mvn clean install -DskipTests
```

**Output:**
- `target/keycloak-merchant-auth-1.0-SNAPSHOT.jar`
- `themes/merchant-portal/`

### 2. **Deploy to Keycloak**
```bash
# Copy JAR to providers folder
cp target/keycloak-merchant-auth-1.0-SNAPSHOT.jar /opt/keycloak/providers/

# Copy theme
cp -r themes/merchant-portal /opt/keycloak/themes/

# Restart Keycloak
systemctl restart keycloak
# hoặc
docker restart keycloak-ma
# hoặc
podman restart keycloak-ma
```

### 3. **Build ma-service**
```bash
cd /root/projects/onepay/portal/ma-service
mvn clean package -DskipTests

# Deploy
./sync_dev
```

### 4. **Deploy Nginx Config**
```bash
# Copy config
cp /root/projects/onepay/portal/ma-keycloak-26.3.4/7604-nginx/https.conf /etc/nginx/sites-available/ma-portal.conf

# Test config
nginx -t

# Reload
nginx -s reload
```

---

## 🔐 **Keycloak Admin Console Setup**

### 1. **Create Realm**
- Realm name: `merchant-portal`
- Display name: `OnePay Merchant Portal`
- Enabled: `ON`

### 2. **Create Client**
- Client ID: `merchant-portal-client`
- Client type: `OpenID Connect`
- Client authentication: `ON` (Confidential client)
- Valid redirect URIs: 
  - `https://dev13-ma.opdev.vn/login`
  - `https://dev13-ma.opdev.vn/*`
- Web origins: `https://dev13-ma.opdev.vn`

### 3. **Get Client Secret**
- Clients → `merchant-portal-client` → Credentials tab
- Copy `Client secret`
- Update vào `ma-service/src/main/resources/server.properties`

### 4. **Configure Authentication Flow**
- Authentication → Flows → Create flow: `MA Browser Flow`
- Add executions:
  1. `MA User Authenticator` (REQUIRED)
  2. `Email OTP Combined Authenticator` (REQUIRED)
- Bind to: Browser flow

### 5. **Configure Realm Theme**
- Realm settings → Themes
- Login theme: `merchant-portal`
- Email theme: `merchant-portal`
- Save

### 6. **Configure SMTP**
- Realm settings → Email
- Host: `<SMTP_HOST>`
- Port: `<SMTP_PORT>`
- From: `<EMAIL>`
- Enable SSL/TLS
- Test connection

---

## 🧪 **Testing Checklist**

### ✅ **Login Flow**
1. Truy cập `https://dev13-ma.opdev.vn/accounts`
2. Redirect đến Keycloak login form
3. Nhập username/password (password từ OneAM phải work)
4. Redirect đến OTP form
5. Nhận OTP email
6. Nhập OTP (checkbox "Don't ask again" đã tick sẵn)
7. Login thành công → Redirect về MA Portal dashboard

### ✅ **Device Trust**
1. Login lần đầu với "Don't ask again" checked
2. Check database: `SELECT * FROM tb_otp_login_history WHERE user_id = '...'`
3. Logout
4. Login lại → Skip OTP (vì đã trust device)

### ✅ **Forgot Password**
1. Click "Forgot Password?"
2. Nhập email
3. Nhận email reset password
4. Click link → Redirect đến Keycloak reset password form
5. Nhập password mới
6. Password được encode theo OneAM logic (double HMAC-SHA256)

### ✅ **Email Templates**
1. OTP email: Logo OnePay + 6-digit code
2. Password reset email: Logo OnePay + link
3. Device alert email: Thông báo đăng nhập từ thiết bị mới

---

## 📊 **Database Changes**

### ✅ **Keycloak Database (keycloak-ma schema)**
- User credentials: Lưu password đã encode từ OneAM
- User attributes: Sync từ OneAM (name, phone, address)
- Client config: `merchant-portal-client` với client secret

### ✅ **ma-service Database**
- `tb_otp_login_history`: Lưu device trust information
- Giữ nguyên các bảng khác (tb_user, tb_permission, tb_merchant, ...)

---

## 🔄 **SSO Integration - Payment Link Config**

### ❓ **Câu hỏi từ user:**
> "payment-link-config và payment-link-config-go có cần sửa để SSO không?"

### ✅ **Trả lời:**
**KHÔNG CẦN SỬA** `payment-link-config` và `payment-link-config-go`.

**Lý do:**
- Các service này **KHÔNG trực tiếp tích hợp với Keycloak**
- Chúng nhận `X-USER-ID` header từ `ma-service` sau khi user đã SSO thành công
- `ma-service` đã xử lý toàn bộ OAuth2 flow với Keycloak
- `ma-service` đặt `X-USER-ID` vào request header khi proxy đến các service khác

**Flow:**
```
User → Keycloak (login) → ma-service (OAuth2 callback) 
     → ma-service sets X-USER-ID header 
     → payment-link-config/payment-link-config-go (nhận X-USER-ID)
```

---

## 🚀 **Next Steps (Chưa làm)**

### 🔲 **Deploy lên Production**
- [ ] Cập nhật domain trong Keycloak Valid Redirect URIs
- [ ] Cập nhật `keycloak.redirect_uri` trong `ma-service`
- [ ] Test full flow trên production environment

### 🔲 **Monitoring & Logging**
- [ ] Setup Keycloak event logging
- [ ] Monitor OTP delivery success rate
- [ ] Track device trust usage statistics

### 🔲 **Security Enhancements**
- [ ] Enable rate limiting cho OTP requests
- [ ] Setup IP whitelisting nếu cần
- [ ] Review client secret rotation policy

### 🔲 **Documentation**
- [ ] User guide: Cách reset password, device trust
- [ ] Admin guide: Cách quản lý users trong Keycloak
- [ ] Troubleshooting guide: Common issues & solutions

---

## 📞 **Support & Contacts**

- **Keycloak Admin Console**: `https://dev13-ma.opdev.vn/auth-ma/admin`
- **MA Portal**: `https://dev13-ma.opdev.vn/accounts`
- **Database**: PostgreSQL `keycloak-ma` schema

---

## 📝 **Important Notes**

1. **Password Encoding**: OneAM dùng double HMAC-SHA256 với hex keys và uppercase output. Keycloak phải replicate chính xác logic này.

2. **Device Trust**: Cookie lifetime 180 days, database record giới hạn 10 cookies/user để security.

3. **Email OTP**: OTP TTL 5 phút, max 5 attempts, resend interval 1 phút để chống spam.

4. **Migration Tool**: Chỉ chạy 1 lần để migrate users từ OneAM. Sau khi migrate xong, disable tool này.

5. **Legacy OneAM Config**: Vẫn giữ trong `ma-service` vì một số handlers cũ còn reference đến. Sẽ cleanup sau khi migrate hết.

6. **Client Secret**: **BẮT BUỘC** phải lấy từ Keycloak Admin Console và update vào `server.properties`. Không có client secret thì OAuth2 flow sẽ fail với `400 Bad Request`.

---

**Generated:** 2025-10-01  
**Status:** ✅ Login flow đã test thành công  
**Pending:** Deploy theme với checkbox checked + test device trust

---

## 🎉 **Achievement Unlocked!**

```
┌─────────────────────────────────────────┐
│   ✅ Keycloak Migration - MA Portal     │
│                                         │
│   🔐 Password encoding: DONE            │
│   📧 Email OTP: DONE                    │
│   🍪 Device trust: DONE                 │
│   🔄 OAuth2 flow: DONE                  │
│   🎨 Custom theme: DONE                 │
│   🛠️  ma-service integration: DONE      │
│                                         │
│   Status: Ready for final testing! 🚀   │
└─────────────────────────────────────────┘
```


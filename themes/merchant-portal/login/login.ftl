<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('username','password') displayInfo=false>
    <div class="wrap_select_lang">
        <div class="custom-lang-selector" id="customLangSelector">
            <div class="lang-selected" id="langSelected">
                <img src="${url.resourcesPath}/assets/en_lang.svg" class="lang-flag" id="langSelectedFlag">
                <span class="lang-label" id="langSelectedLabel">EN</span>
                <span class="lang-caret"><img src="${url.resourcesPath}/assets/chevron-down.svg" alt="caret-down" class="caret-down" id="langCaret"></span>
            </div>
            <div class="lang-options" id="langOptions" style="display:none;">
                <div class="header_select_lang_mobile" translate="selectLanguage"></div>
                <div class="lang-option" data-lang="en">
                    <img src="${url.resourcesPath}/assets/en_lang.svg" class="lang-flag" alt="EN"> <span data-translate-mobile="en">EN</span> <img class="icon_lang_checked" src="${url.resourcesPath}/assets/lang_check.svg" alt=""/>
                </div>
                <div class="lang-option" data-lang="vi">
                    <img src="${url.resourcesPath}/assets/vi_lang.svg" class="lang-flag" alt="VI"><span data-translate-mobile="vi">VI</span> <img class="icon_lang_checked" src="${url.resourcesPath}/assets/lang_check.svg" alt=""/>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row min-vh-100 align-items-center" style="display: flex;">
        <div class="col-md-6">
            <div class="login-container">
                <div class="logo-container mb-3 height_logo_mobile">
                    <img src="${url.resourcesPath}/assets/logo-onepay.svg" alt="OnePay Logo" class="logo">
                </div>
                
                <h1 class="welcome-text mb-3" translate="welcome"></h1>
                
                <#-- Check messagesPerField first (like invoice theme) -->
                <#if messagesPerField.existsError('username','password')>
                    <div class="error-message mb-3">
                        <img src="${url.resourcesPath}/assets/xcircle-red.svg" alt="Error" class="error-icon">
                        <span>406 - ${kcSanitize(messagesPerField.getFirstError('username','password'))?no_esc}</span>
                    </div>
                <#elseif message?has_content>
                    <#if message.type = 'error' || message.type = 'warning'>
                        <div class="error-message mb-3">
                            <img src="${url.resourcesPath}/assets/xcircle-red.svg" alt="Error" class="error-icon">
                            <span>
                                <#if message.summary?has_content>
                                    406 - ${kcSanitize(message.summary)?no_esc}
                                <#elseif message.type = 'error'>
                                    406 - ${msg("loginFailMessage")}
                                <#else>
                                    406 - ${kcSanitize(message.summary)?no_esc}
                                </#if>
                            </span>
                        </div>
                    <#else>
                        <div class="alert alert-info mb-3">
                            <span>${kcSanitize(message.summary)?no_esc}</span>
                        </div>
                    </#if>
                </#if>
                
                <form id="kc-form-login" onsubmit="login.disabled = true; return true;" action="${url.loginAction}" method="post">
                    <div class="form-group mb-3">
                        <div class="form-floating email-container">
                            <#if usernameEditDisabled??>
                                <input tabindex="1" id="username" class="form-control custom-input" name="username" value="${(login.username!'')}" type="text" disabled placeholder=" " placeholderTran="emailMobile" />
                            <#else>
                                <input tabindex="1" id="username" class="form-control custom-input" name="username" value="${(login.username!'')}" type="text" autofocus autocomplete="off" placeholder=" " placeholderTran="emailMobile" />
                            </#if>
                            <label for="username" translate="emailMobile"></label>
                            <span class="clear-toggle">
                                <img src="${url.resourcesPath}/assets/xcircle.svg" alt="Clear input" class="clear-icon">
                            </span>
                        </div>
                    </div>
                    
                    <div class="form-group mb-2">
                        <div class="form-floating password-container">
                            <input tabindex="2" id="password" class="form-control custom-input" name="password" type="password" autocomplete="off" placeholder=" " placeholderTran="password" />
                            <label for="password" translate="password"></label>
                            <span class="password-toggle">
                                <img src="${url.resourcesPath}/assets/eye-off.svg" alt="Toggle password" class="eye-icon">
                            </span>
                        </div>
                    </div>
                    
                    <#if realm.rememberMe && !usernameEditDisabled??>
                        <div class="form-group mb-2">
                            <div id="kc-form-options">
                                <div class="checkbox">
                                    <label>
                                        <#if login.rememberMe??>
                                            <input class="remember-me" tabindex="3" id="rememberMe" name="rememberMe" type="checkbox" translate="rememberMe" checked>
                                        <#else>
                                            <input class="remember-me" tabindex="3" id="rememberMe" name="rememberMe" type="checkbox" translate="rememberMe">
                                        </#if>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </#if>
                    
                    <#if realm.resetPasswordAllowed>
                        <div class="forgot-password mb-8">
                            <p><a tabindex="5" href="${url.loginResetCredentialsUrl}" translate="welcomeForgotPassword"></a></p>
                        </div>
                    </#if>
                    
                    <div id="kc-form-buttons" class="form-group">
                        <input type="hidden" id="id-hidden-input" name="credentialId" value="${selectedCredential!''}"/>
                        <button tabindex="4" class="btn btn-primary btn-block login-btn" name="login" id="kc-login" type="submit" translate="login" disabled></button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="col-md-6 d-none d-md-block" style="z-index: -1;">
            <div class="illustration-container">
                <img src="${url.resourcesPath}/assets/login-illustration.svg" alt="Security Illustration" class="img-fluid">
            </div>
        </div>
    </div>
</@layout.registrationLayout>
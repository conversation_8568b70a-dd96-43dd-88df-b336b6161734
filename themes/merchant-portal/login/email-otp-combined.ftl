<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('otp') displayInfo=false pageType="otp">
    <div class="wrap_select_lang">
        <div class="custom-lang-selector" id="customLangSelector">
            <div class="lang-selected" id="langSelected">
                <img src="${url.resourcesPath}/assets/en_lang.svg" class="lang-flag" id="langSelectedFlag">
                <span class="lang-label" id="langSelectedLabel">EN</span>
                <span class="lang-caret"><img src="${url.resourcesPath}/assets/chevron-down.svg" alt="caret-down" class="caret-down" id="langCaret"></span>
            </div>
            <div class="lang-options" id="langOptions" style="display:none;">
                <div class="header_select_lang_mobile" translate="selectLanguage"></div>
                <div class="lang-option" data-lang="en">
                    <img src="${url.resourcesPath}/assets/en_lang.svg" class="lang-flag" alt="EN"> <span data-translate-mobile="en">EN</span> <img class="icon_lang_checked" src="${url.resourcesPath}/assets/lang_check.svg" alt=""/>
                </div>
                <div class="lang-option" data-lang="vi">
                    <img src="${url.resourcesPath}/assets/vi_lang.svg" class="lang-flag" alt="VI"><span data-translate-mobile="vi">VI</span> <img class="icon_lang_checked" src="${url.resourcesPath}/assets/lang_check.svg" alt=""/>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row min-vh-100 align-items-center" style="display: flex;">
        <div class="col-md-6">
            <div class="otp-container">
                <div class="logo-container center-logo">
                    <img src="${url.resourcesPath}/assets/logo-onepay.svg" alt="OnePay Logo" class="logo">
                </div>
                
                <h1 class="welcome-text" translate="twoStepVerificationTitle">Two-Step Verification</h1>
                
                <#if displayMessage?? && displayMessage && message?has_content && message.type != 'warning'>
                    <div class="error-message mb-3">
                        <img src="${url.resourcesPath}/assets/xcircle-red.svg" alt="Error" class="error-icon">
                        <span>${kcSanitize(message.summary)?no_esc}</span>
                    </div>
                </#if>
                
                <form id="kc-form-login" onsubmit="login.disabled = true; return true;" action="${url.loginAction}" method="post">
                    <div class="otp-label" > <p class="otp-label" translate="enterOtpCode"></p><b class="otp-label">${userEmail!"[unknown]"}</b></div>
                    
                    <div class="otp-input-group">
                        <input type="text" class="otp-input" maxlength="1" onkeypress="return (event.charCode >= 48 && event.charCode <= 57)" tabindex="1">
                        <input type="text" class="otp-input" maxlength="1" onkeypress="return (event.charCode >= 48 && event.charCode <= 57)" tabindex="2">
                        <input type="text" class="otp-input" maxlength="1" onkeypress="return (event.charCode >= 48 && event.charCode <= 57)" tabindex="3">
                        <input type="text" class="otp-input" maxlength="1" onkeypress="return (event.charCode >= 48 && event.charCode <= 57)" tabindex="4">
                        <input type="text" class="otp-input" maxlength="1" onkeypress="return (event.charCode >= 48 && event.charCode <= 57)" tabindex="5">
                        <input type="text" class="otp-input" maxlength="1" onkeypress="return (event.charCode >= 48 && event.charCode <= 57)" tabindex="6">
                    </div>
                    
                    <input type="hidden" id="otp" name="otp" value="">
                    
                    <#-- Trust Device Checkbox -->
                    <#if showTrustDevice?? && showTrustDevice>
                    <div class="${properties.kcFormGroupClass!} ${properties.kcFormSettingClass!}">
                        <div id="kc-form-options" class="${properties.kcFormOptionsClass!}">
                            <div class="${properties.kcFormOptionsWrapperClass!}">
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="trustDevice" id="trustDevice" value="true" checked>
                                        <span style="color: #666; font-size: 14px;" translate="Dontask"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    </#if>
                    
                    <div class="form-group">
                        <button tabindex="7" class="btn btn-primary btn-block login-btn" name="login" id="kc-login" type="submit" translate="login"></button>
                    </div>
                    
                    <div class="reset-link-wrapper">
                        <span translate="troubleOtp">Have trouble with OTP Code?</span>
                        <span href="#" id="rescanQrOtp" class="reset-link" translate="reRegisterOtp"></span>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="col-md-6 d-none d-md-block" style="z-index: -1;">
            <div class="illustration-container">
                <img src="${url.resourcesPath}/assets/login-illustration.svg" alt="Security Illustration" class="img-fluid">
            </div>
        </div>
    </div>
</@layout.registrationLayout>

<script>
// OTP Input Handling
document.addEventListener('DOMContentLoaded', function() {
    const otpInputs = document.querySelectorAll('.otp-input');
    const hiddenOtpInput = document.getElementById('otp');
    const submitBtn = document.getElementById('kc-login');
    
    // Auto-focus first input
    otpInputs[0].focus();
    
    // Handle input events
    otpInputs.forEach((input, index) => {
        input.addEventListener('input', function(e) {
            const value = e.target.value;
            
            // Only allow numbers
            if (!/^\d$/.test(value)) {
                e.target.value = '';
                return;
            }
            
            // Move to next input
            if (value && index < otpInputs.length - 1) {
                otpInputs[index + 1].focus();
            }
            
            // Update hidden input
            updateHiddenInput();
        });
        
        input.addEventListener('keydown', function(e) {
            // Handle backspace
            if (e.key === 'Backspace' && !e.target.value && index > 0) {
                otpInputs[index - 1].focus();
            }
        });
        
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const pastedData = e.clipboardData.getData('text').replace(/\D/g, '');
            
            if (pastedData.length === 6) {
                for (let i = 0; i < 6; i++) {
                    if (otpInputs[i]) {
                        otpInputs[i].value = pastedData[i];
                    }
                }
                updateHiddenInput();
                otpInputs[5].focus();
            }
        });
    });
    
    function updateHiddenInput() {
        const otpValue = Array.from(otpInputs).map(input => input.value).join('');
        hiddenOtpInput.value = otpValue;
        
        // Enable/disable submit button
        submitBtn.disabled = otpValue.length !== 6;
    }
    
    // Form submission
    document.getElementById('kc-form-login').addEventListener('submit', function(e) {
        const otpValue = hiddenOtpInput.value;
        if (otpValue.length !== 6) {
            e.preventDefault();
            alert('Please enter a complete 6-digit OTP code');
            return false;
        }
    });
});
</script>

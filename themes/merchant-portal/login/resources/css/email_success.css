/* Container Styles */
.success-container {
    max-width: 400px;
    margin: 0 auto;
    text-align: center;
}

/* Logo Styles */
.logo-container {
    margin-bottom: 16px;
    text-align: center;
}

.logo {
    height: 45px;
    width: auto;
}

/* Content Styles */
.content {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.success-icon {
    width: 88px;
    height: auto;
    margin-bottom: 16px;
}

/* Welcome Text */
.welcome-text {
    color: var(--text-headings, #1C1C1C);
    text-align: center;
    font-size: var(--Typeface-size-2xl, 24px);
    font-style: normal;
    font-weight: 600;
    line-height: 36px;
    margin-bottom: 24px;
}

/* Success Title */
.success-title {
    color: var(--text-headings, #1C1C1C);
    text-align: center;
    font-size: var(--Typeface-size-lg, 16px);
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    margin-bottom: 4px;
}

/* Instruction Text */
.instruction-text {
    color: var(--text-body, #404040);
    text-align: center;
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
    margin-bottom: 24px;
}

/* Back Link */
.back-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-action, #2E6BE5);
    font-size: var(--Typeface-size-md, 14px);
    font-weight: 600;
    line-height: 20px;
    text-decoration: none;
}

.back-link:hover {
    text-decoration: none;
    color: var(--text-action-hover, #2457B8);
}

.arrow-icon {
    width: 20px;
    height: 20px;
}
/* Custom container width */
.container {
    max-width: 1200px !important;
    width: 100%;
    padding-right: 0px;
    padding-left: 0px;
    margin-right: auto;
    margin-left: auto;
}

.container .row .col-12 {
    padding-right: 0px;
    padding-left: 0px;
}


/* Language selector */
.language-selector {
    position: fixed;
    top: 24px;
    right: 24px;
    z-index: 1000;
}

.language-selector .form-select {
    border: none;
    background-color: transparent;
    font-size: 14px;
    color: #1C1C1C;
    padding-right: 24px;
    cursor: pointer;
}

.language-selector .form-select:focus {
    box-shadow: none;
    border: none;
}

body {
    background: var(--color-neutral-75_240, #F0F0F0);
}

.wrap_content {
    border-radius: var(--layout-spacing-spacing-xs, 8px);
    background: var(--surface-ghost, #FFF);
    margin-top: 32px;
    margin-bottom: 32px;
}

/* Container styles */
.expired-container {
    border-radius: 8px;
    padding: 48px;
}

.content {
    display: flex;
    justify-content: center;
    gap: 48px;
    align-items: center;
}

/* Text content */
.text-content {
    width: 310px;
    text-align: left;
}

.title {
    color: #1C1C1C;
    font-size: 24px;
    font-weight: 600;
    line-height: 36px;
    margin-bottom: 8px;
}

.description {
    color: #4A4A4A;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 0;
}

/* Image content */
.image-content {
    width: 320px;
    text-align: right;
}

.expired-icon {
    width: 100%;
    height: auto;
    max-width: 320px;
}

/* Responsive styles */
@media (max-width: 991.98px) {
    .content {
        flex-direction: column-reverse;
        gap: 32px;
    }

    .text-content,
    .image-content {
        width: 100%;
        text-align: center;
    }

    .expired-icon {
        width: 240px;
        height: auto;
    }
}
/* Container Styles */
.otp-container {
    max-width: 400px;
    margin: 0 auto;
    text-align: center;
    padding: 24px 0px;
}

/* Logo Styles */
.logo-container {
    margin-bottom: 16px;
}

.logo {
    height: 45px;
    width: auto;
    padding: 0;
}

/* Welcome Text */
.welcome-text {
    color: var(--text-headings, #1C1C1C);
    text-align: center;
    font-size: var(--Typeface-size-2xl, 24px);
    font-style: normal;
    font-weight: 600;
    line-height: 36px;
    margin-bottom: 24px;
}

/* OTP Input Styles */
.otp-form {
    width: 100%;
}

.otp-label {
    color: var(--text-body, #404040);
    text-align: center;
    font-size: var(--Typeface-size-lg, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 24px;
}

.otp-input-group {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-bottom: 24px;
}

.otp-input {
    width: 52px;
    height: 64px;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid var(--border-tertiary, #E6E6E6);
    background: var(--surface-ghost, #FFF);
    text-align: center;
    font-size: var(--Typeface-size-xl, 20px);
    font-weight: 400;
    color: #000;
    -moz-appearance: textfield;
    line-height: 28px;
}

/* Ẩn mũi tên tăng giảm của input number */
.otp-input::-webkit-outer-spin-button,
.otp-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.otp-input:focus {
    border: 1.5px solid var(--border-info, #2E6BE5);
    box-shadow: none;
    outline: none;
}

.otp-input.active {
    border: 1.5px solid var(--border-info, #2E6BE5);
}

/* Error Message */
.error-message {
    color: var(--text-danger, #D82039);
    text-align: center;
    font-family: 'Inter' ,sans-serif;
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    margin-top: -16px;
    margin-bottom: 24px;
}

.error-message.show {
    margin-top: -16px;
    margin-bottom: 24px;
    display: block !important;
}

/* Submit Button */
.login-btn {
    width: 100%;
    height: 48px;
    display: flex;
    padding: var(--layout-spacing-spacing-md, 12px) var(--layout-spacing-spacing-xxl, 24px);
    justify-content: center;
    align-items: center;
    gap: var(--layout-spacing-spacing-xs, 8px);
    align-self: stretch;
    border-radius: var(--layout-radius-radius-xs, 8px);
    background: var(--surface-action, #2E6BE5);
    color: white;
    font-size: 14px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-bottom: 16px;
}

.login-btn:disabled {
    border-radius: var(--layout-radius-radius-xs, 8px);
    background: var(--surface-action-disabled2, #E6E6E6);
    color: var(--text-on-action, #FFF);
    text-align: center;
    font-size: var(--Typeface-size-lg, 16px);
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    cursor: not-allowed;
}

.login-btn:not(:disabled):hover {
    background: var(--surface-action-hover, #2457B8);
}

.login-btn:focus {
    box-shadow: none !important;
    outline: none;
}

/* Reset Link */
.reset-link-wrapper {
    margin-top: 24px;
    text-align: center;
    color: var(--text-tertiary, #6C6C6C);
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
}

.reset-link {
    color: var(--text-action, #2E6BE5);
    font-weight: 600;
    text-decoration: none;
    line-height: 20px; /* 142.857% */
    font-size: var(--Typeface-size-md, 14px);
}

.reset-link:hover {
    text-decoration: underline;
    cursor: pointer;
}

/* Responsive Styles */
@media (max-width: 767.98px) {
    .otp-input-group {
        gap: 12px;
    }

    .otp-input {
        width: 44px;
        height: 56px;
        font-size: 14px;
    }
} 
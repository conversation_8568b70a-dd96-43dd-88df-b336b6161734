/* Login Form Styles */
.login-container {
    max-width: 413px;
}

/* Responsive styles */
@media (max-width: 767.98px) {
    .login-container {
        margin: 0 auto;
    }
}

@media (min-width: 768px) {
    .login-container {
        margin: 0;
    }
}

.logo {
    width: 160px;
    height: auto;
    padding: 11px 0px;
}

.welcome-text {
    color: #1C1C1C;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 36px;
}

.error-message {
    display: flex;
    align-items: center;
    font-family: 'Inter' ,sans-serif;
    gap: 8px;
    padding: 12px 16px;
    border-radius: 8px;
    background: var(--surface-error-light, #FEF3F2);
    color: var(--text-error, #D92D20);
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    border-radius: var(--layout-spacing-spacing-xs, 8px);
    border: 1px solid var(--theme-danger-lighter, #ED98A4);
    background: var(--surface-danger, #FFEFF0);
}

.error-message .error-icon {
    width: 20px;
    height: 20px;
}

.error-message span {
    color: var(--text-body, #404040);
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
}

.custom-input {
    height: 64px;
    color: #1C1C1C;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; 
    max-width: 413px;
}

.custom-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 82, 204, 0.1);
    outline: none;
}

.password-container,
.email-container {
    position: relative;
}

.password-toggle,
.clear-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    z-index: 5;
}

.eye-icon,
.clear-icon {
    width: 20px;
    height: 20px;
    transition: opacity 0.2s;
    color: #4A4A4A;
}

.password-toggle:hover .eye-icon,
.clear-toggle:hover .clear-icon {
    opacity: 1;
}

/* Ẩn nút clear khi input trống */
.email-container .clear-toggle {
    display: none;
}

.email-container input:not(:placeholder-shown) ~ .clear-toggle {
    display: block;
}

.forgot-password {
    text-align: left;
    margin-bottom: 32px;
}

.forgot-password p {
    color: var(--text-action, #2E6BE5);
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 500;
    line-height: 20px; 
}

.forgot-password :hover {
    cursor: pointer;
}

.login-btn {
    display: flex;
    padding: var(--layout-spacing-spacing-md, 12px) var(--layout-spacing-spacing-xxl, 24px);
    justify-content: center;
    align-items: center;
    gap: var(--layout-spacing-spacing-xs, 8px);
    align-self: stretch;
    border-radius: var(--layout-radius-radius-xs, 8px);
    background: var(--surface-action, #2E6BE5);
}

.login-btn:focus {
    box-shadow: none !important;
}

/* Language Selector */
.language-selector {
    text-align: left;
    margin-top: 12px;
}

.language-selector select {
    width: auto;
    min-width: 80px;
    height: 24px;
    border: 1px solid var(--border-tertiary, #E6E6E6);
    border-radius: 4px;
    padding: 0 0.5rem;
    font-weight: 400;
    color: #4A4A4A;
    background-color: var(--background-color);
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%234A4A4A' d='M6 8.825L1.175 4 2.238 2.938 6 6.7l3.763-3.763L10.825 4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    padding-right: 1.5rem;
    color: var(--text-body, #404040);
    text-align: center;
    font-size: var(--Typeface-size-sm, 12px);
    font-style: normal;
    font-weight: 400;
    line-height: 16px; 
    text-align: left;
}

.language-selector select:hover {
    border-color: #E6E6E6;
}

.language-selector select:focus {
    border-color: var(--border-tertiary, #E6E6E6);
    box-shadow: none;
    outline: none;
}

/* Illustration Container */
.illustration-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.illustration-container img {
    max-width: 100%;
    height: auto;
}

/* Form Floating Labels */
.form-floating {
    position: relative;
}

.form-floating > .form-control {
    height: 64px;
    padding: 30px 40px 8px 16px;
    font-weight: 400;
    font-size: 16px;
    max-width: 413px;
    color: var(--text-headings, #1C1C1C);
    font-size: var(--Typeface-size-lg, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
}

.form-floating > label {
    display: none;
    position: absolute;
    top: 11px;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 19px 12px;
    font-size: 14px;
    overflow: hidden;
    text-align: start;
    text-overflow: ellipsis;
    white-space: nowrap;
    pointer-events: none;
    transform-origin: 0 0;
    transition: opacity .1s ease-in-out, transform .1s ease-in-out;
    font-weight: 400;
    color: #1C1C1C;
    line-height: 20px;
}

.form-floating > .form-control:not(:focus){
    padding: 0px 40px 0px 16px;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    display: block;
    transform: scale(.85) translateY(-0.75rem) translateX(0.15rem);
    padding: 8px 16px;
}
/* Update password container for floating label */
.password-container .password-toggle {
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 5;
}

/* Hide placeholder when input is focused or has value */
.form-floating > .form-control:focus::placeholder,
.form-floating > .form-control:not(:placeholder-shown)::placeholder {
    opacity: 0;
    padding: 30px 40px 8px 16px;
}

/* Custom styling for the floating inputs */
.form-floating > .custom-input {
    border-radius: var(--layout-radius-radius-xs, 8px);
    border: 1px solid var(--border-primary, #DCDCDC);
    background: var(--surface-ghost, #FFF);
}

.form-floating > .custom-input:focus {
    padding: 30px 40px 8px 16px;
    border: 1.5px solid var(--border-info, #2E6BE5);
    box-shadow: none;
}

.form-floating > .form-control:not(:placeholder-shown){
    padding: 30px 40px 8px 16px;
}

.form-floating > .form-control:-webkit-autofill ~ label {
    display: block;
    transform: scale(.85) translateY(-0.75rem) translateX(0.15rem);
    padding: 8px 16px;
}
/* Loại bỏ background khi autocomplete */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 40px white inset !important;
    -webkit-text-fill-color: var(--text-headings, #1C1C1C) !important;
    background-color: white !important;
}

.invalid-password {
    border: 2px solid #ff0000 !important;
    outline: none;
}

.invalid-email {
    border: 2px solid #ff0000 !important;
    outline: none;
} 

.remember-me {
  cursor: pointer;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
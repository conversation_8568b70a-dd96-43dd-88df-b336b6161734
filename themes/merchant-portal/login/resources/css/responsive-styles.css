@media (max-width: 767.98px) {
    .container-responsive {
        padding: 0px;
        margin-left: 16px;
        margin-right: 16px;
    }
    .wrap_select_lang {
        width: 100%;
        height: 36px;
        border-bottom: 1px solid #E6E6E6;
        margin-bottom: 24px;
        background: #fff;
    }
    .custom-lang-selector {
        position: relative;
        top: 0;
        right: 0;
        float: right;
        bottom: 0;
        border-radius: none;
        border: none;
        background: transparent;
        width: auto;
    }
    .custom-lang-selector .lang-options {
        z-index: 1000;
        border-radius: 0px;
        position: fixed;
        bottom: 0px;
        width: 100%;
        top: auto;
    }

    .custom-lang-selector .lang-caret {
        display: none;
    }
    .custom-lang-selector .lang-options .lang-option {
        padding: 8px 0px;
        color: #404040;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 16px;
    }
    #langSelectedLabel {
        color: #404040;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 16px;
    }
    .logo-container {
        display: flex;
        align-items: center;
    }
    .logo-container .logo {
        padding: 0px;
    }
    .logo-container.height_logo_mobile{
        height: 52px;
    }
    .logo-container.center-logo {
        justify-content: center;
    }
    .container {
        display: block;
    }

    .align-items-center .col-md-6 {
        padding-left: 16px;
        padding-right: 16px;
    }

    .expired-container {
        padding: 24px 16px;
        border-radius: 0px;
    }

    .container-expired .wrap_select_lang {
        margin-bottom: 8px;
    }
    .container-expired .wrap_content {
        margin-bottom: 8px;
        border-radius: 0px;
        margin-top: 0px;
    }

    .expired-container .title {
        font-size: 20px;
        line-height: 28px;
    }
    .expired-container .description {
        font-size: 14px;
        line-height: 20px;
    }
    .header_select_lang_mobile {
        display: block;
        border-bottom: 1px solid var(--border-tertiary, #e6e6e6);
        font-weight: 600;
        margin-left: -16px;
        margin-right: -16px;
        padding-bottom: 8px;
        padding-left: 16px;
    }

    #overlay_bg {
        position: fixed;
        background: rgba(0, 0, 0, 0.5);
        width: 100%;
        height: 100%;
    }
}

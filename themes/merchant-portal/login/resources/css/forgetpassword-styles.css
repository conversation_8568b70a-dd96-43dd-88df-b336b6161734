/* Container Styles */
.forget-password-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 24px;
}

/* Logo Styles */
.logo-container {
    margin-bottom: 16px;
    text-align: center;
}

.logo {
    height: 45px;
    width: auto;
}

/* Welcome Text */
.welcome-text {
    color: var(--text-headings, #1C1C1C);
    text-align: center;
    font-size: var(--Typeface-size-2xl, 24px);
    font-style: normal;
    font-weight: 600;
    line-height: 36px;
    margin-bottom: 24px;
}

/* Instruction Text */
.instruction-text {
    color: var(--text-body, #404040);
    text-align: center;
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    margin-bottom: 12px;
}

/* Form Styles */
.form-group {
    margin-bottom: 24px;
}

.email-container {
    position: relative;
}


/* Ẩn nút clear khi input trống */
.email-container .clear-toggle {
    display: none;
}

.email-container input:not(:placeholder-shown) ~ .clear-toggle {
    display: block;
}

/* Form Floating Labels */
.form-floating {
    position: relative;
}

.form-floating > .form-control {
    height: 64px;
    padding: 30px 40px 8px 16px;
    font-weight: 400;
    font-size: 16px;
    max-width: 413px;
    color: var(--text-headings, #1C1C1C);
    font-size: var(--Typeface-size-lg, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
}

.form-floating > label {
    display: none;
    position: absolute;
    top: 11px;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 19px 12px;
    font-size: 14px;
    overflow: hidden;
    text-align: start;
    text-overflow: ellipsis;
    white-space: nowrap;
    pointer-events: none;
    transform-origin: 0 0;
    transition: opacity .1s ease-in-out, transform .1s ease-in-out;
    font-weight: 400;
    color: #1C1C1C;
    line-height: 20px;
}

.form-floating > .form-control:not(:focus){
    padding: 0px 40px 0px 16px;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    display: block;
    transform: scale(.85) translateY(-0.75rem) translateX(0.15rem);
    padding: 8px 16px;
}
/* Update password container for floating label */
.password-container .password-toggle {
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 5;
}

/* Hide placeholder when input is focused or has value */
.form-floating > .form-control:focus::placeholder,
.form-floating > .form-control:not(:placeholder-shown)::placeholder {
    opacity: 0;
    padding: 30px 40px 8px 16px;
}

.form-floating > .form-control:-webkit-autofill ~ label {
    display: block;
    transform: scale(.85) translateY(-0.75rem) translateX(0.15rem);
    padding: 8px 16px;
}
/* Custom styling for the floating inputs */
.form-floating > .custom-input {
    border-radius: var(--layout-radius-radius-xs, 8px);
    border: 1px solid var(--border-primary, #DCDCDC);
    background: var(--surface-ghost, #FFF);
}

.form-floating > .custom-input:focus {
    padding: 30px 40px 8px 16px;
    border: 1.5px solid var(--border-info, #2E6BE5);
    box-shadow: none;
}

.form-floating > .form-control:not(:placeholder-shown){
    padding: 30px 40px 8px 16px;
}

/* Error state styling */
.form-floating > .custom-input.error {
    border: 1.5px solid var(--border-danger, #D82039);
    background: var(--surface-ghost, #FFF);
}

.form-floating > .custom-input.error:focus {
    border: 1.5px solid var(--border-danger, #D82039);
    box-shadow: none;
}

/* Loại bỏ background khi autocomplete */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 40px white inset !important;
    -webkit-text-fill-color: var(--text-headings, #1C1C1C) !important;
    background-color: white !important;
} 

/* Clear Button */
.clear-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    display: none;
    z-index: 5;
}

.clear-toggle img {
    width: 20px;
    height: 20px;
}

.email-container:hover .clear-toggle,
.email-container:focus-within .clear-toggle {
    display: block;
}

/* Error Message */
.error-message {
    display: none;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: 8px;
    background: var(--surface-error-light, #FEF3F2);
    color: var(--text-error, #D92D20);
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    border-radius: var(--layout-spacing-spacing-xs, 8px);
    border: 1px solid var(--theme-danger-lighter, #ED98A4);
    background: var(--surface-danger, #FFEFF0);
    margin-bottom: 12px;
}

.error-message .error-icon {
    width: 20px;
    height: 20px;
}

.error-message span {
    color: var(--text-body, #404040);
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
}

.error-message.show {
    display: flex !important;
}

/* Small Error Message */
.error-message-small {
    display: none;
    align-items: center;
    gap: 4px;
    margin-top: 4px;
    color: var(--text-danger, #D82039);
    font-size: var(--Typeface-size-sm, 12px);
    font-weight: 400;
    line-height: 16px;
}

.error-message-small.show {
    display: flex !important;
}

.error-icon-small {
    width: 16px;
    height: 16px;
}

/* Submit Button */
.submit-btn {
    width: 100%;
    height: 48px;
    display: flex;
    padding: 12px 24px;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background: var(--surface-action, #2E6BE5);
    color: white;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    border: none;
    cursor: pointer;
}

.submit-btn:disabled {
    background: var(--surface-action-disabled2, #E6E6E6);
    cursor: not-allowed;
}

.submit-btn:not(:disabled):hover {
    background: var(--surface-action-hover, #2457B8);
}
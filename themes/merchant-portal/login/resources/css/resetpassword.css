body {
    /* min-height: 100vh; */
    /* display: flex; */
    /* flex-direction: column; */
    margin: 0;
}

/* Title Section */
/* .title-section { */
/*     background: #FFFFFF; */
/*     padding: 24px 0; */
/*     width: 100%; */
/* } */

/* Form Section */
/* .form-section { */
/*     flex: 1; */
/*     background: #F0F0F0; */
/*     padding: 48px 0; */
/* } */

/* Container Styles */
.forget-password-container {
    max-width: 400px;
    margin: 0 auto;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 22px;
}

.reset-password-container {
    /* max-width: 400px; */ /* Moved to .forget-password-container */
    /* margin: 0 auto; */ /* Moved to .forget-password-container */
    /* background: #FFFFFF; */ /* Moved to .forget-password-container */
    /* border-radius: 8px; */ /* Moved to .forget-password-container */
    /* padding: 22px; */ /* Moved to .forget-password-container */
    padding-top: 10px; /* Adjust padding for internal form spacing */
}

/* Page Title */
/* .page-title { */
/*     color: var(--text-headings, #1C1C1C); */
/*     text-align: center; */
/*     font-size: var(--Typeface-size-2xl, 24px); */
/*     font-style: normal; */
/*     font-weight: 600; */
/*     line-height: 36px; */
/*     margin: 0; */
/* } */

/* Welcome Text (for new h1) */
.welcome-text {
    color: var(--text-headings, #1C1C1C);
    text-align: center;
    font-size: var(--Typeface-size-2xl, 24px);
    font-style: normal;
    font-weight: 600;
    line-height: 36px;
    margin: 0 0 24px 0; /* Added margin-bottom for spacing */
}

/* Form Styles */
.form-group {
    margin-bottom: 24px;
}

.password-container {
    position: relative;
    width: 100%;
}

/* Form Floating Labels */
.form-floating {
    position: relative;
}

.form-floating > .form-control {
    height: 64px;
    padding: 30px 40px 8px 16px;
    font-weight: 400;
    font-size: 16px;
    max-width: 413px;
    color: var(--text-headings, #1C1C1C);
    font-size: var(--Typeface-size-lg, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
}

.form-floating > label {
    display: none;
    position: absolute;
    top: 8px;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0px 16px;
    font-size: 14px;
    overflow: hidden;
    text-align: start;
    text-overflow: ellipsis;
    white-space: nowrap;
    pointer-events: none;
    transform-origin: 0 0;
    transition: opacity .1s ease-in-out, transform .1s ease-in-out;
    font-weight: 400;
    color: #1C1C1C;
    line-height: 20px;
}

.form-floating > .form-control:not(:focus){
    padding: 0px 40px 0px 16px;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    display: block;
    transform: scale(.85) translateY(-0.75rem) translateX(0.15rem);
    padding: 8px 16px;
}
.form-floating > .form-control:focus::placeholder,
.form-floating > .form-control:not(:placeholder-shown)::placeholder {
    opacity: 0;
    padding: 30px 40px 8px 16px;
}
/* Custom styling for the floating inputs */
.form-floating > .custom-input {
    border-radius: var(--layout-radius-radius-xs, 8px);
    border: 1px solid var(--border-primary, #DCDCDC);
    background: var(--surface-ghost, #FFF);
}

.form-floating > .custom-input:focus {
    padding: 30px 40px 8px 16px;
    border: 1.5px solid var(--border-info, #2E6BE5);
    box-shadow: none;
}

.form-floating > .form-control:not(:placeholder-shown){
    padding: 30px 40px 8px 16px;
}

.form-floating > .form-control:-webkit-autofill ~ label {
    display: block;
    transform: scale(.85) translateY(-0.75rem) translateX(0.15rem);
    padding: 8px 16px;
}
/* Error state styling */
.form-floating > .custom-input.error {
    border: 1.5px solid var(--border-danger, #D82039);
    background: var(--surface-ghost, #FFF);
}

.form-floating > .custom-input.error:focus {
    border: 1.5px solid var(--border-danger, #D82039);
    box-shadow: none;
}

/* Password Toggle Button */
.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    z-index: 5;
}

.eye-icon {
    width: 20px;
    height: 20px;
    transition: opacity 0.2s;
    color: #4A4A4A;
}

.password-toggle:hover .eye-icon {
    opacity: 0.8;
}

/* Small Error Message */
.error-message-small {
    display: none;
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
}

.error-message-small.show {
    display: flex !important;
    align-items: center;
}

.error-message-small .error-icon-small {
    margin-right: 5px;
}

/* Make only confirm password error text red */
.form-group:last-of-type .error-message-small.show {
    color: #DC3545;
}

/* Submit Button */
.submit-btn {
    width: 100%;
    height: 48px;
    display: flex;
    padding: 12px 24px;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background: var(--surface-action, #2E6BE5);
    color: white;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    border: none;
    cursor: pointer;
    outline: none;
}

.submit-btn:disabled {
    background: var(--surface-action-disabled2, #E6E6E6);
    cursor: not-allowed;
}

.submit-btn:not(:disabled):hover {
    background: var(--surface-action-hover, #2457B8);
}

.submit-btn:focus {
    border: none;
    outline: none;
    box-shadow: none;
}

/* Loại bỏ background khi autocomplete */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 40px white inset !important;
    -webkit-text-fill-color: var(--text-headings, #1C1C1C) !important;
    background-color: white !important;
}

/* Toast Container */
.toast-container {
    position: fixed;
    bottom: 16px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
}

/* Error Toast */
.error-toast {
    display: none;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 4px;
    border-radius: var(--layout-spacing-spacing-xxs, 4px);
    background: var(--theme-danger-light, #E04D61);
}

.error-toast.show {
    display: flex !important;
}

.error-toast .error-icon {
    width: 24px;
    height: 24px;
}

.error-toast .error-message {
    color: #FFFFFF;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
}
.error-message {
    display: none;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: 8px;
    background: var(--surface-error-light, #FEF3F2);
    color: var(--text-error, #D92D20);
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    border-radius: var(--layout-spacing-spacing-xs, 8px);
    border: 1px solid var(--theme-danger-lighter, #ED98A4);
    background: var(--surface-danger, #FFEFF0);
    margin-bottom: 12px;
}

.error-message .error-icon {
    width: 20px;
    height: 20px;
}

.error-message span {
    color: var(--text-body, #404040);
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
}

.error-message.show {
    display: flex !important;
}
/* Responsive Styles */
@media (max-width: 767.98px) {
    /* .title-section { */
    /*     padding: 16px 0; */
    /* } */

    /* .form-section { */
    /*     padding: 24px 0; */
    /* } */

    .reset-password-container {
        /* padding: 24px 16px; */ /* Handled by parent container */
        /* margin: 0 16px; */ /* Handled by parent container */
    }

    .toast-container {
        bottom: 16px;
        left: 16px;
        right: 16px;
        transform: none;
    }

    .error-toast {
        width: 100%;
    }
}

.password-requirements {
    margin-top: 10px;
    /* margin-bottom: 15px; */ /* Already commented out */
    padding: 0;
}

.password-requirements ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.password-requirements li {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
}

.password-requirements li.valid {
    color: green;
    padding-left: 20px;
    background: url('../assets/v-valid.svg') no-repeat left top;
    background-size: 16px 16px;
}

.password-requirements li.invalid {
    color: #6c757d;
    padding-left: 20px;
    background: url('../assets/x-invalid.svg') no-repeat left top;
    background-size: 16px 16px;
}

.password-requirements li.valid::before {
    content: none;
}

.password-requirements li.invalid::before {
    content: none;
}

.center-logo {
    text-align: center;
    margin-bottom: 24px;
} 
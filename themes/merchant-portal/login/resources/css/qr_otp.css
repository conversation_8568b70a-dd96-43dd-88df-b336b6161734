/* QR Container Styles */
.qr-container {
    max-width: 400px;
    margin: 0 auto;
    text-align: center;
    padding: 24px 0px;
}

.logo-container {
    margin-bottom: 16px;
}

.logo-container .logo {
    height: 45px;
    width: auto;
    padding: 0;
}

.welcome-text {
    color: var(--text-headings, #1C1C1C);
    text-align: center;
    font-size: var(--Typeface-size-2xl, 24px);
    font-style: normal;
    font-weight: 600;
    line-height: 36px;
    margin-bottom: 16px;
}

.main-mobile-title {
    color: var(--text-headings, #1C1C1C);
    text-align: center;
    font-size: var(--Typeface-size-2xl, 24px);
    font-style: normal;
    font-weight: 600;
    line-height: 36px;
    margin-bottom: 16px;
}

.qr-code-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 24px;
}

.qr-code-wrapper {
    position: relative;
    width: 208px;
    height: 208px;
    border-radius: 8px;
    border: 1px solid var(--220_DCDCDC, #DCDCDC);
    display: flex;
    justify-content: center;
    align-items: center;
    background: white;
}

.qr-code {
    width: 187.2px;
    height: 187.2px;
    border-radius: 8px;
    object-fit: contain;
    margin: auto; /* Ensure image is centered within flex container */
}

.qr-icon {
    background-color: white;
    position: absolute;
    width: 40px;
    height: 40px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    padding: 4px;
}

.instruction-text {
    color: var(--text-body, #404040);
    text-align: center;
    font-size: var(--Typeface-size-lg, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
    margin-bottom: 40px; /* Confirmed at 40px */
}

.mobile-subtitle-text {
    color: var(--text-body, #404040);
    text-align: center;
    font-size: var(--Typeface-size-xl, 20px); /* Confirmed at 20px */
    font-style: normal;
    font-weight: 600; /* Confirmed at 600 */
    line-height: 28px; /* Confirmed at 28px */
    margin-bottom: 24px; /* Confirmed at 24px */
}

.logo-container .mobile-logo {
    height: 45px;
    width: auto;
    padding: 0;
}

/* OTP Input Styles */
.otp-form {
    width: 100%;
}

.otp-input-group {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-bottom: 24px;
}

.otp-input {
    width: 52px;
    height: 64px;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid var(--border-tertiary, #E6E6E6);
    background: var(--surface-ghost, #FFF);
    text-align: center;
    font-size: var(--Typeface-size-xl, 20px);
    font-weight: 400;
    color: #000;
    -moz-appearance: textfield;
    line-height: 28px;
}

/* Ẩn mũi tên tăng giảm của input number */
.otp-input::-webkit-outer-spin-button,
.otp-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.otp-input:focus {
    border: 1.5px solid var(--border-info, #2E6BE5);
    box-shadow: none;
    outline: none;
}

.otp-input.active {
    border: 1.5px solid var(--border-info, #2E6BE5);
}

/* Submit Button */
.login-btn {
    width: 100%;
    height: 48px;
    display: flex;
    padding: var(--layout-spacing-spacing-md, 12px) var(--layout-spacing-spacing-xxl, 24px);
    justify-content: center;
    align-items: center;
    gap: var(--layout-spacing-spacing-xs, 8px);
    align-self: stretch;
    border-radius: var(--layout-radius-radius-xs, 8px);
    background: var(--surface-action, #2E6BE5);
    color: white;
    font-size: 14px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.login-btn:disabled {
    border-radius: var(--layout-radius-radius-xs, 8px);
    background: var(--surface-action-disabled2, #E6E6E6);
    color: var(--text-on-action, #FFF);
    text-align: center;
    font-size: var(--Typeface-size-lg, 16px);
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    cursor: not-allowed;
}

.login-btn:not(:disabled):hover {
    background: var(--surface-action-hover, #2457B8);
}

.login-btn:focus {
    box-shadow: none !important;
    outline: none;
}

/* Responsive Styles */
@media (max-width: 767.98px) {
    .qr-container {
        padding: 24px 16px; /* Adjusted vertical padding for mobile */
    }

    .otp-input-group {
        gap: 12px;
    }

    .otp-input {
        width: 44px;
        height: 56px;
        font-size: 14px;
    }

    .logo-container .logo {
        height: 32px; /* Specific height for mobile logo */
    }
}

.error-message {
    color: var(--text-danger, #D82039);
    text-align: center;
    font-family: 'Inter' ,sans-serif;
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    margin-top: -16px;
    margin-bottom: 24px;
}

.error-message.show {
    margin-top: -16px;
    margin-bottom: 24px;
    display: block !important;
}

/* Styles to remove shadow from modal close button */
.modal-header .btn-close {
    box-shadow: none !important;
    outline: none !important;
}

.modal-header .btn-close:focus,
.modal-header .btn-close:active {
    box-shadow: none !important;
    outline: none !important;
}

/* Styles for close image interactivity */
.modal-header .btn-close {
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.modal-header .btn-close:hover {
    opacity: 0.7;
}

/* Styles for modal body steps and note orientation and sizing */
.modal-body .step {
    display: flex;
    align-items: center; /* Center items vertically */
    margin-bottom: 20px; /* Keep space between steps */
}

.modal-body .step-icon {
    width: 48px; /* Adjust size as needed */
    height: 48px; /* Adjust size as needed */
    margin-right: 15px; /* Space between icon and text */
    flex-shrink: 0; /* Prevent icon from shrinking */
}

.modal-body .alert-info {
    display: flex;
    align-items: center; /* Center items vertically */
    background-color: #EDF3FF; /* Set the background color */
    border: 1px solid #9FBBF3; /* Set the border color */
    border-radius: 8px; /* Add rounded corners */
    padding: 15px; /* Add padding inside the box */
    height: 56px; /* Keep fixed height */
    margin-bottom: 0px;
}

.modal-body .note-icon {
    width: 16.67px; /* Adjust size as needed */
    height: 16.67px; /* Adjust size as needed */
    margin-right: 15px; /* Space between icon and text */
    flex-shrink: 0; /* Prevent icon from shrinking */
}

.modal-body .step p {
    font-size: var(--Typeface-size-md, 14px); /* Set text size to md */
    margin-top: 0; /* Remove default top margin */
    margin-bottom: 0; /* Remove default bottom margin */
}

.modal-body .alert-info p {
    margin-bottom: 0; /* Remove default paragraph margin within alert */
    color: #404040; /* Ensure text color is consistent with body text */
    font-size: var(--Typeface-size-md, 14px); /* Set text size to md */
}

/* Style for the modal title */
.modal-header .modal-title {
    font-weight: bold;
}

.modal-content {
    border-radius: 16px; /* Apply border radius to the whole modal */
}

.modal-dialog {
    max-width: 480px; /* Increase max width for the modal */
    margin: auto; /* Center the modal */
}

.modal-body {
    padding-top: 24px; /* Set top padding to 24px */
    padding-bottom: 24px; /* Set bottom padding to 24px */
    padding-left: 24px; /* Keep left padding */
    padding-right: 24px; /* Keep right padding */
} 
/* Variables */
:root {
    --primary-color: #0052CC;
    --text-color: #333333;
    --border-color: #E5E5E5;
    --background-color: #FFFFFF;
    --font-family: 'Inter' ,sans-serif;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    height: 100%;
}

body {
    min-height: 100vh;
    margin: 0;
    display: flex;
    flex-direction: column;
    font-family: 'Inter' ,sans-serif;
    color: var(--text-color);
    background-color: var(--background-color);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Main content wrapper */
.container {
    width: 100%;
    flex: 1 0 auto;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

@media (min-width: 1200px) {
    .container {
        max-width: 1040px;
    }
}

/* Main content area */
.row {
    min-height: 100%;
    display: block;
    align-items: center;
    width: 100%;
    margin: 0;
}

.disabled-filter{
    color: gray;
  background: gray;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0px;
  opacity: 0.15;
  border-radius: var(--layout-radius-radius-xs, 8px);
}

/* Footer Styles */
.footer {
    padding: 50px 0 0 0;
    flex-shrink: 0;
    width: 100%;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.powered-logo {
    height: 22px;
    width: auto;
    margin-bottom: 8px;
}

.security-badges {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.security-badges img {
    height: 28px;
    width: auto;
    margin: 0px 10px;
    margin-bottom: 4px;
}

.security-badges img:hover {
    opacity: 1;
}

.footer-links-wrapper {
    background: var(--theme-neutral-lightest, #F5F5F5);
    width: 100%;
    padding-top: 6px;
    padding-bottom: 4px;
}

.footer-links {
    text-align: center;
    margin-bottom: 0;
}

.footer-links a {
    color: var(--Neutrals-42_108, #6C6C6C);
    text-align: center;
    font-size: 11px;
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-links .separator {
    display: inline-block;
    width: 1px;
    height: 12px;
    background-color: #8C8C8C;
    margin: 0 8px;
    vertical-align: middle;
}

.copyright-wrapper {
    background: var(--theme-neutral-lightest, #F5F5F5);
    width: 100%;
}

.copyright {
    text-align: center;
    color: var(--Neutrals-58_148, #949494);
    text-align: center;
    font-family: Helvetica Neue;
    font-size: 11px;
    font-style: normal;
    font-weight: 400;
    line-height: 15px; 
    padding-bottom: 6px;
    letter-spacing: 0%;
}

/* Utility classes */
.font-light {
    font-weight: 300 !important;
}

.font-regular {
    font-weight: 400 !important;
}

.font-medium {
    font-weight: 500 !important;
}

.font-semibold {
    font-weight: 600 !important;
}

.font-bold {
    font-weight: 700 !important;
} 

@media (max-width: 767.98px) {
    .language-selector {
        top: 16px;
        right: 16px;
    }

    .expired-container {
        padding: 32px 16px;
        margin: 0 16px;
    }

    .content {
        padding: 0;
    }

    .expired-icon {
        width: 160px;
    }

    .title {
        font-size: 20px;
        line-height: 30px;
    }

    .description {
        font-size: 14px;
        line-height: 20px;
    }
    .container {
        padding-left: 0px;
        padding-right: 0px;
    }
} 
.custom-lang-selector {
    background: #fff;
    width: 107px;
    height: 36px;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    border-radius: var(--layout-spacing-spacing-xxs, 4px);
    border: 1px solid var(--border-primary, #DCDCDC);
    position: absolute;
    float: right;
    top: 32px;
    bottom: 20px;
    right: 15px;
}

.lang-selected {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    height: 36px;
    cursor: pointer;
    font-size: 24px;
    font-weight: 500;
    user-select: none;
}

.lang-flag {
    width: 20px;
    height: auto;
    margin-right: 8px;
    object-fit: cover;
}

.lang-label {
    margin-right: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #404040;
    line-height: 20px;
}

.lang-caret {
    font-size: 28px;
    color: #404040;
    margin-left: auto;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.3s ease;
}

.caret-down.rotated {
    transform: rotate(180deg);
}

.lang-options {
    border-top: 1px solid #e0e0e0;
    position: absolute;
    top: 35px;
    left: 0;
    width: 100%;
    background-color: #fff;
    border-radius: var(--layout-spacing-spacing-xxs, 8px);
    box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.20);
    width: 180px;
    padding: 12px 16px;
}

.lang-option {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    cursor: pointer;
    transition: background 0.15s;

    font-size:  14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
}


/* .lang-option:hover {
    background: #f0f0f0;
} */
.icon_lang_checked{
    display: none;
    float: right;
    position: absolute;
    right: 16px;
}

.lang_checked .icon_lang_checked{
    display: block;
}

.header_select_lang_mobile {
    display: none;
}
#overlay_bg {
    display: none;
}

.contact-info{
    padding-top: 7px;
    font-family: Helvetica Neue;
    text-align: center; 
    color: #6C6C6C; font-size: 11px;
    line-height: 14px;
    font-weight: 400;
    font-style: normal;
}
.contact-info a:hover {
    color: #2E6BE5;
    text-decoration: underline;
    cursor: pointer;
}
.contact-info a {
    color: #2E6BE5;
    text-decoration: none;
}


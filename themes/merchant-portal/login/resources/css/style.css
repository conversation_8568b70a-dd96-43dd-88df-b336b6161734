* {
    margin:0;
    padding:0;
}

html,body {
    font-size: 12px;
    background-image: url("../assets/backgroud.png");
    background-size: cover;
    height: 100%;
    background-color: #ffffff;
}


.lg_text{
    color: #00529c;
    font-size: 12px;
}

.footer{
    height: 50px;
    background-color: #f1f1f1;
    width: 100%;
}

.full_width{
    width: 100%;
}

.fix_height{
    height: 28px;
    margin-top: 14px;
}

.top_margin{
    padding-top: 42px;
}

.body_margin{
    padding-top: 66px;
}

.margin_form{
    padding-top: 0px;
}

.font-black {
    color: #000000;
}

.margin_submit{
    padding-top: 22px;
}

.margin_copy{
    padding-top: 10px;
}

.btn_login{
    width: 100%;
    height: 60px;
    background-color: #00529c;
    color: #f1f1f1;
    font-size: 18px;
}

.border_login{
    border: 1px solid #fafafa;
    border-radius: 6px;
    padding-left: 16px;
    padding-right: 16px;
    background-color: #ffffff;
    height: 450px;
    width: 353px;
    margin-left: 11%;
    box-shadow: 0px 10px 4px;
    color: #fafafa;
}

.change_pass{
    color:#00529c;
    font-size: 12px;
	cursor: pointer;
}

.lg_text{
    color: #00529c;
    font-size: 12px;
}

.text_note{
    font-size: 12px;
}

.form-control{
    display: block;
    width: 100%;
    padding: 10px 0px 14px 10px;
    font-size: 1.2rem;
    line-height: 1.5;
    background-color: #ffffff;
    background-clip: padding-box;
    border: 2px solid #bbbbbb;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    color: #bbbbbb;
    border-left: none;
    border-right: none;
    border-top: none;
    border-radius: 0px;
}

.form-control::-webkit-input-placeholder { color: #bbbbbb; }  /* WebKit, Blink, Edge */
.form-control:-moz-placeholder { color: #bbbbbb; }  /* Mozilla Firefox 4 to 18 */
.form-control::-moz-placeholder { color: #bbbbbb; }  /* Mozilla Firefox 19+ */
.form-control:-ms-input-placeholder { color: #bbbbbb; }  /* Internet Explorer 10-11 */
.form-control::-ms-input-placeholder { color: #bbbbbb; }  /* Microsoft Edge */


.wrapper{
   min-height: 94%;
}

.text_title{
    font-size: 18px;
    color: #00529C;
    padding-top: 40px;
    font-weight: 500;
}

.border_error_page{
    padding-left: 16px;
    height: 450px;
    width: 353px;
    margin-left: 11%;
    color: #fafafa;
}

/* animation modal */
@keyframes zoomIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.modal.fade .modal-dialog {
    transform: scale(0.9);
    opacity: 0;
    transition: all 0.3s ease-in-out;
}

.modal.show .modal-dialog {
    transform: scale(1);
    opacity: 1;
    animation: zoomIn 0.3s ease;
}

.modal-dialog {
    max-width: 480px;
    width: 90%;
    margin: 1.5rem auto;
}

.modal-content {
    border-radius: 12px;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    border: none;
    background-color: #fff;
}

.modal-header {
    background-color: #417ab6;
    color: white;
    padding: 16px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.modal-body {
    font-size: 16px;
    padding: 20px;
    color: #333;
    word-wrap: break-word;
}

.modal-footer {
    padding: 16px 20px;
    border-top: none;
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.modal-footer .btn {
    flex: 1;
    padding: 12px;
    font-weight: 600;
    border-radius: 6px;
}

.modal-footer .btn-primary {
    background-color: #417ab6;
    border-color: #417ab6;
    color: white;
}

.modal-footer .btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* button close */
.modal-header .btn-close {
    background: none;
    border: none;
    font-size: 24px;
    line-height: 1;
    color: #ffffff;
    opacity: 0.85;
    padding: 0 8px;
    margin-left: auto;
    cursor: pointer;
    transition: transform 0.2s ease, opacity 0.2s ease;
}

.modal-header .btn-close:hover {
    opacity: 1;
    transform: scale(1.2);
    color: #000;
}

.modal-header .btn-close:focus {
    outline: none;
    box-shadow: none;
}

/* Responsive */
@media (max-width: 576px) {
    .modal-footer {
        flex-direction: column-reverse;
        align-items: stretch;
    }

    .modal-footer .btn {
        width: 100%;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 16px;
    }
}

/* spinner loading */
.spinner-border {
    animation: spin 1s linear infinite;
}

.spinner-border {
    display: inline-block;
    width: 3rem;
    height: 3rem;
    vertical-align: text-bottom;
    border: 0.25em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    -webkit-animation: spinner-border 0.75s linear infinite;
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    100% {
        transform: rotate(360deg);
    }
}
/* end spinner loading */

.modal-dialog.modal-sm {
    max-width: 360px;
}
  
.modal-content {
    border-radius: 12px;
    border: none;
    animation: fadeIn 0.4s ease;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}
  
.modal-header {
    background: linear-gradient(90deg, #417ab6 0%, #417ab6 100%);
    border-bottom: none;
}
  
.modal-body {
    font-family: sans-serif;
}
  
@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}
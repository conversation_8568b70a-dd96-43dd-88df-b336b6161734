function includeHTML(callback) {
    const elements = document.querySelectorAll('[data-include]');
    let loaded = 0;
    elements.forEach(el => {
      const file = el.getAttribute('data-include');
      fetch(file)
        .then(res => {
          if (!res.ok) throw new Error(`HTTP ${res.status}`);
          return res.text();
        })
        .then(data => {
          el.innerHTML = data;
          loaded++;
          if (loaded === elements.length && callback) callback();
        })
        .catch(err => {
          console.error(`Error loading ${file}:`, err);
          el.innerHTML = `<div style="color:red;">Lỗi tải ${file}</div>`;
        });
    });
}
  
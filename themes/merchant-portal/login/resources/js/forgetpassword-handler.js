// Keycloak Forgot Password Script - Match OneAM behavior
console.log('Forgot password script loaded');

// Email validation regex (same as OneAM)
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.getElementById('username');
    const submitBtn = document.getElementById('kc-submit');
    const clearButton = document.querySelector('.clear-toggle');
    const errorMessageSmall = document.querySelector('.error-message-small');
    const form = document.getElementById('kc-reset-password-form');
    
    if (!emailInput || !submitBtn) {
        console.error('Email input or submit button not found!');
        return;
    }
    
    // Initially disable submit button
    submitBtn.disabled = true;
    
    // Function to validate email format
    function validateEmail(email) {
        return EMAIL_REGEX.test(email.trim());
    }
    
    // Function to toggle clear button visibility
    function toggleClearButton() {
        if (clearButton) {
            if (emailInput.value.length > 0) {
                clearButton.style.display = 'block';
            } else {
                clearButton.style.display = 'none';
            }
        }
    }
    
    // Function to check email and enable/disable submit button
    function checkEmail() {
        const email = emailInput.value.trim();
        
        if (email === '') {
            submitBtn.disabled = true;
            if (errorMessageSmall) {
                errorMessageSmall.style.display = 'none';
            }
            return;
        }
        
        if (!validateEmail(email)) {
            // Show "Wrong email format" error
            submitBtn.disabled = true;
            if (errorMessageSmall) {
                errorMessageSmall.style.display = 'flex';
            }
        } else {
            // Valid email format, enable submit
            submitBtn.disabled = false;
            if (errorMessageSmall) {
                errorMessageSmall.style.display = 'none';
            }
        }
    }
    
    // Email input event
    emailInput.addEventListener('input', function() {
        toggleClearButton();
        checkEmail();
    });
    
    // Clear button click event
    if (clearButton) {
        clearButton.addEventListener('click', function() {
            emailInput.value = '';
            emailInput.focus();
            toggleClearButton();
            checkEmail();
        });
    }
    
    // Initialize
    toggleClearButton();
    checkEmail();
});


$(document).ready(function () {

  $("#myform").submit(function (event) {
    event.preventDefault();
    login();
  });

  $("#otpform").submit(function (event) {
    event.preventDefault();
    submitOtp();
  });

  $("#qrform").submit(function (event) {
    event.preventDefault();
    submitQr();
  });

  function login() {
    document.getElementById("login").disabled = true;
    var email_input = $("#email").val();
    var password_input = $("#password").val();
    loginStep1(email_input, password_input, loginStep2);
  }

  function submitOtp() {
    var url_string = window.location.href;
    var clientRequestId = getAllUrlParams(url_string).clientRequestId;
    var otp = $("#verificationCode").val();

    var sequenceId = 0;
    var shaObj = new jsSHA(otp, "TEXT");
    var _otpHash = shaObj.getHMAC(
      "23BF1DC999734E6A6860BD56273AA455",
      "HEX",
      "SHA-256",
      "HEX"
    );
    $.ajax({
      type: "GET",
      beforeSend: function (request) {
        request.setRequestHeader("X-Client-Request-Id", clientRequestId);
        request.setRequestHeader("Content-Type", "application/json");
        request.setRequestHeader("Accept", "application/json");
        request.setRequestHeader("X-Language", getCurrentLanguage());
        request.setRequestHeader(
          "X-Request-Id",
          CryptoJS.SHA256(sequenceId + "" + new Date().getTime())
            .toString()
            .substr(0, 32)
            .toUpperCase()
        );

        sequenceId += 1;
      },
      dataType: "json",
      // url:
      //   "/accounts/api/v1/oauth2/auth?otpHash=" +
      //   _otpHash.toString().toUpperCase(),
      url: 
      "/accounts/api/v1/users/"+
      _otpHash.toString().toUpperCase()
      + "/otps/channels",
      processData: false,
      success: function (msg) {
        // $("#results").append("The result =" + StringifyPretty(msg));
        console.log(msg);
        window.location = msg.redirect;
      },
      error: function (failMsg) {
        console.log(failMsg);
        var error = translations[getCurrentLanguage()]["SystemError"];
        if (failMsg && failMsg.responseJSON && failMsg.responseJSON.responseDescription !== undefined) {
          error = failMsg.responseJSON.responseDescription;
        }
        // Show error message
        $('.error-message span').text(error);
        $('.error-message').show();
        document.getElementById("login").disabled = false;
      },
    });
  }

  function loginStep2(message) {
    $.ajax({
      type: "GET",
      beforeSend: function (request) {
        request.setRequestHeader(
          "X-Client-Request-Id",
          message.clientRequestId
        );
        request.setRequestHeader("Content-Type", "application/json");
        request.setRequestHeader("Accept", "application/json");
        request.setRequestHeader("X-Language", getCurrentLanguage());
      },
      dataType: "json",
      url: "api/v1/oauth2/authorize?confirm=true",
      processData: false,
      success: function (msg) {
        // $("#results").append("The result =" + StringifyPretty(msg));
        console.log(msg);
        window.location = msg.redirect;
      },
      error: function (failMsg) {
        console.log(failMsg);
        alert(failMsg.responseJSON.responseDescription);
        document.getElementById("login").disabled = false;
      },
    });
  }

  function loginStep1(user, password, callback) {
    var username = user
      ? user.replace(
          /^(\+?84|0)?((3[0-9]|4[0-9]|5[0-9]|7[0-9]|8[0-9]|9[0-9]|12[0-9]|16[0-9]|18[68]|199)\d{7})$/,
          "84$2"
        )
      : user;

    var shaObj = new jsSHA(password, "TEXT");
    var hmac = shaObj.getHMAC(
      "23BF1DC999734E6A6860BD56273AA455",
      "HEX",
      "SHA-256",
      "HEX"
    );
    var url_string = window.location.href;
    console.log(getAllUrlParams(url_string));
    var c = getAllUrlParams(url_string).continue;

    // TEST ENVIRONMENT
    //var data = {
    //  continue: 'http%3A%2F%2Foneam.onepay.vn%2F%3FresponseType%3Dcode%26scope%3Dprofile%26redirectUri%3Dhttps%3A%2F%2Fma-mtf.onepay.vn%2Flogin%26clientId%3DONEMADM%26authLevel%3D1'
    //}

     //PRODUCTION ENVIRONMENT
    var data = {
      continue: 'http%3A%2F%2Foneam.onepay.vn%2F%3FresponseType%3Dcode%26scope%3Dprofile%26redirectUri%3Dhttps%3A%2F%2Fma.onepay.vn%2Flogin%26clientId%3DONEMADM%26authLevel%3D1'
    }
    if (c) {
      data = {
        continue: c
      }
    } 
    // DEV ENVIRONMENT
    //data = {
    //  continue:
    //    "http%3A%2F%2Foneam.onepay.vn%2F%3FresponseType%3Dcode%26scope%3Dprofile%26redirectUri%3Dhttps%3A%2F%2Fdev.onepay.vn%2Fma%2Flogin%26clientId%3DONEMADM%26authLevel%3D1",
    //};
    //if(c != undefined) {
    //	data = {
    //		continue: c
    //	}
    //}
   /* var SMPstatus ;
   
	*/
    var self = this;
    if(document.getElementById("login").disabled)
    $.ajax({ 
      type: "POST",
      beforeSend: function (request) {
        request.setRequestHeader("X-Username", username);
        request.setRequestHeader("X-Password", hmac.toString().toUpperCase());
        request.setRequestHeader("Content-Type", "application/json");
        request.setRequestHeader("Accept", "application/json");
        request.setRequestHeader("X-Language", getCurrentLanguage());
      }, 
      dataType: "json",
      url: "api/v1/oauth2/auth/v2",
      data: JSON.stringify(data),
      processData: false,
      success: function (msg) {
        // $("#results").append("The result =" + StringifyPretty(msg));
        if (msg.nextState !== undefined && msg.nextState === "otp") {
          processLogin1OTP( msg.clientRequestId, msg.user.id, msg.user.profile.email);
         
        } else {
          callback(msg);
        }
      },
      error: function (failMsg) {
        console.log(failMsg);
        var error = translations[getCurrentLanguage()]["SystemError"];
        if (failMsg && failMsg.responseJSON && failMsg.responseJSON.responseDescription !== undefined) {
          error = failMsg.responseJSON.responseDescription;
        }
        // Show error message
        $('.error-message span').text(error);
        $('.error-message').show();
        document.getElementById("login").disabled = false;
      },
    });
  }
  
  

  function processLogin1OTP(clientRequestId, userId, email) { 
    
    $.ajax({
      type: "GET",
      beforeSend: function (request) {
        request.setRequestHeader("X-Client-Request-Id", clientRequestId);
        request.setRequestHeader("Content-Type", 'application/json');
        request.setRequestHeader("Accept", 'application/json');
        request.setRequestHeader("X-Language", getCurrentLanguage());
      },
      dataType: 'json',
      url: "/accounts/api/v1/users/"+userId+"/otps/channels",
      processData: false,
      success: function (msg) {
          // if (checkIfSMPActive(msg.otpChannels)) {
          //   console.log('===in page otp');
            
          //   window.location.href = "otp?clientRequestId=" + clientRequestId+"&userId=" + userId;
          // } else {
          //   console.log('===in page qr');
            
          //   window.location.href = "qr?userId=" + userId;
          // }


        let isFromEmail = false;
        let redirectUrl = `qr?userId=${userId}&email=${email}&clientRequestId=${clientRequestId}`;
        // check params from email
        const token = new URLSearchParams(window.location.search).get("token");
        if (token) {
          // parse
          const parts = token.split('.');
          const base64Url = parts[0];

          // convert base64url → base64
          const base64 = base64Url
            .replace(/-/g, '+')
            .replace(/_/g, '/')
            + '='.repeat((4 - base64Url.length % 4) % 4);

          // encode base64 to []byte
          const binaryStr = atob(base64);
          const byteArray = Uint8Array.from(binaryStr, c => c.charCodeAt(0));

          // convert to UTF-8 string
          const decodedPayload = new TextDecoder('utf-8').decode(byteArray);

          console.log('Decoded payload:', decodedPayload);
          if (decodedPayload) {
            const valuesPayload = decodedPayload.split('|');
            if (valuesPayload.length === 4 && valuesPayload[0] === userId && valuesPayload[2] >= Date.now()) {
              redirectUrl = `${redirectUrl}&token=${token}`;
              isFromEmail = true;
            }
          }
        }

        if (!isFromEmail && checkIfSMPActive(msg.otpChannels)) {
          redirectUrl = `otp?clientRequestId=${clientRequestId}&userId=${userId}&email=${email}`;
        }

        window.location.href = redirectUrl;
      },
      error: function (failMsg) {
        var error = translations[getCurrentLanguage()]["SystemError"];
        if (failMsg && failMsg.responseJSON && failMsg.responseJSON.responseDescription !== undefined) {
          error = failMsg.responseJSON.responseDescription;
        }
        // Show error message
        $('.error-message span').text(error);
        $('.error-message').show();
        document.getElementById("login").disabled = false;
      }
    });
  }
  
  function checkIfSMPActive(channels) {
			let result = false;
			channels.forEach(a => {
				if(a.channel === 'SMP' &&  a.status==="A") result = true;
			})
			return result;
  }

  function getAllUrlParams(url) {
    // get query string from url (optional) or window
    var queryString = url ? url.split("?")[1] : window.location.search.slice(1);

    // we'll store the parameters here
    var obj = {};

    // if query string exists
    if (queryString) {
      // stuff after # is not part of query string, so get rid of it
      queryString = queryString.split("#")[0];

      // split our query string into its component parts
      var arr = queryString.split("&");

      for (var i = 0; i < arr.length; i++) {
        // separate the keys and the values
        var a = arr[i].split("=");

        // in case params look like: list[]=thing1&list[]=thing2
        var paramNum = undefined;
        var paramName = a[0].replace(/\[\d*\]/, function (v) {
          paramNum = v.slice(1, -1);
          return "";
        });

        // set parameter value (use 'true' if empty)
        var paramValue = typeof a[1] === "undefined" ? true : a[1];

        // if parameter name already exists
        if (obj[paramName]) {
          // convert value to array (if still string)
          if (typeof obj[paramName] === "string") {
            obj[paramName] = [obj[paramName]];
          }
          // if no array index number specified...
          if (typeof paramNum === "undefined") {
            // put the value on the end of the array
            obj[paramName].push(paramValue);
          }
          // if array index number specified...
          else {
            // put the value at that index number
            obj[paramName][paramNum] = paramValue;
          }
        }
        // if param name doesn't exist yet, set it
        else {
          obj[paramName] = paramValue;
        }
      }
    }

    return obj;
  }
  window.forgotPassword = function () {
    window.location.href = '/accounts/forgetpassword/';
  };
});

// Function to validate password length
function validatePasswordInput(input) {
    const minLength = 1;
    if (input.value.length < minLength) {
        input.classList.add('invalid-password');
    } else {
        input.classList.remove('invalid-password');
    }
}

// Password toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password');
    const toggleButton = document.querySelector('.password-toggle');
    const toggleIcon = toggleButton.querySelector('img');
    const errorMessage = document.querySelector('.error-message');
    
    // Add password validation
    passwordInput.addEventListener('input', function() {
        validatePasswordInput(passwordInput);
        errorMessage.style.display = 'none';
    });
    
    toggleButton.addEventListener('click', function() {
        // Toggle password visibility
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        
        // Toggle icon
        const iconSrc = type === 'password' ? 'assets/eye-off.svg' : 'assets/eye.svg';
        toggleIcon.setAttribute('src', iconSrc);
        
        // Update aria-label for accessibility
        toggleIcon.setAttribute('alt', type === 'password' ? 'Show password' : 'Hide password');
    });
});

// Function to validate email input (no Vietnamese characters and check length)
function validateEmailInput(input) {
    if (input.value.trim() !== '') {
        // Remove Vietnamese characters and diacritics
        const vietnameseChars = /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/g;
        const normalized = input.value.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
        const withoutVietnamese = normalized.replace(vietnameseChars, '');
        
        if (input.value !== withoutVietnamese) {
            input.value = withoutVietnamese;
        }
        // Check length only if not empty
        const minLength = 1;
        if (input.value.length < minLength) {
            input.classList.add('invalid-email');
        } else {
            input.classList.remove('invalid-email');
        }
    } else {
        // Remove invalid class if empty
        input.classList.remove('invalid-email');
    }
}

// Email clear button functionality
document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const clearButton = document.querySelector('.clear-toggle');
    const errorMessage = document.querySelector('.error-message');
    
    // Hàm kiểm tra và cập nhật trạng thái hiển thị của nút clear
    function toggleClearButton() {
        if (emailInput.value.length > 0) {
            clearButton.style.display = 'block';
        } else {
            clearButton.style.display = 'none';
        }
    }

    // Kiểm tra khi input thay đổi
    emailInput.addEventListener('input', function() {
        validateEmailInput(emailInput);
        toggleClearButton();
        errorMessage.style.display = 'none'; // Ẩn error message khi nhập email
    });
    
    // Ẩn error message khi nhập password
    passwordInput.addEventListener('input', function() {
        errorMessage.style.display = 'none';
    });
    
    // Xử lý sự kiện click vào nút clear
    clearButton.addEventListener('click', function() {
        emailInput.value = '';
        emailInput.focus();
        toggleClearButton();
        errorMessage.style.display = 'none'; // Ẩn error message khi clear email
    });

    // Khởi tạo trạng thái ban đầu
    toggleClearButton();
});

// Function to check login fields and control button state
function checkLoginFields() {
    const email = document.getElementById('email');
    const password = document.getElementById('password');
    const loginBtn = document.getElementById('login');
    if (!email || !password || !loginBtn) return; // Safety check

    const isEmailValid = !email.classList.contains('invalid-email');
    const isPasswordValid = !password.classList.contains('invalid-password');
    const hasEmailValue = email.value.trim() !== '';
    const hasPasswordValue = password.value.trim() !== '';
    // Enable button only if both fields are valid and have values
    if (hasEmailValue && hasPasswordValue && isEmailValid && isPasswordValid) {
        loginBtn.disabled = false;
    } else {
        loginBtn.disabled = true;
    }
}

// Add event listeners for input fields
document.addEventListener('DOMContentLoaded', function() {
    const email = document.getElementById('email');
    const password = document.getElementById('password');
    if (email && password) {
        email.addEventListener('input', checkLoginFields);
        password.addEventListener('input', checkLoginFields);
        checkLoginFields(); // Initial check
    }
});

$(document).ready(function () {

  function handleSessionUnauthorizedOtp(failMsg) {
    if (
      failMsg &&
      failMsg.responseJSON &&
      failMsg.responseJSON.responseCode === "ONEAM_USER_SESSION_UNAUTHOR.401"
    ) {
      window.location.href = '/accounts/';
      return true;
    }
    return false;
  }
  
  function handleSessionExpired(failMsg) {
    if (
      failMsg &&
      failMsg.responseJSON &&
      failMsg.responseJSON.responseCode === "ONEAM_SESSION_EXPIRED.401"
    ) {
      window.location.href = '/forgetpassword/linkexpired.html';
      return true;
    }
    return false;
  }

  function handleTokenExpired(failMsg) {
    if (
      failMsg &&
      failMsg.responseJSON &&
      failMsg.responseJSON.responseCode === "ONEAM_TOKEN_EMAIL_EXPIRED.401"
    ) {
      window.location.href = '/accounts/error_page/token_expire.html';
      return true;
    }
    return false;
  }
  // Get URL parameters on load
  var url_string = window.location.href;
  var urlParams = getAllUrlParams(url_string);
  const token = new URLSearchParams(window.location.search).get("token");

  // Check if email parameter exists and display it
  if (urlParams.email) {
    $('#userEmail').text(decodeURIComponent(urlParams.email)); // Decode URI component just in case
  }

  let isMobile = detectMobile();
  const desktopView = $('.desktop-view');
  const mobileView = $('.mobile-view');

  function updateView() {
    isMobile = detectMobile();
    if (isMobile) {
      desktopView.hide();
      mobileView.show();
    } else {
      desktopView.show();
      mobileView.hide();
    }
  }

  updateView(); // Initial view update

  // Add event listener for window resize to handle responsiveness
  $(window).on('resize', function() {
    updateView();
  });
  getChannels(getQr);
  // performTokenVerification();
  // Hide error message when user starts typing new OTP
  $('.otp-input').on('input', function() {
    $('.error-message').removeClass('show');
  });
    // enter 
    $('.otp-input').keydown(function(event) {
      const submitBtn = document.getElementById('submit-btn'); // or your submit button's id
      if (event.keyCode === 13 && submitBtn.disabled == false) {
        submitOtp();
      }
  });
  $("#otpform").submit(function (event) {
    event.preventDefault();
    submitOtp();
  });

  // Add event listener to open the modal
  $('#howToGetCodeLink').on('click', function(e) {
      e.preventDefault(); // Prevent the default link behavior
      $('#howToGetCodeModal').modal('show'); // Show the modal
  });

  // Add click listener to the close image to close the modal
  $('.modal-header .btn-close').on('click', function() {
      $('#howToGetCodeModal').modal('hide');
  });

  // Add click listener for back to login link (for mobile view)
  $('#backToLoginLink').on('click', function(e) {
    e.preventDefault();
    window.location.href = '/accounts/'; // Redirect to login page
  });
  // Performs the token verification AJAX call
  function performTokenVerification() {
    return new Promise((resolve, reject) => {
      if (token) {
        $.ajax({
          url: "/accounts/api/v1/verify-token?token=" + token, // Token will be in the POST body
          method: "POST",
          beforeSend: function (request) {
            request.setRequestHeader("X-Language", getCurrentLanguage());
          },
          contentType: "application/json",
          dataType: "json",
          data: JSON.stringify({ token: token, action: "reset_qr_otp" }),
          success: function (res) {
            console.log("link still avaiable")
            getChannels(getQr);
            resolve(res);
          },
          error: function (failMsg, textStatus) {
            if (textStatus === 'abort') {return}
            else if (handleTokenExpired(failMsg)) {console.log(failMsg); return reject(failMsg) } else {
              window.location.href = '/accounts/';
            }
          }
        });
      } else {
        getChannels(getQr);
        resolve();
      }
    });
  }

  function getChannels(getQrCallBack) {
    var url_string = window.location.href;
    var userId = getAllUrlParams(url_string).userId;
    var self = this;
    $.ajax({
      type: "GET",
      beforeSend: function (request) {
        request.setRequestHeader("Content-Type", "application/json");
        request.setRequestHeader("Accept", "application/json");
        request.setRequestHeader("X-Language", getCurrentLanguage());
      },
      dataType: "json",
      url: "/accounts/api/v1/users/" + userId + "/otps/channels",
      processData: false,
      success: function (msg) {
        getQrCallBack();
      },
      error: function (failMsg, textStatus) {
        if (textStatus === 'abort') {return} else 
        // if (handleSessionExpired(failMsg)) { return } else if (handleSessionUnauthorizedOtp(failMsg)) { return } else 
        {
          console.log(failMsg);
          var error = translations[getCurrentLanguage()]["SystemError"];

          if (
            failMsg.responseJSON != undefined &&
            failMsg.responseJSON.responseDescription !== undefined
          ) {
            error = failMsg.responseJSON.responseDescription;
          }
          if (failMsg.responseJSON != undefined && failMsg.responseJSON.responseCode === "ONEAM_USER_SESSION_UNAUTHOR.401") {

          }
          $('.error-message').text(error).addClass('show');
        }
      },
    });
  }
  function getQr() {
    var url_string = window.location.href;
    var userId = getAllUrlParams(url_string).userId;
    var data = { default: 0 };
    var self = this;
    $.ajax({
      type: "POST",
      beforeSend: function (request) {
        request.setRequestHeader("Content-Type", "application/json");
        request.setRequestHeader("Accept", "application/json");
        request.setRequestHeader("X-Language", getCurrentLanguage());
      },
      dataType: "json",
      url: "/accounts/api/v1/users/" + userId + "/otps/channels/SMP",
      data: JSON.stringify(data),
      processData: false,
      success: function (msg) {
        // var qrcode = new QRCode("id-qrcode", {
        //   text:msg.qrCode,
        //   // width:200,
        //   // height:200,
        //   colorDark:"#000000",
        //   colorLight:"#ffffff",
        //   correctLevel:QRCode.CorrectLevel.L
        // });
        // console.log(qrcode);
        // $("#qrCode").attr("src", "data:image/png;base64," + msg.qrCode);
        newQR("qrCode", msg.secretURL)
        self.otpId = msg.otpId;
        submitBtn.disabled = false;
      },
      error: function (failMsg, textStatus) {
        if (textStatus === 'abort') {return} else
        //  if (handleSessionExpired(failMsg)) { return } else if (handleSessionUnauthorizedOtp(failMsg)) { return } else 
         {
          console.log(failMsg);
          submitBtn.disabled = false;
          var error = translations[getCurrentLanguage()]["SystemError"];

          if (
            failMsg.responseJSON != undefined &&
            failMsg.responseJSON.responseDescription !== undefined
          ) {
            error = failMsg.responseJSON.responseDescription;
          }
          $('.error-message').text(error).addClass('show');
        }
      },
    });
  }

  let otpLockoutTimer = null;
  let otpLockoutSeconds = 30;
  
  const submitBtn = document.getElementById('submit-btn'); // or your submit button's id
  const timer = document.getElementById("otp-timer");
  function lockOtpSubmit() {
    let secondsLeft = otpLockoutSeconds;
    submitBtn.disabled = true;
    timer.textContent = `(${secondsLeft}s)`;
    otpLockoutTimer = setInterval(() => {
        secondsLeft--;
        timer.textContent = `(${secondsLeft}s)`;
        if (secondsLeft <= 0) {
            clearInterval(otpLockoutTimer);
            timer.textContent = ``;
            submitBtn.disabled = false;
        }
    }, 1000);
}

function decodeEscapedOtpAuthUri(escapedString) {
  // First, replace \u003d (which is '='), and similar escaped unicode
  const jsonDecoded = escapedString.replace(/\\u([\dA-F]{4})/gi, (_, g1) =>
    String.fromCharCode(parseInt(g1, 16))
  );

  // Then decode URI components like %40 (which is '@')
  const decodedUri = decodeURIComponent(jsonDecoded);

  return decodedUri;
}

  async function submitOtp() {
    const submitBtn = document.getElementById("submit-btn")
    var otp = $("#verificationCode").val();
    var url_string = window.location.href;
    var userId = getAllUrlParams(url_string).userId;
    var shaObj = new jsSHA(otp, "TEXT");
    var _otpHash = shaObj.getHMAC(
      "23BF1DC999734E6A6860BD56273AA455",
      "HEX",
      "SHA-256",
      "HEX"
    );
    var data = { otpHash: _otpHash, otpId: this.otpId };
    let urlApi = `/accounts/api/v1/users/${userId}/otps/channels/SMP`;
    const token = new URLSearchParams(window.location.search).get("token");
    if (token) {
      urlApi = `${urlApi}?token=${token}`;
    }
    if(!submitBtn.disabled)
    submitBtn.disabled = true;
    // await performTokenVerification();
    if(submitBtn.disabled)
    $.ajax({
      type: "PUT",
      beforeSend: function (request) {
        request.setRequestHeader("Content-Type", "application/json");
        request.setRequestHeader("Accept", "application/json");
        request.setRequestHeader("X-Language", getCurrentLanguage());
      },
      dataType: "json",
      url: urlApi,
      processData: false,
      data: JSON.stringify(data),
      success: function (msg) {
        // dev-13
        // window.location.href="https://dev13-ma.opdev.vn/accounts/"
        var clientRequestId = getAllUrlParams(url_string).clientRequestId;
        loginStep2(clientRequestId);

        // public
        // window.location.href="https://ma.onepay.vn/accounts/" 
        
      },
      error: function (failMsg, textStatus) {
        if (textStatus === 'abort') {return} else 
        // if (handleSessionExpired(failMsg)) { return } else if (handleSessionUnauthorizedOtp(failMsg)) { return } else 
        {
          // getQr();
          submitBtn.disabled = false;
          var error = translations[getCurrentLanguage()]["SystemError"];
          if (failMsg && failMsg.responseJSON && failMsg.responseJSON.responseCode === 'OTP_TOO_MANY_REQUESTS.429') {
            lockOtpSubmit();
          }
          if (failMsg && failMsg.responseJSON && failMsg.responseJSON.responseDescription !== undefined) {
            error = failMsg.responseJSON.responseDescription;
          }
          $('.error-message').text(error).addClass('show');
          $("#verificationCode").val("");
        }
      },
    });
  }

  function newQR(elementId, text) {
    // Remove previous QR code if any
    var el = document.getElementById(elementId);
    el.innerHTML = '';
    new QRCode(el, {
        text: text,
        width: 187.2,
        height: 187.2,
        typeNumber: 4,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H
    });
}

  function loginStep2(clientRequestId) {
    $.ajax({
      type: "GET",
      beforeSend: function (request) {
        request.setRequestHeader("X-Client-Request-Id", clientRequestId);
        request.setRequestHeader("Content-Type", "application/json");
        request.setRequestHeader("Accept", "application/json");
        request.setRequestHeader("X-Language", getCurrentLanguage());
      },
      dataType: "json",
      url: "/accounts/api/v1/oauth2/authorize?confirm=true",
      processData: false,
      success: function (msg) {
        // $("#results").append("The result =" + StringifyPretty(msg));
        console.log(msg);
        window.location = msg.redirect;
      },
      error: function (failMsg, textStatus) {
        if (textStatus === 'abort') {return} else
        //  if (handleSessionExpired(failMsg)) { return } else if (handleSessionUnauthorizedOtp(failMsg)) { return } else 
         {
          console.log(failMsg);
          alert(failMsg.responseJSON.responseDescription);
          document.getElementById("submit-btn").disabled = false;
        }
      },
    });
  }

  function getAllUrlParams(url) {
    // get query string from url (optional) or window
    var queryString = url ? url.split("?")[1] : window.location.search.slice(1);

    // we'll store the parameters here
    var obj = {};

    // if query string exists
    if (queryString) {
      // stuff after # is not part of query string, so get rid of it
      queryString = queryString.split("#")[0];

      // split our query string into its component parts
      var arr = queryString.split("&");

      for (var i = 0; i < arr.length; i++) {
        // separate the keys and the values
        var a = arr[i].split("=");

        // in case params look like: list[]=thing1&list[]=thing2
        var paramNum = undefined;
        var paramName = a[0].replace(/\W\[\d*\]/, function (v) {
          paramNum = v.slice(1, -1);
          return "";
        });

        // set parameter value (use 'true' if empty)
        var paramValue = typeof a[1] === "undefined" ? true : a[1];

        // if parameter name already exists
        if (obj[paramName]) {
          // convert value to array (if still string)
          if (typeof obj[paramName] === "string") {
            obj[paramName] = [obj[paramName]];
          }
          // if no array index number specified...
          if (typeof paramNum === "undefined") {
            // put the value on the end of the array
            obj[paramName].push(paramValue);
          }
          // if array index number specified...
          else {
            // put the value at that index number
            obj[paramName][paramNum] = paramValue;
          }
        }
        // if param name doesn't exist yet, set it
        else {
          obj[paramName] = paramValue;
        }
      }
    }

    return obj;
  }
  window.forgotPassword = function () {
    window.location.href = '/accounts/forgetpassword/';
  };
});

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('otpform');
    const inputs = document.querySelectorAll('.otp-input');
    const verificationCode = document.getElementById('verificationCode');
    const submitButton = document.querySelector('.login-btn');
    const isMobile = detectMobile();

    if (!isMobile) {
        // Kiểm tra xem đã nhập đủ 6 số chưa
        function checkInputsFilled() {
          const isFilled = [...inputs].every(input => input.value.length === 1 && submitButton.textContent == translations[getCurrentLanguage()].submit);
          submitButton.disabled = !isFilled;
        }

        // Xử lý paste
        function handlePaste(e) {
            e.preventDefault();
            const pasteData = e.clipboardData.getData('text').replace(/[^0-9]/g, '').slice(0, 6);
            
            if (pasteData) {
                [...inputs].forEach((input, index) => {
                    if (pasteData[index]) {
                        input.value = pasteData[index];
                        if (index < inputs.length - 1 && pasteData[index + 1]) {
                            inputs[index + 1].focus();
                        }
                    }
                });
                updateVerificationCode();
                checkInputsFilled();
            }
        }

        // Cập nhật giá trị verification code
        function updateVerificationCode() {
            verificationCode.value = [...inputs].map(input => input.value).join('');
        }

        // Thêm event listeners cho mỗi input
        inputs.forEach((input, index) => {
            // Xử lý khi nhập
            input.addEventListener('input', function(e) {
                // Chỉ giữ lại số
                this.value = this.value.replace(/[^0-9]/g, '');
                
                // Đảm bảo chỉ có 1 số
                if (this.value.length > 1) {
                    this.value = this.value.slice(0, 1);
                }
                
                if (e.inputType !== "deleteContentBackward") {
                    // Tự động focus vào ô tiếp theo khi nhập
                    if (this.value.length === 1 && index < inputs.length - 1) {
                        inputs[index + 1].focus();
                    }
                }
                updateVerificationCode();
                checkInputsFilled();
            });

            // Xử lý khi xóa
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Backspace' && !this.value && index > 0) {
                    inputs[index - 1].focus();
                }
                // Chạy checkInputsFilled sau khi xóa
                setTimeout(checkInputsFilled, 0);
            });

            // Xử lý paste
            input.addEventListener('paste', handlePaste);
        });
    }
});

function detectMobile() {
    return window.innerWidth < 767.98;
}

function handleSessionExpired(failMsg) {
  if (
    failMsg &&
    failMsg.responseJSON &&
    failMsg.responseJSON.responseCode === "ONEAM_SESSION_EXPIRED.401"
  ) {
    window.location.href = '/forgetpassword/linkexpired.html';
    return true;
  }
  return false;
}

function handleSessionUnauthorized(failMsg) {
  if (
    failMsg &&
    failMsg.responseJSON &&
    failMsg.responseJSON.responseCode === "ONEAM_USER_SESSION_UNAUTHOR.401"
  ) {
    window.location.href = '/accounts/';
    return true;
  }
  return false;
}

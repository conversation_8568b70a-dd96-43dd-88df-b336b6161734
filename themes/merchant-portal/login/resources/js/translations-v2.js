let isMobile = detectMobile(); // Global variable to store mobile detection status

const translations = {
    en: {
        welcome: "Welcome to Merchant Portal",
        emailMobile: "Email/ Mobile number",
        password: "Password",
        forgotPassword: "Forgot password?",
        login: "Log in",
        aboutOnePay: "About OnePay",
        paymentGuide: "Payment Guide",
        faqs: "FAQs",
        contactUs: "Contact Us",
        copyright: `Copyright © 2006 - ${new Date().getFullYear()} OnePay. All rights reserved.`,
        otpTitle: "OTP Verification",
        otpVerification: "OTP Verification",
        otpInputPlaceholder: "Verification Code",
        enterOtpCode: "We have sent a verification code to your email address:",
        troubleOtp: "Have trouble with OTP code?",
        reRegisterOtp: "Resend OTP email",
        welcomeResetOtp:"Reset authentication",
        resetAuthTitle: "Reset authentication",
        rescanOtpTitle: "Merchant Accounts",
        rescanOtpInstruction: "We will send the guidance email for re-registering your OTP to your email",
        qrInstruction: "Scan QR code with Google Authenticator application and enter 6 characters generated.",
        enterOtp: "Enter OTP",
        otpLabel: "One-Time Password",
        verify: "Verify",
        backToLogin: "Back to Login",
        rescanMessage: "An email with detailed instructions has been sent to your registered mailbox. Please check your inbox or spam folder (if any) to continue the authenticator setup process.",
        rescanWarning: "The request to reset the authenticator has been recorded. Please check your email and try again after 10 minutes.",
        rescanError: "An error occurred while processing your request. Please try again.",
        footerTel: "Tel:",
        footerEmail: "Email:",
        modalTitle: "Confirm authenticator reset",
        modalBody: "An email with detailed instructions will be sent to your registered mailbox in a few minutes. Please confirm the reset request to continue.",
        close: "Cancel",
        saveChanges: "Confirm",
        resendQrOtp: "Reset Authenticator",
        resetAuthentication: "Reset Authentication",
        tokenExpireTitle: "Merchant Accounts",
        notAvailable: "Not available",
        linkExpired: "The link has expired. Please try again.",
        successTitle: "Merchant Accounts",
        emailSentTitle: "Instructions have been sent to your email",
        emailSentInstruction: "Please check your inbox and follow instructions to reset your password",
        emailSentInstructionOTP: "Please check your inbox and follow instructions to re-register OTP",
        forgetPasswordTitle: "Merchant Accounts",
        forgetPasswordInstruction: "We will send the guidance email for resetting your password to the email you enter here",
        email: "Email",
        wrongEmailFormat: "Wrong email format",
        submit: "Submit",
        resetPasswordTitle: "Merchant Accounts",
        resetPassword: "Reset password",
        newPassword: "New password",
        otpRegistrationSuccess: "OTP authentication via Google Authenticator application has been successfully registered",
        passwordRequirement: "Password must include character and number",
        confirmNewPassword: "Confirm new password",
        passwordsDoNotMatch: "Passwords do not match",
        resetError: "An error occurred. Please try again.",
        en: "English",
        vi: "Vietnamese",
        SystemError: "System error! Please, contact to Administrator!",
        welcomeForgotPassword: "Forgot Password",
        newPasswordRequired: "New password is required.",
        passwordMinLength: "Minimum of 12 characters",
        passwordLowercase: "At least one lowercase letter",
        passwordUppercase: "At least one uppercase letter",
        passwordNumber: "At least one number",
        passwordSpecial: "At least one special character: @ $ ! % ? &",
        welcomeOtp:"Welcome to Merchant Portal",
        twoStepVerificationTitle: "2-Step Verification",
        howToGetCodeTitle: "How to get<br>Google Authenticator code?",
        howToGetCodeTitleBtn: "How to get Google Authenticator code?",
        step1Instruction: "<strong>Step 1</strong><br>Open the App Store/Google Play on your phone and download the Google Authenticator app.",
        step2Instruction: "<strong>Step 2</strong><br>Open the app and select \"Scan a QR code\".",
        step3Instruction: "<strong>Step 3</strong><br>Scan the QR code displayed on this screen to add the authentication method.",
        step4Instruction: "<strong>Step 4</strong><br>Enter the OTP shown in the app to complete two-factor authentication setup.",
        step5Instruction: "<strong>Step 5</strong><br>Log in again and verify using the new OTP.",
        noteText: "<strong>Note:</strong> Each OTP can only be used <strong>once</strong> and refreshes every <strong>30 seconds</strong>.",
        oldPassword: "Old Password",
        youAreLogedAs: "You are logged in as",
        selectLanguage: "Select language",
        mobileWelcomeOtp: "You have not registered for 2-step verification yet",
        mobileInstruction: "Please log in on a desktop and follow the instructions to complete your registration.",
        mobileBackToLogin: "Back to login",
        matchesRegex: "Only contain letters (a-Z), numbers (0-9) & common special characters",
        rememberMe:"Remember me",
        Dontask:"Don't ask again on this device"
    },
    vi: {
        welcome: "Chào mừng đến Merchant Portal",
        emailMobile: "Email/ Số điện thoại",
        password: "Mật khẩu",
        forgotPassword: "Quên mật khẩu?",
        login: "Đăng nhập",
        aboutOnePay: "Về OnePay",
        paymentGuide: "Hướng dẫn thanh toán",
        faqs: "Câu hỏi thường gặp",
        contactUs: "Liên hệ",
        copyright: `Copyright © 2006 - ${new Date().getFullYear()} OnePay. Bảo lưu mọi quyền.`,
        otpTitle: "Xác thực OTP",
        otpVerification: "Xác thực OTP",
        otpInputPlaceholder: "Mã xác thực",
        enterOtpCode: "Mã xác thực đã được gửi đến email:",
        troubleOtp: "Gặp vấn đề với mã OTP.",
        reRegisterOtp: "Gửi lại email OTP",
        rescanOtpTitle: "Cổng thông tin Merchant OnePay",
        welcomeResetOtp:"Đăng ký lại mã xác thực",
        resetAuthTitle: "Đăng ký lại mã xác thực",
        rescanOtpInstruction: "Thông tin hướng dẫn đăng ký lại OTP sẽ được gửi đến địa chỉ email phía dưới",
        qrInstruction: "Quét mã QR với ứng dụng Google Authenticator và nhập 6 ký tự OTP được tạo ra.",
        enterOtp: "Nhập OTP",
        otpLabel: "Mã xác thực một lần",
        verify: "Xác nhận",
        backToLogin: "Trở về trang Đăng nhập",
        rescanMessage: "Một email chứa hướng dẫn chi tiết đã được gửi đến địa chỉ hộp thư đã đăng ký. Vui lòng kiểm tra hộp thư đến hoặc thư mục thư rác (nếu có) để tiếp tục quá trình cài đặt mã xác thực.",
        rescanWarning: "Yêu cầu cài đặt lại mã xác thực đã được ghi nhận. Vui lòng kiểm tra email và thử lại sau 10 phút.",
        rescanError: "Xảy ra lỗi trong quá trình xử lý yêu cầu. Vui lòng thử lại.",
        footerTel: "Điện thoại:",
        footerEmail: "Email:",
        modalTitle: "Xác nhận cài đặt lại mã xác thực",
        modalBody: "Một email chứa hướng dẫn chi tiết sẽ được gửi đến địa chỉ hòm thư đã đăng ký trong ít phút. Vui lòng xác nhận yêu cầu cài đặt lại mã xác thực để tiếp tục quá trình.",
        close: "Hủy",
        saveChanges: "Xác nhận",
        resendQrOtp: "Cài đặt lại mã xác thực",
        resetAuthentication: "Reset Authentication",
        tokenExpireTitle: "Cổng thông tin Merchant OnePay",
        notAvailable: "Không khả dụng",
        linkExpired: "Liên kết đã hết hạn. Vui lòng thử lại.",
        successTitle: "Cổng thông tin Merchant OnePay",
        emailSentTitle: "Hướng dẫn đã được gửi đến địa chỉ email",
        emailSentInstruction: "Vui lòng kiểm tra hòm thư và thực hiện theo hướng dẫn để thiết lập lại mật khẩu",
        emailSentInstructionOTP: "Vui lòng kiểm tra hòm thư và thực hiện theo hướng dẫn để đăng ký lại OTP",
        forgetPasswordTitle: "Cổng thông tin Merchant OnePay",
        forgetPasswordInstruction: "Thông tin hướng dẫn thiết lập lại mật khẩu sẽ được gửi đến địa chỉ email được nhập phía dưới",
        email: "Email",
        wrongEmailFormat: "Sai định dạng email",
        submit: "Xác nhận",
        resetPasswordTitle: "Cổng thông tin Merchant OnePay",
        resetPassword: "Cài đặt lại mật khẩu",
        newPassword: "Mật khẩu mới",
        otpRegistrationSuccess: "Kênh xác thực OTP qua ứng dụng Google Authenticator đã đăng ký thành công",
        passwordRequirement: "Mật khẩu phải bao gồm chữ và số",
        confirmNewPassword: "Xác nhận lại mật khẩu mới",
        passwordsDoNotMatch: "Mật khẩu không khớp",
        resetError: "Đã xảy ra lỗi. Vui lòng thử lại.",
        en: "Tiếng Anh",
        vi: "Tiếng Việt",
        SystemError: "Lỗi hệ thống! Vui lòng liên hệ quản trị viên!",
        welcomeForgotPassword: "Quên mật khẩu",
        newPasswordRequired: "Mật khẩu mới là bắt buộc.",
        passwordMinLength: "Tối thiểu 12 ký tự",
        passwordLowercase: "Ít nhất một chữ thường",
        passwordUppercase: "Ít nhất một chữ hoa",
        passwordNumber: "Ít nhất một chữ số",
        passwordSpecial: "Ít nhất một ký tự đặc biệt: @ $ ! % ? &",
        welcomeOtp:"Xác thực 2 bước",
        twoStepVerificationTitle: "Xác thực 2 bước",
        howToGetCodeTitle: "Hướng dẫn<br>lấy mã OTP từ Google Authenticator",
        howToGetCodeTitleBtn: "Hướng dẫn lấy mã OTP từ Google Authenticator",
        step1Instruction: "<strong>Bước 1:</strong><br>Mở kho ứng dụng App Store/ Google Play trên điện thoại và tải ứng dụng Google Authenticator.",
        step2Instruction: "<strong>Bước 2:</strong><br>Mở ứng dụng Google Authenticator và chọn \"Scan a QR code\".",
        step3Instruction: "<strong>Bước 3:</strong><br>Quét QR hiển thị tại màn này để thêm kênh xác thực trên ứng dụng Google Authenticator.",
        step4Instruction: "<strong>Bước 4:</strong><br>Nhập mã OTP hiển thị trên ứng dụng để hoàn tất đăng ký xác thực 2 yếu tố.",
        step5Instruction: "<strong>Bước 5:</strong><br>Đăng nhập lại và xác thực bằng mã OTP mới.",
        noteText: "<strong>Lưu ý:</strong> Mỗi mã OTP chỉ sử dụng được <strong>1 lần</strong> và sẽ được làm mới sau <strong>30 giây</strong>.",
        oldPassword:"Mật khẩu cũ",
        youAreLogedAs: "Bạn đã đăng nhập với",
        selectLanguage: "Chọn ngôn ngữ",
        mobileWelcomeOtp: "Tài khoản chưa đăng ký xác thực 2 bước",
        mobileInstruction: "Vui lòng đăng nhập trên thiết bị máy tính và thực hiện theo các chỉ dẫn chi tiết",
        mobileBackToLogin: "Trở về trang Đăng nhập",
        matchesRegex: "Chỉ chứa chữ cái (a-Z), số (0-9) và các ký tự đặc biệt thông dụng",
        rememberMe:"Ghi nhớ đăng nhập",
        Dontask:"Đừng hỏi lại trên thiết bị này"
    }
};

const links = {
    en: {
        aboutOnePay: "https://www.onepay.vn/en/about/",
        paymentGuide: "https://onepay.vn/documents/payment/guideEN.jsp?logos=v,m,a,j,u,at",
        faqs: "https://www.onepay.vn/en/for-service-support/",
        contactUs: "https://www.onepay.vn/en/for-business/"
    },
    vi: {
        aboutOnePay: "https://www.onepay.vn/gioi-thieu/",
        paymentGuide: "https://onepay.vn/documents/payment/guideVN.jsp?logos=v,m,a,j,u,at",
        faqs: "https://www.onepay.vn/lien-he/ho-tro-khach-hang/",
        contactUs: "https://www.onepay.vn/lien-he/lien-he-hop-tac/"
    }
}

// Function to get browser language
function getBrowserLanguage() {
    const lang = navigator.language || navigator.userLanguage;
    return lang.startsWith('vi') ? 'vi' : 'en';
}

// Function to get current language from localStorage or browser
function getCurrentLanguage() {
    return localStorage.getItem('language') || getBrowserLanguage();
}

// Function to set language
function setLanguage(lang) {
    localStorage.setItem('language', lang);
    translatePage(lang);
}

// Function to translate all elements with the 'translate' attribute
function translatePage(lang) {
    const elements = document.querySelectorAll('[translate]');
    elements.forEach(function(el) {
        const key = el.getAttribute('translate');
        if (translations[lang] && translations[lang][key]) {
            el.innerHTML = translations[lang][key];
        }
        if (el.tagName === 'A' && links[lang] && links[lang][key]) {
            el.href = links[lang][key];
        }
    });
    const placeholders = document.querySelectorAll('[placeholderTran]');
    placeholders.forEach(function(el) {
        const key = el.getAttribute('placeholderTran');
        if (translations[lang] && translations[lang][key]) {
            el.setAttribute("placeholder", translations[lang][key]);
        }
    });
}

// Initialize language on page load
document.addEventListener('DOMContentLoaded', function() {
    const currentLang = getCurrentLanguage();
    translatePage(currentLang);

    // Set the language selector to the current language
    if(document.querySelector('.language-selector select')){
        document.querySelector('.language-selector select').value = currentLang;
        document.querySelector('.language-selector select').addEventListener('change', function(e) {
            setLanguage(e.target.value);
            translatePage(e.target.value);
        });
    }
    // Add event listener for language selector
    const langSelected = document.getElementById('langSelected');
    const langOptions = document.getElementById('langOptions');
    const langSelectedFlag = document.getElementById('langSelectedFlag');
    const langSelectedLabel = document.getElementById('langSelectedLabel');
    const langCaret = document.getElementById('langCaret'); // Get reference to the caret icon
    const langOptionElements = Array.from(langOptions.getElementsByClassName('lang-option'));
    const bgOverlayMobile = document.getElementById('overlay_bg');

    function setLangUI(lang) {
        // Commented out to allow error messages to display
        // if (window.$) {
        //     $('.error-message').removeClass('show').hide();
        //     $('.error-message-small').removeClass('show').hide();
        // }
        const viText = isMobile ? translations[lang].vi : 'VI';
        const enText = isMobile ? translations[lang].en : 'EN';
        const langElement = document.querySelector(`.lang-option[data-lang="${lang}"] img`);
        const iconFlag = langElement ? langElement.getAttribute('src') : `assets/${lang}_lang.svg`; 
        if (lang === 'vi') {
            langSelectedFlag.src = iconFlag;
            langSelectedFlag.alt = 'VI';
            langSelectedLabel.textContent = viText;
        } else {
            langSelectedFlag.src = iconFlag;
            langSelectedFlag.alt = 'EN';
            langSelectedLabel.textContent = enText;
        }
        // Update the text for the language options in the dropdown
        langOptionElements.forEach(opt => {
            const optionLang = opt.getAttribute('data-lang');
            const spanElement = opt.querySelector('[data-translate-mobile]');
            if (spanElement) {
                const langText = translations[lang][optionLang];
                spanElement.textContent = langText;
            }
            if (optionLang === lang) {
                opt.classList.add('lang_checked');
            } else {
                opt.classList.remove('lang_checked');
            }
        });
        
    }

    langSelected.onclick = function(e) {
        langOptions.style.display = langOptions.style.display === 'block' ? 'none' : 'block';
        langCaret.classList.toggle('rotated'); // Toggle the rotated class
        if (isMobile) {
            bgOverlayMobile.style.display = langOptions.style.display === 'block' ? 'block' : 'none';
        }
        e.stopPropagation();
    };

    document.body.onclick = function() {
        if (langOptions.style.display === 'block') {
            langOptions.style.display = 'none';
            langCaret.classList.remove('rotated'); // Remove rotated class if dropdown closes
            if (isMobile) {
                bgOverlayMobile.style.display = 'none';
            }
        }
    };

    langOptionElements.forEach(opt => {
        opt.onclick = function(e) {
            const lang = this.getAttribute('data-lang');
            localStorage.setItem('language', lang);
            setLangUI(lang);
            setLanguage(lang);
            langOptions.style.display = 'none';
            langCaret.classList.remove('rotated'); // Remove rotated class when an option is selected
            if (isMobile) {
                bgOverlayMobile.style.display = 'none'; // Hide overlay when language is selected
            }
        };
    });

    // On page load, set the correct flag, label, language, and hide the selected option
    document.addEventListener('DOMContentLoaded', function() {
        const lang = getCurrentLanguage();
        setLangUI(lang);
        setLanguage(lang);
    });

    $(document).ready(function () {
        const lang = getCurrentLanguage();
        setLangUI(lang);
        setLanguage(lang);
    })

    window.addEventListener('resize', function() {
        const oldIsMobile = isMobile;
        isMobile = detectMobile();
        if (oldIsMobile !== isMobile) {
            console.log("resize")
            // Only update UI if mobile status changed
            setLangUI(getCurrentLanguage());
        }
    });
});

function detectMobile() {
    return window.innerWidth < 767.98;
}

 
$(document).ready(function () {
    includeHTML(function () {
        const path = new URLSearchParams(window.location.search);
        const token = path.get("token");
        const actionParam = path.get("action");

        // --- Helper Functions ---
        function processLogin1OTP(clientRequestId, userId, email) { 
            $.ajax({
              type: "GET",
              beforeSend: function (request) {
                request.setRequestHeader("X-Client-Request-Id", clientRequestId);
                request.setRequestHeader("Content-Type", 'application/json');
                request.setRequestHeader("Accept", 'application/json');
                request.setRequestHeader("X-Language", getCurrentLanguage());
              },
              dataType: 'json',
              url: "/accounts/api/v1/users/"+userId+"/otps/channels",
              processData: false,
              success: function (msg) {
                  // if (checkIfSMPActive(msg.otpChannels)) {
                  //   console.log('===in page otp');
                    
                  //   window.location.href = "otp?clientRequestId=" + clientRequestId+"&userId=" + userId;
                  // } else {
                  //   console.log('===in page qr');
                    
                  //   window.location.href = "qr?userId=" + userId;
                  // }
        
        
                let isFromEmail = false;
                let redirectUrl = `/accounts/qr?userId=${userId}`;
                // check params from email
                const token = new URLSearchParams(window.location.search).get("token");
                if (token) {
                  // parse
                  const parts = token.split('.');
                  const base64Url = parts[0];
        
                  // convert base64url → base64
                  const base64 = base64Url
                    .replace(/-/g, '+')
                    .replace(/_/g, '/')
                    + '='.repeat((4 - base64Url.length % 4) % 4);
        
                  // encode base64 to []byte
                  const binaryStr = atob(base64);
                  const byteArray = Uint8Array.from(binaryStr, c => c.charCodeAt(0));
        
                  // convert to UTF-8 string
                  const decodedPayload = new TextDecoder('utf-8').decode(byteArray);
        
                  console.log('Decoded payload:', decodedPayload);
                  redirectUrl = `/accounts/qr?userId=${userId}&token=${token}&clientRequestId=${clientRequestId}&email=${email}`;
                }
                window.location.href = redirectUrl;
              },
              error: function (failMsg) {
                console.log(failMsg);
                var error = translations[getCurrentLanguage()]["SystemError"];
                if(failMsg.responseJSON && failMsg.responseJSON.responseDescription) {
                  error = failMsg.responseJSON.responseDescription;
                }
                alert(error);
              }
            });
          }
        // Shows a modal and redirects after a delay
        function displayModalAndRedirect(modalId, redirectUrlTemplate, options = {}) {
            const { message, messageElementId, token: currentToken, delay = 3000 } = options;

            if (message && messageElementId) {
                $(`#${messageElementId}`).text(message);
            }

            $(`#${modalId}`).modal('show');

            let finalRedirectUrl = redirectUrlTemplate;
            if (currentToken && redirectUrlTemplate.includes('${token}')) { // For templates like `/path?token=${token}`
                finalRedirectUrl = redirectUrlTemplate.replace(/\${token}/g, encodeURIComponent(currentToken));
            }
            
            setTimeout(() => {
                window.location.href = finalRedirectUrl;
            }, delay);
        }

        // Performs the token verification AJAX call
        function performTokenVerification(apiToken, successCallback, errorCallback, actionParam) {
            $("#spinner").show();
            $.ajax({
                url: "/accounts/api/v1/verify-token?token=" + token, // Token will be in the POST body
                method: "POST",
                beforeSend: function (request) {
                    request.setRequestHeader("X-Language", getCurrentLanguage());
                },
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify({ token: apiToken , action: actionParam}),
                success: function (res) {
                    $("#spinner").hide();
                    try {
                        let parsedRes = (typeof res === 'string') ? JSON.parse(res) : res;
                        console.log('data response after verify: ', parsedRes);
                        if (parsedRes && (parsedRes.success || parsedRes.responseCode == "200")) {
                            successCallback(parsedRes); 
                            if(actionParam == "reset_password") {
                                window.location.href = "/accounts/forgetpassword/resetpassword.html?token=" + token;
                            } else if(actionParam == "reset_qr_otp") {
                                if (res.nextState !== undefined && res.nextState === "otp") {
                                    var email = res.email;	
                                    processLogin1OTP( res.clientRequestId, res.user.id,res.user.profile.email);
                                  }
                            }
                        } else {
                            errorCallback(parsedRes, null, null, apiToken); // Logical error from API
                        }
                    } catch (e) {
                        $("#spinner").hide();
                        console.error('JSON parse error: ', e);
                        errorCallback({ message: "Lỗi xử lý dữ liệu từ máy chủ." }, null, e, apiToken); // JSON parse error
                    }
                },
                error: function (xhr, status, error) {
                    $("#spinner").hide();
                    console.error("Ajax error: ", error, status, xhr.responseJSON);
                    errorCallback(xhr.responseJSON || { message: "Lỗi kết nối hoặc máy chủ không phản hồi đúng cách." }, xhr, null, apiToken); // AJAX transport error
                }
            });
        }

        // Error handler for reset password flow
        function handleResetPasswordError(responseData, xhr, exception, currentToken) {
            const responseCode = xhr && xhr.responseJSON ? xhr.responseJSON.responseCode : (responseData ? responseData.responseCode : null);

            if (exception) { // JSON parse error
                message = responseData.message || "Lỗi xử lý dữ liệu từ máy chủ.";
                $("#status").text(message);
                displayModalAndRedirect("errorModal", "/accounts/error_page/error.html", {
                    message: message,
                    messageElementId: "errorModalMessage"
                });
                return;
            }
            
            let defaultErrorMessage = "Lỗi trong quá trình xác minh mã xác thực.";
            if (!xhr && responseData) { // API logical error
                defaultErrorMessage = responseData.message || "Xác minh thất bại.";
            } else if (xhr && responseData && responseData.message) { // AJAX error with API message
                defaultErrorMessage = responseData.message;
            }


            if (responseCode === 'ONEAM_TOKEN_EMAIL_INVALID.400') {
                message = "Mã xác thực không hợp lệ.";
                $("#status").text(message);
                displayModalAndRedirect("invalidTokenModal", "/accounts/error_page/token_invalid.html?token=${token}", { token: currentToken });
            } else if (responseCode === 'ONEAM_TOKEN_EMAIL_EXPIRED.401') {
                message = "Mã xác thực hết hạn.";
                $("#status").text(message);
                displayModalAndRedirect("expiredTokenModal", "/accounts/error_page/token_expire.html?token=${token}", { token: currentToken });
            } else if (responseCode === 'ONEAM_TOKEN_EMAIL_USED.403') {
                message = "Mã xác thực đã được sử dụng.";
                $("#status").text(message);
                displayModalAndRedirect("usedTokenModal", "/accounts/error_page/token_used.html?token=${token}", { token: currentToken });
            } else {
                $("#status").text(defaultErrorMessage);
                displayModalAndRedirect("errorModal", "/accounts/error_page/error.html", {
                    message: defaultErrorMessage,
                    messageElementId: "errorModalMessage"
                });
            }
        }

        // Error handler for QR OTP reset flow
        function handleQrOtpError(responseData, xhr, exception, currentToken) {
            let message;
            const responseCode = xhr && xhr.responseJSON ? xhr.responseJSON.responseCode : (responseData ? responseData.responseCode : null);

            if (exception) { // JSON parse error
                message = responseData.message || "Lỗi xử lý dữ liệu từ máy chủ.";
                $("#status").text(message);
                displayModalAndRedirect("errorModal", "/accounts/error_page/error.html", {
                    message: message,
                    messageElementId: "errorModalMessage"
                });
                return;
            }
            
            let defaultErrorMessage = "Lỗi trong quá trình xác minh mã xác thực.";
            if (!xhr && responseData) { // API logical error
                defaultErrorMessage = responseData.message || "Xác minh thất bại.";
            } else if (xhr && responseData && responseData.message) { // AJAX error with API message
                defaultErrorMessage = responseData.message;
            }


            if (responseCode === 'ONEAM_TOKEN_EMAIL_INVALID.400') {
                message = "Mã xác thực không hợp lệ.";
                $("#status").text(message);
                displayModalAndRedirect("invalidTokenModal", "/accounts/error_page/token_invalid.html?token=${token}", { token: currentToken });
            } else if (responseCode === 'ONEAM_TOKEN_EMAIL_EXPIRED.401') {
                message = "Mã xác thực hết hạn.";
                $("#status").text(message);
                displayModalAndRedirect("expiredTokenModal", "/accounts/error_page/token_expire.html?token=${token}", { token: currentToken });
            } else if (responseCode === 'ONEAM_TOKEN_EMAIL_USED.403') {
                message = "Mã xác thực đã được sử dụng.";
                $("#status").text(message);
                displayModalAndRedirect("usedTokenModal", "/accounts/error_page/token_used.html?token=${token}", { token: currentToken });
            } else {
                $("#status").text(defaultErrorMessage);
                displayModalAndRedirect("errorModal", "/accounts/error_page/error.html", {
                    message: defaultErrorMessage,
                    messageElementId: "errorModalMessage"
                });
            }
        }

        // --- Main Logic ---

        if (!token) {
            $("#status").text("Thiếu mã xác thực.");
            $("#spinner").hide();
            displayModalAndRedirect("errorModal", "/accounts/error_page/error.html", {
                message: "Thiếu mã xác thực trong URL.",
                messageElementId: "errorModalMessage"
            });
            return;
        }

        console.log('====actionParam: ', actionParam);

        if (actionParam === "reset_password") {
            performTokenVerification(token,
                function successResetPassword(res) { // Success callback for password reset
                    console.log('inside success verify for reset_password', res);
                    $('#successModal').modal('show');
                    // Note: The original commented-out API call for 'change-password' 
                    // and the redirect logic are not included here.
                    // If successModal doesn't handle next steps, add them here.
                },
                handleResetPasswordError // Error callback for password reset
                ,actionParam
            );
        } else if (actionParam === "reset_qr_otp"){ // Default action is reset QR OTP
            performTokenVerification(token,
                function successQrOtp(res) { // Success callback for QR OTP
                    console.log('inside success verify for QR OTP', res);
                    $('#successModal').modal('show');
                    setTimeout(() => {
                        window.location.href = "/accounts/qr?token=" + token + "&clientRequestId=" + res.clientRequestId;
                    }, 3000);
                },
                handleQrOtpError // Error callback for QR OTP
                ,actionParam
            );
        }
    });
});

$(document).ready(function () {

  function handleSessionUnauthorizedOtp(failMsg) {
    if (
      failMsg &&
      failMsg.responseJSON &&
      failMsg.responseJSON.responseCode === "ONEAM_USER_SESSION_UNAUTHOR.401"
    ) {
      window.location.href = '/accounts/';
      return true;
    }
    return false;
  }
  
  function handleSessionExpired(failMsg) {
    if (
      failMsg &&
      failMsg.responseJSON &&
      failMsg.responseJSON.responseCode === "ONEAM_SESSION_EXPIRED.401"
    ) {
      window.location.href = '/forgetpassword/linkexpired.html';
      return true;
    }
    return false;
  }

  //reset authen case error, not check 
  let tokenResetAuthenQr = '';
  if (window.location.pathname.includes("/error_page")) {
    tokenResetAuthenQr = new URLSearchParams(window.location.search).get("token");
  }

  if (!tokenResetAuthenQr && !window.location.pathname.includes("rescan-otp")) {
    //check if SMP is active
    getSMP();
  }

  // Hide error message when user starts typing new OTP
  $('.otp-input').on('input', function() {
    $('.error-message').removeClass('show');
  });

    // enter 
    $('.otp-input').keydown(function(event) {
      const submitBtn = document.getElementById('submit-btn'); // or your submit button's id
      if (event.keyCode === 13 && submitBtn.disabled == false) {
        if (window.location.pathname.includes("rescan-otp")) {
          submitRescanOtp();
        } else {
          submitOtp();
        }
      }
  });

  $('#otpform').submit(function (event) { 
    event.preventDefault();

    // Check if we're on the rescan page
    if (window.location.pathname.includes("rescan-otp")) {
      submitRescanOtp();
    } else {
      submitOtp();
    }
  });

  // Lấy email từ URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const email = urlParams.get('email');
  
  // Nếu có email trong URL, điền vào input
  if (email) {
      $('#email').val(decodeURIComponent(email));
      // Kích hoạt sự kiện input để cập nhật UI
      $('#email').trigger('input');
      $('#button-rescan-otp-submit').prop('disabled', false);
  }

  function getSMP() { 
    
    var url_string = window.location.href;
    var clientRequestId = getAllUrlParams(url_string).clientRequestId;
    $.ajax({
      type: "POST",
      beforeSend: function (request) {
        request.setRequestHeader("X-Client-Request-Id", clientRequestId);
        request.setRequestHeader("Content-Type", 'application/json');
        request.setRequestHeader("Accept", 'application/json');
        request.setRequestHeader("X-Language", getCurrentLanguage());
      },
      dataType: 'json',
      url: "/accounts/api/v1/oauth2/channels/SMP",
      processData: false,
      success: function (msg) {
        
      },
      error: function (failMsg) {
        if (handleSessionExpired(failMsg)) { return } else if (handleSessionUnauthorizedOtp(failMsg)) { return } else {
          console.log(failMsg);
          var error = translations[getCurrentLanguage()]["SystemError"];
          if (failMsg && failMsg.responseJSON && failMsg.responseJSON.responseDescription !== undefined) {
            error = failMsg.responseJSON.responseDescription;
          }
          $('.error-message').text(error).addClass('show');
        }
      }
    });
  }
  let otpLockoutTimer = null;
  let otpLockoutSeconds = 30;
  
  const submitBtn = document.getElementById('submit-btn'); // or your submit button's id
  const timer = document.getElementById("otp-timer");
  function lockOtpSubmit() {
    let secondsLeft = otpLockoutSeconds;
    submitBtn.disabled = true;
    timer.textContent = `(${secondsLeft}s)`;
    otpLockoutTimer = setInterval(() => {
        secondsLeft--;
        timer.textContent = `(${secondsLeft}s)`;
        if (secondsLeft <= 0) {
            clearInterval(otpLockoutTimer);
            timer.textContent = ``;
            submitBtn.disabled = false;
        }
    }, 1000);
}

  function submitOtp() {
    var url_string = window.location.href;
    var clientRequestId = getAllUrlParams(url_string).clientRequestId;
    var otp = $("#verificationCode").val();
    const submitButton = document.querySelector('.login-btn');
    var shaObj = new jsSHA(otp, "TEXT");
    var _otpHash = shaObj.getHMAC('23BF1DC999734E6A6860BD56273AA455', "HEX", "SHA-256", "HEX");
    submitButton.disabled = true;
    if(submitButton.disabled)
    $.ajax({
      type: "GET",
      beforeSend: function (request) {
        request.setRequestHeader("X-Client-Request-Id", clientRequestId);
        request.setRequestHeader("Content-Type", 'application/json');
        request.setRequestHeader("Accept", 'application/json');
        request.setRequestHeader("X-Language", getCurrentLanguage());
      },
      dataType: 'json',
      url: "/accounts/api/v1/oauth2/auth?otpHash="+_otpHash.toString().toUpperCase(),
      processData: false,
      success: function (msg) {
        // $("#results").append("The result =" + StringifyPretty(msg));
        console.log(msg);
        // window.location = msg.redirect;
        loginStep2(clientRequestId);
      },
      error: function (failMsg) {
        if (handleSessionExpired(failMsg)) { return } else if (handleSessionUnauthorizedOtp(failMsg)) { return } else {
          submitButton.disabled = false;
          var error = translations[getCurrentLanguage()]["SystemError"];
          if (failMsg && failMsg.responseJSON && failMsg.responseJSON.responseCode === 'OTP_TOO_MANY_REQUESTS.429') {
            lockOtpSubmit();
          }
          if (failMsg && failMsg.responseJSON && failMsg.responseJSON.responseDescription !== undefined) {
            error = failMsg.responseJSON.responseDescription;
          }
          // Show error message
          $('.error-message').text(error).addClass('show');
          // Clear all OTP inputs
          $("#verificationCode").val("");
          getSMP();
        }
      }
    });
  }

  function loginStep2(clientRequestId) {


    $.ajax({
      type: "GET",
      beforeSend: function (request) {
        request.setRequestHeader("X-Client-Request-Id", clientRequestId);
        request.setRequestHeader("Content-Type", 'application/json');
        request.setRequestHeader("Accept", 'application/json');
        request.setRequestHeader("X-Language", getCurrentLanguage());
      },
      dataType: 'json',
      url: "/accounts/api/v1/oauth2/authorize?confirm=true",
      processData: false,
      success: function (msg) {
        // $("#results").append("The result =" + StringifyPretty(msg));
        console.log(msg);
        window.location = msg.redirect;
      },
      error: function (failMsg) {
        if (handleSessionExpired(failMsg)) { return } else if (handleSessionUnauthorizedOtp(failMsg)) { return } else {
        console.log(failMsg);
        alert(failMsg.responseJSON.responseDescription);
        }
      }
    });
  }

  function loginStep1(user, password, callback) {    
  var username = (user) ? user.replace(/^(\+?84|0)?((3[0-9]|4[0-9]|5[0-9]|7[0-9]|8[0-9]|9[0-9]|12[0-9]|16[0-9]|18[68]|199)\d{7})$/, '84$2') : user;

    var shaObj = new jsSHA(password, "TEXT");
    var hmac = shaObj.getHMAC('23BF1DC999734E6A6860BD56273AA455', "HEX", "SHA-256", "HEX");

    var url_string = window.location.href;
    console.log(getAllUrlParams(url_string));
    var c = getAllUrlParams(url_string).continue;
    // TEST ENVIRONMENT
	//var data = {
    //  continue: 'http%3A%2F%2Foneam.onepay.vn%2F%3FresponseType%3Dcode%26scope%3Dprofile%26redirectUri%3Dhttps%3A%2F%2Fma-mtf.onepay.vn%2Flogin%26clientId%3DONEMADM%26authLevel%3D1'
    //}
	
	// PRODUCTION ENVIRONMENT
	 var data = {
       continue: 'http%3A%2F%2Foneam.onepay.vn%2F%3FresponseType%3Dcode%26scope%3Dprofile%26redirectUri%3Dhttps%3A%2F%2Fma.onepay.vn%2Flogin%26clientId%3DONEMADM%26authLevel%3D1'
  	}
// DEV ENVIRONMENT
//    data = {
//      continue: 'http%3A%2F%2Foneam.onepay.vn%2F%3FresponseType%3Dcode%26scope%3Dprofile%26redirectUri%3Dhttps%3A%2F%2Fdev.onepay.vn%2Fma%2Flogin%26clientId%3DONEMADM%26authLevel%3D1'
//    }
	//if(c != undefined) {
	//	data = {
	//		continue: c
	//	}
	//}

    $.ajax({
      type: "POST",
      beforeSend: function (request) {
        request.setRequestHeader("X-Username", username);
        request.setRequestHeader("X-Password", hmac.toString().toUpperCase());
        request.setRequestHeader("Content-Type", 'application/json');
        request.setRequestHeader("Accept", 'application/json');
        request.setRequestHeader("X-Language", getCurrentLanguage());
      },
      dataType: 'json',
      url: "api/v1/oauth2/auth",
      data: JSON.stringify(data),
      processData: false,
      success: function (msg) {
        // $("#results").append("The result =" + StringifyPretty(msg));
        console.log(msg);
        if(msg.nextState !== undefined && msg.nextState === "otp") {
          window.location.href= 'otp?clientRequestId='+ msg.clientRequestId ;
        }else {
          callback(msg)
        }
      },
      error: function (failMsg) {
        if (handleSessionExpired(failMsg)) { return } else if (handleSessionUnauthorizedOtp(failMsg)) { return } else {
          console.log(failMsg);
          var error = translations[getCurrentLanguage()]["SystemError"];
          if (failMsg.responseJSON.responseDescription !== undefined) {
            error = failMsg.responseJSON.responseDescription;
          }
          alert(error);
        }
      }
    });
  }

function getAllUrlParams(url) {

  // get query string from url (optional) or window
  var queryString = url ? url.split('?')[1] : window.location.search.slice(1);

  // we'll store the parameters here
  var obj = {};

  // if query string exists
  if (queryString) {

    // stuff after # is not part of query string, so get rid of it
    queryString = queryString.split('#')[0];

    // split our query string into its component parts
    var arr = queryString.split('&');

    for (var i=0; i<arr.length; i++) {
      // separate the keys and the values
      var a = arr[i].split('=');

      // in case params look like: list[]=thing1&list[]=thing2
      var paramNum = undefined;
      var paramName = a[0].replace(/\[\d*\]/, function(v) {
        paramNum = v.slice(1,-1);
        return '';
      });

      // set parameter value (use 'true' if empty)
      var paramValue = typeof(a[1])==='undefined' ? true : a[1];


      // if parameter name already exists
      if (obj[paramName]) {
        // convert value to array (if still string)
        if (typeof obj[paramName] === 'string') {
          obj[paramName] = [obj[paramName]];
        }
        // if no array index number specified...
        if (typeof paramNum === 'undefined') {
          // put the value on the end of the array
          obj[paramName].push(paramValue);
        }
        // if array index number specified...
        else {
          // put the value at that index number
          obj[paramName][paramNum] = paramValue;
        }
      }
      // if param name doesn't exist yet, set it
      else {
        obj[paramName] = paramValue;
      }
    }
  }

  return obj;
}
   window.forgotPassword = function () {
	     window.location.href = '/accounts/forgetpassword/';
  }

  // // ========= Reset Authentication Processing =========
  // includeHTML(function () {
  //   console.log("Fragments loaded");

  //   const $rescanBtn = $('#rescanQrOtp');
  //   const $confirmRescanBtn = $('#confirmRescanBtn');

  //   const $rescanMessage = $('#rescanMessage');
  //   const $rescanWarning = $('#rescanWarning');
  //   const $rescanError = $('#rescanError');

  //   let isCooldown = false; // variable prevent spam click link

  //   if ($rescanBtn.length && $rescanMessage.length && $rescanWarning.length && $rescanError.length) {
  //       $rescanBtn.on('click', function (e) {
  //           e.preventDefault();
  //           $('#confirmRescanModal').modal('show');
  //       });

  //       $confirmRescanBtn.on('click', function () {
  //           $('#confirmRescanModal').modal('hide');

  //           if (isCooldown) {
  //               showMessage($rescanWarning);
  //               return;
  //           }

  //           isCooldown = true;

  //           let dataToSend = null;
  //           const path = window.location.pathname;
  //           if (path.includes("/otp")) {
  //             const userId = new URLSearchParams(window.location.search).get("userId");
  //             if (!userId) {
  //               console.log("Missing userId in URL for OTP page");
  //               return;
  //             }
  //             dataToSend = JSON.stringify({ user_id: userId });
  //           } else if (path.includes("/error_page")) {
  //             const token = new URLSearchParams(window.location.search).get("token");
  //             if (!token) {
  //               console.log("Missing token in URL for error page");
  //               return;
  //             }
  //             dataToSend = JSON.stringify({ token: token });
  //           } else {
  //             console.log("Unsupported page path:", path);
  //             return;
  //           }

  //           // Check if dataToSend is valid
  //           if (!dataToSend || dataToSend === "{}") {
  //             console.error("Invalid or missing data to send. Aborting AJAX request.");
  //             showMessage($rescanError);
  //             isCooldown = false;
  //             return;
  //           }

  //           $.ajax({
  //               url: '/accounts/api/v1/users/reset-authentication?_action=rescanQrOtp',
  //               method: 'PUT',
  //               data: dataToSend,
  //               success: function (res) {
  //                 try {
  //                   let parsedRes = res;
  //                   if (typeof res === 'string') {
  //                     parsedRes = JSON.parse(res);
  //                   }

  //                   console.log('data response: ', parsedRes);
                    
  //                   if (parsedRes.success || parsedRes.responseCode == "200") {
  //                     if (parsedRes.message == "ONEAM_EMAIL_HAS_SENT") {
  //                       showMessage($rescanWarning);
  //                       setTimeout(() => isCooldown = false, 60000);
  //                       return;
  //                     }

  //                     console.log('inside success');
  //                     showMessage($rescanMessage);
  //                     setTimeout(() => isCooldown = false, 60000);
  //                   } else {
  //                       console.log('inside error');
  //                       showMessage($rescanError);
  //                       isCooldown = false;
  //                   }
  //                 } catch (e) {
  //                   console.error('JSON parse error: ', e);
  //                   showMessage($rescanError);
  //                   isCooldown = false;
  //                 }
                    
  //               },
  //               error: function (jqXHR, textStatus, errorThrown) {
  //                   console.error('AJAX error info:');
  //                   console.error('Status:', jqXHR.status);
  //                   console.error('Status text:', textStatus);
  //                   console.error('Error thrown:', errorThrown);
  //                   console.error('Response text:', jqXHR.responseText);
  //                   console.error('Response JSON (nếu có):', jqXHR.responseJSON);
  //                   console.error('All response headers:\n' + jqXHR.getAllResponseHeaders());
  //                   console.error('Full jqXHR object:', jqXHR);

  //                   showMessage($rescanError);
  //                   isCooldown = false;
  //               }
  //           });

  //           // test alert
  //           // showMessage($rescanMessage);
  //           // if (isCooldown) {
  //           //   setTimeout(function() {isCooldown = false}, 10000);
  //           // } else {
  //           //   showMessage($rescanError);  
  //           // }
  //           // showMessage($rescanError);
  //       });
  //   }

  //   function showMessage($el) {
  //       $('#rescanMessage, #rescanWarning, #rescanError').css({ visibility: 'hidden', opacity: '0' });
  //       $el.stop(true, true).css({ visibility: 'visible', opacity: '1' });
  //       setTimeout(() => $el.css({ visibility: 'hidden', opacity: '0' }), 4000);
  //   }

  //   // go to login and retry
  //   $('#retryLogin').on('click', function (e) {
  //     e.preventDefault();
  //     window.location.href = "/accounts/";
  //   });
  // });
  // ========= Finished processing Reset Authentication =========

});

// khi click vào text link rescan
$('#rescanQrOtp').on('click', function(e) {
  e.preventDefault();
  const urlParams = new URLSearchParams(window.location.search);
  const email = urlParams.get('email'); // lấy email từ URL hiện tại
  const userId = urlParams.get('userId'); // lấy email từ URL hiện tại
  
  if (email) {
      window.location.href = `rescan-otp.html?email=${email}&userId=${userId}`;
  } else {
      window.location.href = 'rescan-otp.html';
  }
});


document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('qrform');
  const inputs = document.querySelectorAll('.otp-input');
  const verificationCode = document.getElementById('verificationCode');
  const submitButton = document.querySelector('.login-btn');

  function checkInputsFilled() {
  // Kiểm tra xem đã nhập đủ 6 số chưa
      const isFilled = [...inputs].every(input => input.value.length === 1 && submitButton.textContent == translations[getCurrentLanguage()].submit);
      submitButton.disabled = !isFilled;
  }

  // Xử lý paste
  function handlePaste(e) {
      e.preventDefault();
      const pasteData = e.clipboardData.getData('text').replace(/[^0-9]/g, '').slice(0, 6);
      
      if (pasteData) {
          [...inputs].forEach((input, index) => {
              if (pasteData[index]) {
                  input.value = pasteData[index];
                  if (index < inputs.length - 1 && pasteData[index + 1]) {
                      inputs[index + 1].focus();
                  }
              }
          });
          updateVerificationCode();
          checkInputsFilled();
      }
  }

  // Cập nhật giá trị verification code
  function updateVerificationCode() {
      verificationCode.value = [...inputs].map(input => input.value).join('');
  }

  // Thêm event listeners cho mỗi input
  inputs.forEach((input, index) => {
      // Xử lý khi nhập
      input.addEventListener('input', function(e) {
          // Chỉ giữ lại số
          this.value = this.value.replace(/[^0-9]/g, '');
          
          // Đảm bảo chỉ có 1 số
          if (this.value.length > 1) {
              this.value = this.value.slice(0, 1);
          }
          
          if (e.inputType !== "deleteContentBackward") {
              // Tự động focus vào ô tiếp theo khi nhập
              if (this.value.length === 1 && index < inputs.length - 1) {
                  inputs[index + 1].focus();
              }
          }
          updateVerificationCode();
          checkInputsFilled();
      });

      // Xử lý khi xóa
      input.addEventListener('keydown', function(e) {
          if (e.key === 'Backspace' && !this.value && index > 0) {
              inputs[index - 1].focus();
          }
          // Chạy checkInputsFilled sau khi xóa
          setTimeout(checkInputsFilled, 0);
      });

      // Xử lý paste
      input.addEventListener('paste', handlePaste);
  });

  // Add this block for login field checking
  const username = document.getElementById('username');
  const password = document.getElementById('password');
  if (username && password) {
      username.addEventListener('input', checkLoginFields);
      password.addEventListener('input', checkLoginFields);
      checkLoginFields(); // Initial check
  }
});

function showError(message) {
  const errorMessage = document.querySelector('.error-message');
  errorMessage.style.display = 'flex';
  errorMessage.querySelector('span').textContent = message;
}

function hideError() {
  const errorMessage = document.querySelector('.error-message');
  errorMessage.style.display = 'none';
}

function showErrorSmall(message) {
  const emailInput = document.getElementById('email');
  const errorMessageSmall = document.querySelector('.error-message-small');
  emailInput.classList.add('error');
  errorMessageSmall.classList.add('show');
  errorMessageSmall.querySelector('span').textContent = message;
}

function hideErrorSmall() {
  const emailInput = document.getElementById('email');
  const errorMessageSmall = document.querySelector('.error-message-small');
  emailInput.classList.remove('error');
  errorMessageSmall.classList.remove('show');
}

function submitRescanOtp() {
  const otpCode = $("#verificationCode").val();
  
  // Get necessary data based on current page
  let dataToSend = null;
  const queryParams = new URLSearchParams(window.location.search);
  const path = window.location.pathname;
  if (path.includes("/otp")) {
    const userId = queryParams.get("userId");
    const email = queryParams.get("email");
    if (!userId && !email) {
      console.log("Missing userId and email in URL for OTP page: ", userId, email);
      $('.error-message').text("Missing parameter").addClass('show').css('display', '');
      return;
    }
    dataToSend = userId ? JSON.stringify({ user_id: userId }) : JSON.stringify({ email: email });
  } else if (path.includes("/error_page")) {
    const token = queryParams.get("token");
    if (!token) {
      console.log("Missing token in URL for error page");
      $('.error-message').text("Missing token parameter").addClass('show').css('display', '');
      return;
    }
    dataToSend = JSON.stringify({ token: token });
  } else {
    console.log("Unsupported page path:", path);
    $('.error-message').text("Invalid page context").addClass('show').css('display', '');
    return;
  }

  // Check if dataToSend is valid
  if (!dataToSend || dataToSend === "{}") {
    console.error("Invalid or missing data to send. Aborting AJAX request.");
    $('.error-message').text("Missing required parameters").addClass('show').css('display', '');
    return;
  }

  $.ajax({
    url: '/accounts/api/v1/users/reset-authentication?_action=rescanQrOtp',
    method: 'PUT',
    beforeSend: function (request) {
      request.setRequestHeader("X-Language", getCurrentLanguage());
    },
    dataType: "json",
    data: dataToSend,
    success: function(res) {
      try {
        let parsedRes = res;
        if (typeof res === 'string') {
          parsedRes = JSON.parse(res);
        }

        console.log('data response: ', parsedRes);
        
        if (parsedRes.success || parsedRes.responseCode == "200") {
          if (parsedRes.message == "ONEAM_EMAIL_HAS_SENT") {
            // redirect sang trang thành công
            hideErrorSmall();
            hideError();
            window.location.href = '/accounts/otp/rescan-success.html'
            console.log('We need redirect to success page');
            // return;
          } else {
            hideErrorSmall();
            hideError();
            window.location.href = '/accounts/otp/rescan-success.html'
            console.log('We need redirect to success page');
            console.log('inside success');
          }
          // redirect here
        } else {
          console.log('inside error');
        }
      } catch (e) {
        console.error('JSON parse error: ', e);
        $('.error-message').text(e).addClass('show').css('display', '');
      }
    },
    error: function (failMsg) {
      if (handleSessionExpired(failMsg)) { return } else if (handleSessionUnauthorizedOtp(failMsg)) { return } else {
        console.log(failMsg);
        var error = translations[getCurrentLanguage()]["SystemError"];
        if (failMsg && failMsg.responseJSON && failMsg.responseJSON.responseDescription) {
          error = failMsg.responseJSON.responseDescription;
        }
        showError(error);
        hideErrorSmall();
      }
    }
  });
}

// Function to enable/disable login button based on username and password fields
function checkLoginFields() {
    const username = document.getElementById('username');
    const password = document.getElementById('password');
    const loginBtn = document.getElementById('login-btn');
    if (!username || !password || !loginBtn) return; // Safety check

    if (username.value.trim() !== '' && password.value.trim() !== '') {
        loginBtn.disabled = false;
    } else {
        loginBtn.disabled = true;
    }
}

function handleSessionUnauthorizedOtp(failMsg) {
  // if (
  //   failMsg &&
  //   failMsg.responseJSON &&
  //   failMsg.responseJSON.responseCode === "ONEAM_USER_SESSION_UNAUTHOR.401"
  // ) {
  //   window.location.href = '/accounts/';
  //   return true;
  // }
  return false;
}

function handleSessionExpired(failMsg) {
  // if (
  //   failMsg &&
  //   failMsg.responseJSON &&
  //   failMsg.responseJSON.responseCode === "ONEAM_SESSION_EXPIRED.401"
  // ) {
  //   window.location.href = '/forgetpassword/linkexpired.html';
  //   return true;
  // }
  return false;
}
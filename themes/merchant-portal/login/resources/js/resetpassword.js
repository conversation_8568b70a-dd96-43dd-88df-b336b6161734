$(document).ready(function() {
    const newPasswordInput = document.getElementById('newPassword');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    const submitButton = document.querySelector('.submit-btn');
    const passwordToggles = document.querySelectorAll('.password-toggle');
    const errorMessages = document.querySelectorAll('.error-message-small');
    const errorMessage = document.querySelector('.error-message');
    let confirmPasswordInteracted = false;

    const lengthReq = document.getElementById('length-req');
    const lowercaseReq = document.getElementById('lowercase-req');
    const uppercaseReq = document.getElementById('uppercase-req');
    const numberReq = document.getElementById('number-req');
    const specialReq = document.getElementById('special-req');
    const generalReq = document.getElementById('general-req')
    const token = new URLSearchParams(window.location.search).get("token");
    // Disable submit button by default
    submitButton.disabled = true;

    // Toggle password visibility
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const eyeIcon = this.querySelector('img');
            
            if (input.type === 'password') {
                input.type = 'text';
                eyeIcon.src = '../assets/eye.svg';
            } else {
                input.type = 'password';
                eyeIcon.src = '../assets/eye-off.svg';
            }
        });
    });

    // Hide error messages on focus
    newPasswordInput.addEventListener('focus', function() {
        hideError(0);
        hideError();
    });

    newPasswordInput.addEventListener('input', function() {
        validatePasswordRequirements(this.value);
        hideError(0);
        hideError();
    });

    confirmPasswordInput.addEventListener('focus', function() {
        hideError(1);
        hideError();
    });

    confirmPasswordInput.addEventListener('input', function() {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        if (confirmPasswordInteracted) {
            validateConfirmPassword();
        }
        if (newPassword === confirmPassword && newPassword !== '') {
            updateSubmitButton(validatePasswordRequirements(newPassword));
            hasErrors = true;
        }
        hideError(1);
        hideError();
    });

    // Validate both fields on blur
    newPasswordInput.addEventListener('blur', function() {
        validateNewPassword();
        if (confirmPasswordInteracted) {
            validateConfirmPassword();
        }
    });

    confirmPasswordInput.addEventListener('blur', function() {
        confirmPasswordInteracted = true;
        validateNewPassword();
        validateConfirmPassword();
    });
    // performTokenVerification();

    function validatePasswordRequirements(password) {
        const isLengthValid = password.length >= 12;
        const hasLowercase = /[a-z]/.test(password);
        const hasUppercase = /[A-Z]/.test(password);
        const hasNumber = /[0-9]/.test(password);
        const hasSpecial = /[@$!%?&]/.test(password);
        const matchesRegex = /^[A-Za-z\d@$!%?&]+$/.test(password);
        updateRequirement(lengthReq, isLengthValid);
        updateRequirement(lowercaseReq, hasLowercase);
        updateRequirement(uppercaseReq, hasUppercase);
        updateRequirement(numberReq, hasNumber);
        updateRequirement(specialReq, hasSpecial);
        updateRequirement(generalReq, matchesRegex);


        const isValid = isLengthValid && hasLowercase && hasUppercase && hasNumber && hasSpecial && matchesRegex;
        updateSubmitButton(isValid);
        return isValid;
    }

    function updateRequirement(element, isValid) {
        if (isValid) {
            element.classList.remove('invalid');
            element.classList.add('valid');
        } else {
            element.classList.remove('valid');
            element.classList.add('invalid');
        }
    }

    function updateSubmitButton(isValid) {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        const passwordsMatch = newPassword === confirmPassword && newPassword !== '';
        
        submitButton.disabled = !(isValid && passwordsMatch);
    }

    function handleTokenExpired(failMsg) {
        // if (
        //     failMsg &&
        //     failMsg.responseJSON &&
        //     failMsg.responseJSON.responseCode === "ONEAM_TOKEN_EMAIL_EXPIRED.401"
        // ) {
        //     window.location.href = '/accounts/error_page/token_expire.html';
        //     return true;
        // }
        return false;
    }

    function validateNewPassword() {
        const newPassword = newPasswordInput.value;
        let hasErrors = false;

        // Reset error state
        hideError(0);

        // Validate new password
        if (!newPassword) {
            hasErrors = true;
        } else {
            const areAllRequirementsMet = validatePasswordRequirements(newPassword);
            if (!areAllRequirementsMet) {
                hasErrors = true;
            }
        }

        if (confirmPasswordInteracted) {
            validateConfirmPassword();
        }

        return !hasErrors;
    }

    function validateConfirmPassword() {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        let hasErrors = false;

        // Reset error state
        hideError(1);

        // Only validate if confirm password has been interacted with
        if (confirmPasswordInteracted) {
            if (newPassword !== confirmPassword && newPassword !== '') {
                showError(translations[getCurrentLanguage()]['passwordsDoNotMatch']);
                hasErrors = true;
            }
        }

        const isValid = !hasErrors;
        updateSubmitButton(validatePasswordRequirements(newPassword));
        return isValid;
    }

    function showError(index, message) {
        console.log("message");
        const errorElement = errorMessages[index];
        errorElement.classList.add('show');
        errorElement.querySelector('span').textContent = message;
    }

    function hideError(index) {
        if (index !== undefined) {
            errorMessages[index].classList.remove('show');
            errorMessages[index].querySelector('span').textContent = '';
        } else {
            errorMessage.style.display = 'none';
        }
    }

    function showError(message) {
        errorMessage.style.display = 'flex';
        errorMessage.querySelector('span').textContent = message;
    }
      // Performs the token verification AJAX call
    async function performTokenVerification() {
        if (token) {
            const response = await $.ajax({
                url: "/accounts/api/v1/verify-token?token=" + token, // Token will be in the POST body
                method: "POST",
                beforeSend: function (request) {
                    request.setRequestHeader("X-Language", getCurrentLanguage());
                },
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify({ token: token, action: "reset_password" }),
                success: function (res) {
                    console.log("link still avaiable")
                },
                error: function (failMsg) {
                    console.log(failMsg);
                    if (handleTokenExpired(failMsg)) { return } else {
                        window.location.href = '/accounts/';
                    }
                }
            });
        }
    }
    // Handle form submission
    $('#resetPasswordForm').submit(async function(event) {
        event.preventDefault();
        // await performTokenVerification();
        const submitBtn = document.getElementById("submit-btn");
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        // Force validate both fields on form submission
        confirmPasswordInteracted = true;
        
        // Validate before submission
        if (!validateNewPassword() || !validateConfirmPassword()) {
            return;
        }

        submitBtn.disabled = true;
        
        try {
            const response = await $.ajax({
                type: "PUT",
                beforeSend: function(request) {
                    request.setRequestHeader("Accept", "application/json");
                    request.setRequestHeader("X-Language", getCurrentLanguage());
                },
                dataType: "json",
                url: "../api/v1/users/forgot-password",
                data: JSON.stringify({
                    newPassword: newPassword,
                    confirmPassword: confirmPassword,
                    token: new URLSearchParams(window.location.search).get("token")
                }),
                processData: false,
                success: function(response) {
                    window.location.href = "/accounts/resetpassword/success.html";
                },
                error: function(error) {
                    if (handleSessionExpired(error)) { return } else if (handleSessionUnauthorized(error)) { return } else {
                        submitBtn.disabled = false;
                        if (error.responseJSON && error.responseJSON.responseDescription) {
                            showError(error.responseJSON.responseDescription);
                        } else {
                            showError(translations[getCurrentLanguage()]["resetError"]);
                        }
                    }
                }
            });
        } catch (error) {
            console.error("Error in form submission:", error);
            submitBtn.disabled = false;
            if (error.responseJSON && error.responseJSON.responseDescription) {
                showError(error.responseJSON.responseDescription);
            } else {
                showError(translations[getCurrentLanguage()]["resetError"]);
            }
        }
    });

    function handleSessionExpired(failMsg) {
        // if (
        //   failMsg &&
        //   failMsg.responseJSON &&
        //   failMsg.responseJSON.responseCode === "ONEAM_SESSION_EXPIRED.401"
        // ) {
        //   window.location.href = '/forgetpassword/linkexpired.html';
        //   return true;
        // }
        return false;
      }
      
      function handleSessionUnauthorized(failMsg) {
        // if (
        //   failMsg &&
        //   failMsg.responseJSON &&
        //   failMsg.responseJSON.responseCode === "ONEAM_USER_SESSION_UNAUTHOR.401"
        // ) {
        //   window.location.href = '/accounts/';
        //   return true;
        // }
        return false;
      }
}); 
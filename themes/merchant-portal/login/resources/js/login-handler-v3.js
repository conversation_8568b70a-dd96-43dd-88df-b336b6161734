// Keycloak Login Script - Copied from OneAM with Keycloak adaptations
console.log('Keycloak login script loaded');

// Function to validate password length
function validatePasswordInput(input) {
    const minLength = 1;
    if (input.value.length < minLength) {
        input.classList.add('invalid-password');
    } else {
        input.classList.remove('invalid-password');
    }
}

// Function to validate email/username input
function validateEmailInput(input) {
    if (input.value.trim() !== '') {
        // Remove Vietnamese characters and diacritics
        const vietnameseChars = /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/g;
        const normalized = input.value.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
        const withoutVietnamese = normalized.replace(vietnameseChars, '');
        
        if (input.value !== withoutVietnamese) {
            input.value = withoutVietnamese;
        }
        // Check length only if not empty
        const minLength = 1;
        if (input.value.length < minLength) {
            input.classList.add('invalid-email');
        } else {
            input.classList.remove('invalid-email');
        }
    } else {
        // Remove invalid class if empty
        input.classList.remove('invalid-email');
    }
}

// Function to check login fields and control button state (from OneAM)
function checkLoginFields() {
    const username = document.getElementById('username');
    const password = document.getElementById('password');
    const loginBtn = document.getElementById('kc-login');
    
    console.log('checkLoginFields called', {
        username: !!username,
        password: !!password,
        loginBtn: !!loginBtn
    });
    
    if (!username || !password || !loginBtn) {
        console.error('Missing elements!');
        return; // Safety check
    }

    const isUsernameValid = !username.classList.contains('invalid-email');
    const isPasswordValid = !password.classList.contains('invalid-password');
    const hasUsernameValue = username.value.trim() !== '';
    const hasPasswordValue = password.value.trim() !== '';
    
    console.log('Field states:', {
        hasUsernameValue,
        hasPasswordValue,
        isUsernameValid,
        isPasswordValid,
        currentDisabled: loginBtn.disabled
    });
    
    // Enable button only if both fields are valid and have values
    if (hasUsernameValue && hasPasswordValue && isUsernameValid && isPasswordValid) {
        loginBtn.disabled = false;
        console.log('Button ENABLED');
    } else {
        loginBtn.disabled = true;
        console.log('Button DISABLED');
    }
}

// Password toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded - Password toggle setup');
    
    const passwordInput = document.getElementById('password');
    const toggleButton = document.querySelector('.password-toggle');
    const errorMessage = document.querySelector('.error-message');
    
    if (passwordInput && toggleButton) {
        const toggleIcon = toggleButton.querySelector('img');
        
        // Add password validation
        passwordInput.addEventListener('input', function() {
            console.log('Password input triggered');
            validatePasswordInput(passwordInput);
            // Only hide error when user is actively typing (not on page load)
            if (errorMessage && document.activeElement === passwordInput) {
                errorMessage.style.display = 'none';
            }
            checkLoginFields();
        });
        
        toggleButton.addEventListener('click', function() {
            // Toggle password visibility
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            // Toggle icon - same as OneAM
            if (toggleIcon) {
                const currentSrc = toggleIcon.getAttribute('src');
                const basePath = currentSrc.substring(0, currentSrc.lastIndexOf('/') + 1);
                const iconSrc = type === 'password' ? basePath + 'eye.svg' : basePath + 'eye-off.svg';
                toggleIcon.setAttribute('src', iconSrc);
                toggleIcon.setAttribute('alt', type === 'password' ? 'Show password' : 'Hide password');
            }
        });
    }
});

// Email/Username clear button functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded - Clear button setup');
    
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const clearButton = document.querySelector('.clear-toggle');
    const errorMessage = document.querySelector('.error-message');
    
    if (usernameInput) {
        // Function to toggle clear button visibility
        function toggleClearButton() {
            if (clearButton) {
                if (usernameInput.value.length > 0) {
                    clearButton.style.display = 'block';
                } else {
                    clearButton.style.display = 'none';
                }
            }
        }
        
        usernameInput.addEventListener('input', function() {
            console.log('Username input triggered');
            validateEmailInput(usernameInput);
            toggleClearButton();
            // Only hide error when user is actively typing (not on page load)
            if (errorMessage && document.activeElement === usernameInput) {
                errorMessage.style.display = 'none';
            }
            checkLoginFields();
        });
        
        // Hide error message when password input changes
        if (passwordInput) {
            passwordInput.addEventListener('input', function() {
                // Only hide error when user is actively typing
                if (errorMessage && document.activeElement === passwordInput) {
                    errorMessage.style.display = 'none';
                }
            });
        }
        
        if (clearButton) {
            clearButton.addEventListener('click', function() {
                console.log('Clear button clicked');
                usernameInput.value = '';
                usernameInput.focus();
                toggleClearButton();
                if (errorMessage) {
                    errorMessage.style.display = 'none';
                }
                checkLoginFields();
            });
        }
        
        // Initialize clear button state
        toggleClearButton();
    }
});

// Add event listeners for input fields and initial check
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded - Event listeners setup');
    
    const username = document.getElementById('username');
    const password = document.getElementById('password');
    
    console.log('Elements:', {
        username: !!username,
        password: !!password,
        usernameValue: username ? username.value : 'N/A',
        passwordValue: password ? password.value : 'N/A'
    });
    
    if (username && password) {
        username.addEventListener('input', function() {
            console.log('Username input event');
            checkLoginFields();
        });
        password.addEventListener('input', function() {
            console.log('Password input event');
            checkLoginFields();
        });
        console.log('Event listeners added');
        checkLoginFields(); // Initial check
    } else {
        console.error('Username or password element not found!');
    }
});

$(document).ready(function () {
    const emailInput = document.getElementById('email');
    const clearButton = document.querySelector('.clear-toggle');
    const errorMessageSmall = document.querySelector('.error-message-small');
    const errorMessage = document.querySelector('.error-message');
    const submitButton = document.querySelector('.submit-btn');

    $("#forgetPasswordForm").submit(function (event) {
        event.preventDefault();
        submitForgetPassword();
    });

    function submitForgetPassword() {
        
        var email = $("#email").val();
        
        if (!validateEmail(email)) {
            showErrorSmall(translations[getCurrentLanguage()]["wrongEmailFormat"]);
            return;
        }

        var data = {
            email: email
        };
        submitButton.disabled = true;
        if(submitButton.disabled)
        $.ajax({
            type: "PUT",
            beforeSend: function (request) {
                request.setRequestHeader("Accept", "application/json");
                request.setRequestHeader("X-Language", getCurrentLanguage());
            },
            dataType: "json",
            url: "../api/v1/users?_action=forgotPassword",
            data: JSON.stringify(data),
            processData: false,
            success: function (msg) {
                console.log(msg);
                hideErrorSmall();
                hideError();
                window.location.href = `/accounts/forgetpassword/success.html`;
                // Thêm xử lý thành công ở đây nếu cần
            },
            error: function (failMsg) {
                submitButton.disabled = false;
                console.log(failMsg);
                var error = translations[getCurrentLanguage()]["SystemError"];
                if (failMsg && failMsg.responseJSON && failMsg.responseJSON.responseDescription) {
                    error = failMsg.responseJSON.responseDescription;
                }
                showError(error);
                hideErrorSmall();
            }
        });
    }

    function validateEmail(email) {
        if (!email) return true; // Không validate khi input trống
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function showError(message) {
        errorMessage.style.display = 'flex';
        errorMessage.querySelector('span').textContent = message;
    }

    function hideError() {
        errorMessage.style.display = 'none';
    }

    function showErrorSmall(message) {
        emailInput.classList.add('error');
        errorMessageSmall.classList.add('show');
        errorMessageSmall.querySelector('span').textContent = message;
    }

    function hideErrorSmall() {
        emailInput.classList.remove('error');
        errorMessageSmall.classList.remove('show');
    }

    // Toggle clear button visibility
    function toggleClearButton() {
        if (emailInput.value.length > 0) {
            clearButton.style.display = 'block';
        } else {
            clearButton.style.display = 'none';
        }
    }

    // Toggle submit button state
    function toggleSubmitButton() {
        const email = emailInput.value;
        submitButton.disabled = !email || !validateEmail(email);
    }

    // Handle input changes
    emailInput.addEventListener('input', function() {
        toggleClearButton();
        toggleSubmitButton();
        
        // Kiểm tra và ẩn error nếu email hợp lệ
        if (validateEmail(this.value)) {
            hideErrorSmall();
        }
        hideError(); // Ẩn error message chính khi người dùng bắt đầu nhập lại
    });

    // Handle input focus
    emailInput.addEventListener('focus', function() {
        hideErrorSmall();
        hideError();
    });

    // Handle input blur
    emailInput.addEventListener('blur', function() {
        if (this.value && !validateEmail(this.value)) {
            showErrorSmall(translations[getCurrentLanguage()]["wrongEmailFormat"]);
        }
    });
    
    // Handle clear button click
    clearButton.addEventListener('click', function() {
        emailInput.value = '';
        emailInput.focus();
        toggleClearButton();
        toggleSubmitButton();
        hideErrorSmall();
        hideError();
    });

    // Initialize states
    toggleClearButton();
    toggleSubmitButton();
}); 
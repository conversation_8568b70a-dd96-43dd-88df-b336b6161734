<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('password','password-confirm') displayInfo=false pageType="reset-password">
    <div class="wrap_select_lang">
        <div class="custom-lang-selector" id="customLangSelector">
            <div class="lang-selected" id="langSelected">
                <img src="${url.resourcesPath}/assets/en_lang.svg" class="lang-flag" id="langSelectedFlag">
                <span class="lang-label" id="langSelectedLabel">EN</span>
                <span class="lang-caret"><img src="${url.resourcesPath}/assets/chevron-down.svg" alt="caret-down" class="caret-down" id="langCaret"></span>
            </div>
            <div class="lang-options" id="langOptions" style="display:none;">
                <div class="header_select_lang_mobile" translate="selectLanguage"></div>
                <div class="lang-option" data-lang="en">
                    <img src="${url.resourcesPath}/assets/en_lang.svg" class="lang-flag" alt="EN"> <span data-translate-mobile="en">EN</span> <img class="icon_lang_checked" src="${url.resourcesPath}/assets/lang_check.svg" alt=""/>
                </div>
                <div class="lang-option" data-lang="vi">
                    <img src="${url.resourcesPath}/assets/vi_lang.svg" class="lang-flag" alt="VI"> <span data-translate-mobile="vi">VI</span> <img class="icon_lang_checked" src="${url.resourcesPath}/assets/lang_check.svg" alt=""/>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row min-vh-100 align-items-center">
        <div class="forget-password-container container-responsive">
            <div class="logo-container center-logo">
                <img src="${url.resourcesPath}/assets/logo-onepay.svg" alt="OnePay Logo" class="logo">
            </div>
            
            <h1 class="welcome-text" translate="resetPassword">Reset password</h1>
            
            <#-- Error messages -->
            <#if messagesPerField.existsError('password','password-confirm')>
                <div class="error-message mb-3" style="display: flex !important;">
                    <img src="${url.resourcesPath}/assets/xcircle-red.svg" alt="Error" class="error-icon">
                    <span>${kcSanitize(messagesPerField.getFirstError('password','password-confirm'))?no_esc}</span>
                </div>
            <#elseif message?has_content && (message.type == 'error' || message.type == 'warning')>
                <div class="error-message mb-3" style="display: flex !important;">
                    <img src="${url.resourcesPath}/assets/xcircle-red.svg" alt="Error" class="error-icon">
                    <span>${kcSanitize(message.summary)?no_esc}</span>
                </div>
            </#if>
            
            <div class="reset-password-container">
                <form id="kc-passwd-update-form" action="${url.loginAction}" method="post">
                    <div class="form-group">
                        <div class="form-floating password-container">
                            <input type="password" class="form-control custom-input" id="password-new" name="password-new" 
                                   autocomplete="new-password" autofocus>
                            <label for="password-new" translate="newPassword">New password</label>
                            <span class="password-toggle">
                                <img src="${url.resourcesPath}/assets/eye-off.svg" alt="Toggle password" class="eye-icon">
                            </span>
                        </div>
                        <div class="error-message-small">
                            <span></span>
                        </div>
                        <div class="password-requirements">
                            <ul>
                                <li id="length-req" class="invalid"><span translate="passwordMinLength">Minimum of 12 characters</span></li>
                                <li id="lowercase-req" class="invalid"><span translate="passwordLowercase">At least one lowercase letter</span></li>
                                <li id="uppercase-req" class="invalid"><span translate="passwordUppercase">At least one uppercase letter</span></li>
                                <li id="number-req" class="invalid"><span translate="passwordNumber">At least one number</span></li>
                                <li id="special-req" class="invalid"><span translate="passwordSpecial">At least one special character: @ $ ! % ? &</span></li>
                                <li id="general-req" class="invalid"><span translate="matchesRegex">Only contain letters (a-Z), numbers (0-9) & common special characters</span></li>
                            </ul>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-floating password-container">
                            <input type="password" class="form-control custom-input" id="password-confirm" name="password-confirm">
                            <label for="password-confirm" translate="confirmNewPassword">Confirm new password</label>
                            <span class="password-toggle">
                                <img src="${url.resourcesPath}/assets/eye-off.svg" alt="Toggle password" class="eye-icon">
                            </span>
                        </div>
                        <div class="error-message-small">
                            <img src="${url.resourcesPath}/assets/icon-info-red.svg" alt="Error" class="error-icon-small">
                            <span></span>
                        </div>
                    </div>

                    <button type="submit" id="kc-submit" class="btn btn-primary btn-block submit-btn" disabled translate="submit">Submit</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="toast-container">
        <div class="error-toast">
            <img src="${url.resourcesPath}/assets/alert-circle-white.svg" alt="Error" class="error-icon">
            <span class="error-message" translate="resetError">An error occurred. Please try again.</span>
        </div>
    </div>
    
    <div id="overlay_bg"></div>
</@layout.registrationLayout>


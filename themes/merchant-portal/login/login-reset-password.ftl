<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=false displayInfo=false pageType="forgot-password">
    <div class="wrap_select_lang">
        <div class="custom-lang-selector" id="customLangSelector">
            <div class="lang-selected" id="langSelected">
                <img src="${url.resourcesPath}/assets/en_lang.svg" class="lang-flag" id="langSelectedFlag">
                <span class="lang-label" id="langSelectedLabel">EN</span>
                <span class="lang-caret"><img src="${url.resourcesPath}/assets/chevron-down.svg" alt="caret-down" class="caret-down" id="langCaret"></span>
            </div>
            <div class="lang-options" id="langOptions" style="display:none;">
                <div class="header_select_lang_mobile" translate="selectLanguage"></div>
                <div class="lang-option" data-lang="en">
                    <img src="${url.resourcesPath}/assets/en_lang.svg" class="lang-flag" alt="EN"> <span data-translate-mobile="en">EN</span> <img class="icon_lang_checked" src="${url.resourcesPath}/assets/lang_check.svg" alt=""/>
                </div>
                <div class="lang-option" data-lang="vi">
                    <img src="${url.resourcesPath}/assets/vi_lang.svg" class="lang-flag" alt="VI"><span data-translate-mobile="vi">VI</span> <img class="icon_lang_checked" src="${url.resourcesPath}/assets/lang_check.svg" alt=""/>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row min-vh-100 align-items-center">
        <div class="forget-password-container container-responsive">
            <div class="logo-container center-logo">
                <img src="${url.resourcesPath}/assets/logo-onepay.svg" alt="OnePay Logo" class="logo">
            </div>
            
            <h1 class="welcome-text" translate="welcomeForgotPassword">Forgot Password</h1>
            
            <#-- Success message (green) -->
            <#if message?has_content && message.type == 'success'>
                <div class="alert alert-success mb-3" style="display: flex !important; align-items: center; gap: 8px; padding: 12px 16px; border-radius: 8px; background: #D1FAE5; color: #065F46; font-size: 14px;">
                    <span>✓ ${kcSanitize(message.summary)?no_esc}</span>
                </div>
            </#if>
            
            <#-- Error messages (red) -->
            <#if messagesPerField.existsError('username')>
                <div class="error-message mb-3" style="display: flex !important;">
                    <img src="${url.resourcesPath}/assets/xcircle-red.svg" alt="Error" class="error-icon">
                    <span>${kcSanitize(messagesPerField.getFirstError('username'))?no_esc}</span>
                </div>
            <#elseif message?has_content && (message.type == 'error' || message.type == 'warning')>
                <div class="error-message mb-3" style="display: flex !important;">
                    <img src="${url.resourcesPath}/assets/xcircle-red.svg" alt="Error" class="error-icon">
                    <span>${kcSanitize(message.summary)?no_esc}</span>
                </div>
            </#if>
            
            <p class="instruction-text" translate="forgetPasswordInstruction">
                We will send the guidance email for resetting your password to the email you enter here
            </p>
            
            <form id="kc-reset-password-form" action="${url.loginAction}" method="post">
                <div class="form-group mb-3">
                    <div class="form-floating email-container">
                        <input type="text" class="form-control custom-input" id="username" name="username" 
                               value="${(auth.username!'')}" autocomplete="off" autofocus>
                        <label for="username" translate="email">Email</label>
                        <span class="clear-toggle">
                            <img src="${url.resourcesPath}/assets/xcircle.svg" alt="Clear input" class="clear-icon">
                        </span>
                    </div>
                    <div class="error-message-small" style="display: none;">
                        <img src="${url.resourcesPath}/assets/icon-info-red.svg" alt="Error" class="error-icon-small">
                        <span translate="wrongEmailFormat">Wrong email format</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-block login-btn" id="kc-submit" translate="doSubmit">
                        Submit
                    </button>
                </div>
                
                <div class="form-group">
                    <div id="kc-form-options">
                        <div class="form-group">
                            <a href="${url.loginUrl}" class="btn btn-link" translate="backToLogin">Back to Login</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</@layout.registrationLayout>

<#macro registrationLayout bodyClass="" displayInfo=false displayMessage=true displayRequiredFields=false pageType="login">
    <#assign cacheVersion = "20251003-003">
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>OnePay Merchant Portal Login</title>
        <link rel="stylesheet" href="${url.resourcesPath}/css/bootstrap.min.css?v=${cacheVersion}">
        <link rel="stylesheet" href="${url.resourcesPath}/css/fonts-styles.css?v=${cacheVersion}">
        <link rel="stylesheet" href="${url.resourcesPath}/css/common-styles.css?v=${cacheVersion}">
        <link rel="stylesheet" href="${url.resourcesPath}/css/login-styles.css?v=${cacheVersion}">
        <#if pageType == "forgot-password">
        <link rel="stylesheet" href="${url.resourcesPath}/css/forgetpassword-styles.css?v=${cacheVersion}">
        </#if>
        <#if pageType == "email-success">
        <link rel="stylesheet" href="${url.resourcesPath}/css/email_success.css?v=${cacheVersion}">
        </#if>
        <#if pageType == "reset-password">
        <link rel="stylesheet" href="${url.resourcesPath}/css/resetpassword.css?v=${cacheVersion}">
        </#if>
        <#if pageType == "otp">
        <link rel="stylesheet" href="${url.resourcesPath}/css/verify-otp-styles.css?v=${cacheVersion}">
        </#if>
        <link rel="stylesheet" href="${url.resourcesPath}/css/responsive-styles.css?v=${cacheVersion}">
        <link rel="shortcut icon" href="${url.resourcesPath}/assets/favicon.ico"/>
        <script src="${url.resourcesPath}/js/jquery-3.3.1.min.js?v=${cacheVersion}"></script>
        <script src="${url.resourcesPath}/js/bootstrap.min.js?v=${cacheVersion}"></script>
        <script src="${url.resourcesPath}/js/translations-v2.js"></script>
        <#if pageType == "login">
        <script src="${url.resourcesPath}/js/login-handler-v3.js"></script>
        </#if>
        <#if pageType == "forgot-password">
        <script src="${url.resourcesPath}/js/forgetpassword-handler.js"></script>
        </#if>
        <#if pageType == "reset-password">
        <script src="${url.resourcesPath}/js/resetpassword.js"></script>
        </#if>
        <!-- Commented out OneAM legacy scripts that cause auto-reload -->
        <!-- <script src="${url.resourcesPath}/js/login.js"></script> -->
        <!-- <script src="${url.resourcesPath}/js/otp.js"></script> -->
    </head>
    <body>
        <div class="container">
            <div style="width: 100%">
                <#nested>
            </div>
        </div>
        
        <footer class="footer">
            <div class="container">
                <div class="footer-content">
                    <div class="text-center">
                        <img src="${url.resourcesPath}/assets/powered-by-onepay.svg" alt="Powered by OnePay" class="powered-logo">
                    </div>
                    <div class="security-badges">
                        <img src="${url.resourcesPath}/assets/verified-visa.png" alt="Verified by Visa">
                        <img src="${url.resourcesPath}/assets/mastercard-secure.png" alt="Mastercard SecureCode">
                        <img src="${url.resourcesPath}/assets/safekey.png" alt="American Express SafeKey">
                        <img src="${url.resourcesPath}/assets/jcb.png" alt="JCB">
                        <img src="${url.resourcesPath}/assets/pci-dss.png" alt="PCI DSS">
                    </div>
                </div>
            </div>
            <div class="copyright-wrapper">
                <div class="contact-info"><span translate="footerTel">Tel:</span><a href="tel:+842439366668"> (+84) 24 3936 6668</a> | Email: <a href="mailto:<EMAIL>"> <EMAIL></a>
                <div class="container">
                    <div class="copyright" translate="copyright">
                        Copyright © 2006 - 2025 OnePay. All rights reserved.
                    </div>
                </div>
            </div>
        </footer>

        <div id="overlay_bg"></div>
    </body>
    </html>
</#macro>
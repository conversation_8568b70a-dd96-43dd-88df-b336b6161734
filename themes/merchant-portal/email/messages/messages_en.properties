# OnePay Merchant Portal Keycloak Theme - English Messages
loginTitleHtml=Merchant Portal Login
welcome=Welcome to Merchant Portal
emailMobile=Email/ Mobile number
password=Password
login=Log in
forgotPassword=Forgot password?
doForgotPassword=Forgot password?
doLogIn=Log in
rememberMe=Remember me
footerTel=Tel:
copyright=Copyright © 2006 - 2025 OnePay. All rights reserved.
selectLanguage=Select Language
error=Error
invalidUsernameOrPassword=Account or password is incorrect
invalidUserMessage=Account or password is incorrect
loginFailMessage=Account or password is incorrect
usernameOrEmail=Username or email

# Keycloak built-in error messages - Override to match OneAM database messages

# OneAM.OAUTH2.PASSWORD.WRONG (406) - Wrong password/username
invalidUserMessage=User or password wrong
invalidPasswordMessage=User or password wrong
invalidCredentialsMessage=User or password wrong
invalidUsernameOrPassword=User or password wrong

# OneAM.OAUTH2.USER.INACTIVE (423) - User inactive  
userTemporarilyDisabledMessage=User inactive
userDisabledMessage=User inactive
disabledUsernameMessage=User inactive

# OneAM.USER.NOTFOUND (404) - User not found
usernameExistsMessage=User is not found
userNotFoundMessage=User is not found

# OneAM.SYSTEM.ERROR (500) - System error
loginTimeoutMessage=System error!
serverErrorMessage=System error!

# OTP Verification
twoStepVerificationTitle=Two-Step Verification
enterOtpCode=Enter the OTP code sent to your email
troubleOtp=Having trouble with OTP code?
reRegisterOtp=Resend OTP code here
trustDevice=Trust this device (don't ask for OTP for 90 days)

# Forgot Password
passwordResetSubject=OnePay MA - Reset Password
welcomeForgotPassword=Forgot Password
forgetPasswordInstruction=We will send the guidance email for resetting your password to the email you enter here
email=Email
submit=Submit
wrongEmailFormat=Wrong email format
emailNotFound=404 - Email not found
accountDisabled=423 - Account is disabled
emailSendError=500 - Error sending email. Please try again later
missingUsernameMessage=400 - Please enter email
emailSentMessage=Password reset instructions have been sent to your email. Please check your inbox.
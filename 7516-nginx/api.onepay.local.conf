upstream ma-service-ssl {
    server 10-36-71-108.onepay.local:443;
    server 10-36-71-109.onepay.local:443 backup;
    keepalive 32;
}

upstream onecomm-payservice-ssl {
    server 10-36-71-100.onepay.local:443;
    server 10-36-71-101.onepay.local:443 backup;
}

upstream onecomm-service-refund-ssl {
    server 10-36-71-100.onepay.local:443;
    server 10-36-71-101.onepay.local:443 backup;
}

upstream onecredit-service-ssl {
    server 10-36-71-100.onepay.local:443;
    server 10-36-71-101.onepay.local:443 backup;
}

upstream iportal-service-ssl {
    server 10-36-71-108.onepay.local:443 backup;
    server 10-36-71-109.onepay.local:443;
    keepalive 32;
}

upstream iportal-service-card-ssl {
    #server 10-36-71-108.onepay.local:443 backup;
    server 10-36-71-102.onepay.local:443;
    keepalive 32;
}


upstream iportal-service-ipn {
    server 10-36-144-42.onepay.local:443 ;
    server 10-36-144-43.onepay.local:443 ;
    keepalive 32;
}

upstream iportal-service-ipn-prod1 {
    server 10-36-144-43.onepay.local:443 ;
    keepalive 32;
}

upstream iportal-service-ipn-stg {
    server 10-36-144-52.onepay.local:443 ;
    keepalive 32;
}



map "$http_cookie" $api_iportal_service_ipn {
    default "https://iportal-service-ipn";
    "~^.*env=stg" "https://iportal-service-ipn-stg";
    "~^.*env=prod1" "https://iportal-service-ipn-prod1";
}


map $http_referer $http_referer_hostname {
        ~(^.*://[^?/]+.*\?).*$ $1;
}

upstream iportal-frontend {
    server 127.0.0.1:8483;
    keepalive 32;
}

upstream pci-16444-16445 {
    server ************:445;
    server ************:445 backup;
    keepalive 32;
}

server {
    listen                      443 ssl;
    server_name                 api.onepay.local;
    ssl_certificate             /etc/nginx/conf.d/api.onepay.local.cer;
    ssl_certificate_key         /etc/nginx/conf.d/api.onepay.local.key;
    ssl_client_certificate      /etc/nginx/conf.d/onepayrootca.cer;
    ssl_verify_client           on;

    ssl_protocols TLSv1.2;
    ssl_ciphers HIGH:!aNULL:!eNULL:!PSK:!RC4:!MD5:!3DES;
    ssl_prefer_server_ciphers   on;

    ## for call https to other server
    proxy_http_version 1.1;
    proxy_set_header Host api.onepay.local;

    proxy_ssl_certificate           /etc/nginx/conf.d/10-36-75-116.onepay.local.cer;
    proxy_ssl_certificate_key       /etc/nginx/conf.d/10-36-75-116.onepay.local.key;
    proxy_ssl_trusted_certificate   /etc/nginx/conf.d/onepayrootca.cer;
    proxy_ssl_name                  api.onepay.local;
    proxy_ssl_verify                on;
    proxy_ssl_server_name           on;

    access_log /var/log/nginx/access.log no_query;

    location ~ ^/iportal/api/v1/ipn/.+$ {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        expires 0;
        #proxy_pass https://iportal-service-ipn;
        proxy_pass $api_iportal_service_ipn;
    }

    location ~ /iportal/api/v1/card/query-transaction-id {
        access_log /var/log/nginx/access.log no_query;

        proxy_http_version 1.1;
        proxy_redirect off;
        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;

        proxy_ssl_name pci.onepay.local;
        proxy_set_header Host pci.onepay.local;
        proxy_set_header Connection "";
        proxy_set_header Authorization "Basic aXBvcnRhbC13ZWI6NGozNGopIyUz";
        proxy_next_upstream error http_502 non_idempotent;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        rewrite /iportal/api/v1/card/query-transaction-id /card-helper/api/v1/charge-back break;
        proxy_pass https://pci-16444-16445;       
    }



    location ~ /iportal/api/v1/card {
        access_log /var/log/nginx/access.log no_query;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass https://iportal-service-card-ssl;
    }

    location ~ /iportal/api/v1/auth  {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        rewrite ^/iportal/api/v1(/.*)$ $1 break;
        proxy_pass http://iportal-frontend;
    }


#    location ~ ^/iportal/(login|api/v1/.+|api/v2/.+)$ {
#        access_log /var/log/nginx/access.log no_query;
#        #proxy_set_header        X-Forwarded-Proto $scheme;
#        proxy_read_timeout 600;
#        proxy_connect_timeout 600;
#        proxy_send_timeout 600;
#        expires 0;
#        proxy_pass https://iportal-service-ssl;
#    }

    
    # location ~ ^/iportal/api/v1/miniapp/app/data {
    #    #proxy_set_header        X-Forwarded-Proto $scheme;
    #    proxy_read_timeout 600;
    #    proxy_connect_timeout 600;
    #    proxy_send_timeout 600;
    #    expires 0;
    #     proxy_pass https://pci-14442-14443;
    # }
    

    location ~ ^/iportal/(api/v1/.+|api/v2/.+)$ {
        #proxy_set_header        X-Forwarded-Proto $scheme;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        expires 0;
        #proxy_pass https://iportal-service-ssl;
		
		proxy_pass http://iportal-frontend;
    }

    location ~ /iportal/(login|login2)$ {
        expires 0;
        rewrite ^/iportal(/.*)$ $1 break;
        proxy_pass http://iportal-frontend;
    }


    location /ma/api/v1 {
        access_log /var/log/nginx/access.log no_query;
        gzip  on;
        gzip_min_length 1000;
        gzip_types    application/json;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
        rewrite ^/ma/api/v1/(.*) /$1 break;
        proxy_pass http://127.0.0.1:8180;
    }

    location ~ ^/ma/eventbus/(.*) {
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_read_timeout 60s;
        rewrite ^/ma/eventbus/(.*) /eventbus/$1 break;
        proxy_pass http://127.0.0.1:8380;
    }
    
    location /plba/api/v1 {
        gzip  on;
        gzip_min_length 1000;
        gzip_types    application/json;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
        rewrite ^/plba/api/v1/(.*) /plba/$1 break;
        proxy_pass http://127.0.0.1:8180;
    }

    location /paymentlink/login {
        gzip  on;
        gzip_min_length 1000;
        gzip_types    application/json;
        gzip_disable "MSIE [1-6]\.(?!.*SV1)";
        gzip_proxied any;
        gzip_buffers 16 8k;
        gzip_vary on;
        rewrite ^/paymentlink(/.*)$ $1 break;
        proxy_pass http://127.0.0.1:8180;
    } 
}

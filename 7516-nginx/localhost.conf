upstream onecomm_service {
    server *************:8980;
    server *************:8980 backup;
    keepalive 32;
}

upstream onecredit_service {
    server 172.16.75.102:1066;
    server 172.16.75.103:1066 backup;
    keepalive 32;
}

upstream onecomm_service_refund {
    server *************:8880;
    server *************:8880 backup;
    keepalive 32;
}

upstream report_service {
    server *************:8480;
    server 172.16.75.109:8480 backup;
    keepalive 32;
}

upstream mavn_service {
    server *************:8480 backup;
    server 172.16.75.109:8480;
    keepalive 32;
}

upstream ma_service {
	server 172.16.75.109:80;
    server *************:80 backup;
    keepalive 32;
}

upstream tsp {
    server *************:28480 backup;
    server *************:28480;
    keepalive 32;
}

upstream 15446-15447-154146-ssl {
    server 10-36-154-46.onepay.local:443;
    server 10-36-154-47.onepay.local:443 backup;
    #server 10-36-154-146.onepay.local:443;
    keepalive 32;
}

upstream pci-14442-14443 {
    server ************:443;
    server ************:443 backup;
    keepalive 32;
}

server {
    listen       80;
    server_name  localhost;
    allow 127.0.0.1;
    ####deny all;
    proxy_http_version 1.1;
    proxy_set_header Connection "";
    proxy_redirect     off;


    location /onecomm-payservice/execute {
        proxy_pass http://onecomm_service;
    }

    location /onecomm-payservice-refund/execute {
        proxy_pass http://onecomm_service_refund;
    }

    location /onecredit/execute {
        proxy_pass http://onecredit_service;
    }

    location ~ ^/mavn-service/execute$ {
        proxy_pass http://mavn_service;
    }

    location ~ ^/m(a|m)vn-service/execute$ {
        proxy_pass http://report_service;
    }

    location ~ ^/ma-service/api/v1/plba/(.+)$ {
        proxy_set_header Host api.onepay.local;
        rewrite ^/ma-service/api/v1/plba/(.+)$ /plba/api/v1/$1 break;
        proxy_pass https://15446-15447-154146-ssl;
    }

    location /ma-service/api/v1/ {
        access_log /var/log/nginx/access.log no_query;
        #rewrite ^/ma-service/api/v1(.+)$ $1 break;
        proxy_pass http://ma_service;
    }

    location /tsp/api/v1/ {
        proxy_pass http://tsp;
    }

    location ~ ^/iportal-service/api/(v1|v2) {
        access_log /var/log/nginx/access.log no_query;
        proxy_ssl_certificate /etc/nginx/conf.d/_.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/_.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
	proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        rewrite ^/iportal-service/(.*) /iportal/$1 break;
        proxy_pass https://iportal-service-ssl;
    }


    location ^~ /iportal-service/api/v1/miniapp/ {
        proxy_ssl_certificate /etc/nginx/conf.d/_.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/_.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        rewrite ^/iportal-service/(.*) /iportal/$1 break;
        proxy_pass https://pci-14442-14443;
    }


}

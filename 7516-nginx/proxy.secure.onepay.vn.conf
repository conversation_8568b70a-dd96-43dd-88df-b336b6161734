# HTTP proxy from 76.4

upstream portal_service {
    ip_hash;
    server *************:9180;
    #server *************:9180;
    keepalive 32;
}

upstream oneshop_service {
    server *************:6080;
    server *************:6080 backup;
    keepalive 32;
}

upstream oneam_service {
    server *************:8680;
    server *************:8680 backup;
    keepalive 32;
}

#upstream fraud_report {
#    #ip_hash;
#    #server ***********:80;
#    server *************:9280;
#    server *************:9280 backup;
#    keepalive 32;
#}

upstream tsp_internet {
    server *************:28480 backup;
    server *************:28480;
    keepalive 32;
}

upstream tsp2_internet {
    server *************:28482;
    server *************:28482 backup;
    keepalive 32;
}

upstream onecredit_api {
    server *************:8280;
    server *************:8280 backup;
}

upstream smssp {
    #server *************:9380 backup;
    server *************:9380;
    keepalive 32;
}

upstream msp {
    server *************:28380;
    server *************:28380 backup;
    keepalive 32;
}

upstream iportal_service {
    #server *************:80 backup;
    server *************:80;
    keepalive 32;
}

upstream iportal_frontend {
    server 127.0.0.1:8379;
    keepalive 32;
}
server {
    listen 80;
    server_name proxy.secure.onepay.vn;

    access_log /var/log/nginx/access_proxy.log main;
    access_log syslog:server=log.onepay.vn main;

    allow **********;
    allow **********;
    ####deny all;

    proxy_http_version 1.1;
    proxy_set_header Connection "";
    proxy_set_header Host dev13-secure.opdev.vn;
    proxy_redirect off;
    client_max_body_size 100M;

    location ~ ^/(portal|mp|microtec)/.*  {
        proxy_pass  http://portal_service;
        ### force timeouts if one of backend is died ##
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    }

    #location ~ ^/fraud/.*  {
    #    proxy_pass  http://fraud_report;
    #    ### force timeouts if one of backend is died ##
    #    proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    #}

    location /oneshop {
        proxy_pass http://oneshop_service;
    }

    location /accounts/api/v1 {
        rewrite /accounts/api/v1/(.*) /$1 break;
        proxy_pass http://oneam_service;
    }

    location /tsp/api/v1/ {
        proxy_pass http://tsp_internet;
    }

    location /tsp/api/v2/ {
        proxy_pass http://tsp2_internet;
    }

    #Thanh toan tu kios CGV
    location /vpcpay/api/v1/payments {
        proxy_pass http://onecredit_api;
    }

    location /sms/api/v1 {
        proxy_pass http://smssp;
    }

    location /msp/api/v1/ {
        proxy_pass http://msp;
    }

    location ~ /iportal/(login|api/v1/.+)$ {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #rewrite /iportal(/login.*) $1 break;
        proxy_pass http://iportal_service;
    }
	
	 location ~ /iportal/login$ {
        expires 0;
        rewrite ^/iportal(/.*)$ $1 break;
        proxy_pass http://iportal_frontend;
    }

    #location /iportal/api/v1/ {
    #    proxy_read_timeout 600;
    #    proxy_connect_timeout 600;
    #    proxy_send_timeout 600;
    #    proxy_pass http://iportal_frontend;
    #}
}

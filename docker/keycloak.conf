# Keycloak configuration for dev13-ma.opdev.vn

# Hostname configuration
KC_HOSTNAME=dev13-ma.opdev.vn
KC_HOSTNAME_STRICT=false
KC_HOSTNAME_STRICT_HTTPS=false
KC_PROXY=edge

# Database configuration
KC_DB=postgres
KC_DB_URL=****************************************
KC_DB_USERNAME=keycloak
KC_DB_PASSWORD=keycloak

# Admin configuration
KC_HOSTNAME_ADMIN=dev13-ma.opdev.vn
KC_HOSTNAME_STRICT_HTTPS=false

# Logging
KC_LOG_LEVEL=INFO

# HTTP configuration
KC_HTTP_ENABLED=true
KC_HTTP_PORT=8080

# HTTPS configuration (if needed)
# KC_HTTPS_PORT=8443
# KC_HTTPS_CERTIFICATE_FILE=/opt/keycloak/conf/server.crt.pem
# KC_HTTPS_CERTIFICATE_KEY_FILE=/opt/keycloak/conf/server.key.pem

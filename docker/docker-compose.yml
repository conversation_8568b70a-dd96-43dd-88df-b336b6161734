version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: keycloak
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - keycloak-network

  keycloak:
    image: quay.io/keycloak/keycloak:26.2.5
    command: start-dev
    environment:
      # Hostname configuration
      KC_HOSTNAME: dev13-ma.opdev.vn
      KC_HOSTNAME_STRICT: false
      KC_HOSTNAME_STRICT_HTTPS: false
      KC_PROXY: edge
      
      # Database configuration
      KC_DB: postgres
      KC_DB_URL: ****************************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: keycloak
      
      # Admin configuration
      KC_HOSTNAME_ADMIN: dev13-ma.opdev.vn
      
      # HTTP configuration
      KC_HTTP_ENABLED: true
      KC_HTTP_PORT: 8080
      
      # Logging
      KC_LOG_LEVEL: INFO
      
      # Development mode
      KC_DEVELOPMENT: true
      
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    networks:
      - keycloak-network
    volumes:
      - ./keycloak.conf:/opt/keycloak/conf/keycloak.conf:ro

volumes:
  postgres_data:

networks:
  keycloak-network:
    driver: bridge

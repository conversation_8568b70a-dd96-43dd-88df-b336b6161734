server {
    listen                      443 ssl;
    server_name                 api.onepay.local;
    ssl_certificate             /etc/nginx/conf.d/api.onepay.local.cer;
    ssl_certificate_key         /etc/nginx/conf.d/api.onepay.local.key;
    ssl_client_certificate      /etc/nginx/conf.d/onepayrootca.cer;
    ssl_verify_client           on;

    ssl_protocols TLSv1.2;
    ssl_ciphers HIGH:!aNULL:!eNULL:!PSK:!RC4:!MD5:!3DES;
    ssl_prefer_server_ciphers   on;

    ## for call https to other server
    proxy_http_version 1.1;
    proxy_set_header Host api.onepay.local;

    proxy_ssl_certificate           /etc/nginx/conf.d/api.onepay.local.cer;
    proxy_ssl_certificate_key       /etc/nginx/conf.d/api.onepay.local.key;
    proxy_ssl_trusted_certificate   /etc/nginx/conf.d/onepayrootca.cer;
    proxy_ssl_name                  api.onepay.local;
    proxy_ssl_verify                on;
    proxy_ssl_server_name           on;

    #access_log /var/log/nginx/access.log no_query;

    location /bin_us_gb_ca.csv {
       root /opt/onesched;
    }

    location /iportal/login {
        rewrite ^/iportal(/.*)$ $1 break;
        #proxy_pass http://iportal-service;
        proxy_pass http://127.0.0.1:8380;
    }

    location /iportal/api/v1/bnpl {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-bnpl;
        proxy_pass http://127.0.0.1:8385;
    }

    location ~ "^/iportal/api/v1/e-wallet/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-ewallet;
    }

    location /iportal/api/v1/e-wallet {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-ewallet;
    }

    location /iportal/api/v1/fdm {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-fdm;
        proxy_pass http://127.0.0.1:8387;
    }

    location /iportal/api/v2/fdm {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-fdm;
    }

    location ~ "^/iportal/api/v1/bnpl/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-bnpl;
        proxy_pass http://127.0.0.1:8385;
    }

    location /iportal/api/v1/upos {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-upos;
        proxy_pass http://127.0.0.1:8386;
    }

    location /iportal/api/v1/vietqr {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://127.0.0.1:8388;
    }

    location ~ "^/iportal/api/v1/vietqr/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://127.0.0.1:8388;
    }

    location /iportal/api/v1/direct-debit {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://127.0.0.1:8389;
    }

    location ~ "^/iportal/api/v1/direct-debit/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://127.0.0.1:8389;
    }

    location ~ "^/iportal/api/v1/upos/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-upos;
        proxy_pass http://127.0.0.1:8386;
    }

    location ~ "^/iportal/api/v1/accountant-management-p2/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }


    location ~ "^/iportal/api/v1/accountant-management/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }

    location ~ "^/iportal/api/v1/accountant-management/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }

    location /iportal/api/v1/accountant {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }

    location /iportal/api/v1/accountant-p2 {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }

    location ~ "^/iportal/api/v1/accountant-p2/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }

    location ~ "^/iportal/api/v1/accountant-p2/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }

    location ~ "^/iportal/api/v1/accountant-p2/.*/template-file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }
    location ~ "^/iportal/api/v1/accountant/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }

    location ~ "^/iportal/api/v1/accountant/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }

    location ~ "^/iportal/api/v1/accountant/.*/template-file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }

    location ~ "^/iportal/api/v1/accountant/template-file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }

    location /iportal/api/v1/accountant-receipt {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }

    location ~ "^/iportal/api/v1/accountant-receipt/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }
    location ~ "^/iportal/api/v1/accountant-receipt/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }
    
    location ~ "^/iportal/api/v1/accountant-receipt-p2/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service-accountant;
        proxy_pass http://127.0.0.1:8384;
    }
	
	location /iportal/api/v1/merchantmonitorhung/ {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
		rewrite ^/iportal/api/v1/merchantmonitorhung/(.*)$ /merchant-monitor/api/v1/$1 break;
		proxy_pass http://merchant-monitor-ss-service;
	}
		
	location /iportal/api/v1/merchantmonitorthanh/ {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
		rewrite ^/iportal/api/v1/merchantmonitorthanh/(.*)$ /api/$1 break;
		proxy_pass http://merchant-monitor-mm-service;
	}
    
    location /iportal/api/v1 {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service;
        proxy_pass http://127.0.0.1:8380;
    }

    location ~ "^/iportal/api/v1/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        #proxy_pass http://iportal-service2;
        proxy_pass http://127.0.0.1:8382;
    }

    location ~ ^/ufiles/(.*)$ {
        root /opt/ufiles/;
        try_files /ma/$1 /iportal/$1 =404;
    }

    location /ma-service/transaction/vinpearl {
        proxy_pass http://ma_service3;

    }
	
	location /ma-service/api/v1/financial-report {
        proxy_pass http://127.0.0.1:8181;

    }
    #Auto call
    location /api/OmniMessage/SendMessage {
         proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_redirect off;
        proxy_ssl_name omni.incom.vn;
        proxy_ssl_verify off;
        proxy_set_header Host omni.incom.vn;
        proxy_pass https://omni.incom.vn;
    }
}

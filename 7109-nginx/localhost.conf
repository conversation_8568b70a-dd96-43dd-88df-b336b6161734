upstream 71100-71101-7110-ssl {
    server 10-36-71-100.onepay.local:443 ;
    server 10-36-71-101.onepay.local:443 backup;
    #server 10-36-71-10.onepay.local:443;
    keepalive 32;
}

upstream ma_eventbus {
    server 10.36.75.116:8380;
    server 10.36.75.116:8380 backup;
    keepalive 32;
}

upstream onecredit_service {
    server ************:1066;
    server ************:1066 backup;
    keepalive 32;
}

upstream onecomm_service {
    server ************:8980 ;
    server ************:8980 backup;
    keepalive 32;
}

upstream onecomm_bank {
    server ************:8380 ;
    server ************:8380 backup;
    keepalive 32;
}

upstream oauth2_service {
    server ************:8092;
    server ************:8092 backup;
}

upstream oauth2_service_user {
    server ************:8680;
    server ************:8680 backup;
}

upstream onesm_service {
    server ************:8880;
    server ************:8880 backup;
    keepalive 32;
}

upstream msp {
    server ************:28380;
    server ************:28380 backup;
    keepalive 32;
}

upstream msp_big {
    server ************:443;
    server ************:443 backup;
    keepalive 32;
}


upstream smssp {
    #server ************:9380 backup;
    server ************:9380;
    keepalive 32;
}

upstream portal_service {
    #server ************:8280;
    server ************:8280;
    keepalive 32;
}

upstream ipn {
    server ************:8480 backup;
    server ************:8480;
    keepalive 32;
}

upstream ipn2 {
    server ************:8482 backup;
    server ************:8482;
    keepalive 32;
}

upstream ma_service_local {
    server ************:80;
    keepalive 32;
}

upstream ma_service {
    server ************:8182 backup;
    server ************:8180 ;
    keepalive 32;
}

upstream ma_service2 {
    server ************:8182;
    server ************:8182 backup;
    keepalive 32;
}

upstream ma_service3 {
    server ************:8182 backup;
    server ************:8183;
    keepalive 32;
}

upstream ma_service4 {
    server ************:8184;
    keepalive 32;
}
upstream ma_service5 {
    server ************:8185;
    keepalive 32;
}

upstream ma-backend-go {
    server ************:8181;
    keepalive 32;
}


upstream ma_service_paycollect {
    server ************:8184 backup;
    server ************:8184;
    keepalive 32;
}

upstream ma-bnpl-backend {
    server ************:8235 backup;
    server ************:8235;
    keepalive 32;
}

upstream ma-upos-backend {
    server ************:8236 backup;
    server ************:8236;
    keepalive 32;
}
upstream ma-domestic-backend {
    server ************:8237;
    server ************:8237 backup;
    keepalive 32;
}
upstream ma-qr-backend {
    server ************:8241;
    server ************:8241 backup;
    keepalive 32;
}

upstream ma-vietqr-backend {
   server ************:8246;
   keepalive 32;
}

upstream ma-app-backend {
    server ************:8245;
	server ************:8245 backup;
    keepalive 32;
}

upstream ma-dd-backend {
	server ************:8247;
	server ************:8247 backup;
	keepalive 32;
}

upstream ma-service-quicklink {
   server ************:8242;
   server ************:8242 backup;
   keepalive 32;
}

upstream ma-service-promotion {
   server ************:8244;
   server ************:8244 backup;
   keepalive 32;
}

upstream ma-international-backend {
    server ************:8238;
    server ************:8238 backup;
    keepalive 32;
}

upstream ma-payout-backend {
    server ************:8248;
    server ************:8248 backup;
    keepalive 32;
}

upstream ma_service_general {
    server ************:8243;
    server ************:8243 backup;
    keepalive 32;
}

upstream quicklink_service {
    server ************:80 backup;
    server ************:80;
    keepalive 32;
}

upstream quicklink_service_prod {
    server ************:80 backup;
    server ************:80;
    keepalive 32;
}

upstream dispute_service {
    server ************:9098 backup;
    server ************:9098;
    keepalive 32;
}

upstream merchant_account_service {
    server ************:80;
    keepalive 32;
}

upstream portal_web {
    server ************:9180;
    #server ************:8180;
    keepalive 32;
}

upstream wsp {
    server ***********:18680;
    server ***********:18680 backup;
    keepalive 32;
}

upstream paycollect {
    server ************:29180;
    server ************:29180 backup;
    keepalive 32;
}

upstream onepayout-service {
    server ************:28381;
    server ************:28381 backup;
    #server ************:80;
    keepalive 32;
}

upstream iportal-service {
    server ************:8380 backup;
    server ************:8380;
    keepalive 32;
}

upstream iportal-service2 {
    server ************:8382 backup;
    server ************:8382;
    keepalive 32;
}
upstream iportal-service-accountant {
    server ************:8384;
    keepalive 32;
}
upstream iportal-service-bnpl {
    server ************:8385;
    keepalive 32;
}
upstream iportal-service-upos {
    server ************:8386;
    keepalive 32;
}
upstream iportal-service-fdm {
    server ************:8387;
    keepalive 32;
}
upstream iportal-fdm {
    server ************:8445;
   keepalive 32;
}

upstream advance-service {
    server ************:9680 backup;
    server ************:443;
    keepalive 32;
}
upstream onesched-service {
   server ************:443;
    keepalive 32;
}

upstream landing-page {
    server ************:9080;
    keepalive 32;
}

upstream iportal_statistic_service {
    server ************:9089;
    keepalive 32;
}

upstream pci-16444-16445 {
    server ************:445;
    server ************:445 backup;
    keepalive 32;
}
upstream pci-16445-16444 {
    server ************:445 backup;
    server ************:445;
    keepalive 32;
}

upstream api-16444-16445 {
    server ************:443;
    server ************:443 backup;
    keepalive 32;
}

upstream api-16445-16444 {
    server ************:443 backup;
    server ************:443;
    keepalive 32;
}


upstream iportal-service-ewallet {
   server ************:8391;
   keepalive 32;
}

upstream iportal-service-vietqr {
   server ************:8388;
   keepalive 32;
}

upstream mpgs {
    server *************:443;
    server ************:443 backup;
    server ************:443 backup;
    #server ap-gateway.mastercard.com:443;
    #server ***************:2443 backup;
    keepalive 10;
}

upstream merchant-monitor-ss-service {
    #server ************:8088;
	server ************:8088;
    keepalive 32;	
}
	
upstream merchant-monitor-mm-service {
    server ************:8090;	
    keepalive 32;	
}

server {
    listen 80;
    server_name ma_service ma_service_local ************* ************* ************ ************ ufiles;
    proxy_http_version 1.1;
    proxy_set_header Connection "";
    proxy_redirect off;
  
    access_log /var/log/nginx/access.log no_query;

    location /iportal/login {
        rewrite ^/iportal(/.*)$ $1 break;
        proxy_pass http://iportal-service;
    }

    location /iportal/api/v1/bnpl {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-bnpl;
    }

    location /iportal/api/v1/fdm {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-fdm;
    }

    location /iportal/api/v2/fdm {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-fdm;
    }

    location ~ "^/iportal/api/v1/bnpl/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-bnpl;
    }

    location /iportal/api/v1/upos {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-upos;
    }

    location ~ "^/iportal/api/v1/upos/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-upos;
    }

    location ~ "^/iportal/api/v1/accountant-management-p2/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }


    location ~ "^/iportal/api/v1/accountant-management/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }

    location ~ "^/iportal/api/v1/accountant-management/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }

    location /iportal/api/v1/accountant {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }

    location /iportal/api/v1/accountant-p2 {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }

    location ~ "^/iportal/api/v1/accountant-p2/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }

    location ~ "^/iportal/api/v1/accountant-p2/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }

    location ~ "^/iportal/api/v1/accountant-p2/.*/template-file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }
    location ~ "^/iportal/api/v1/accountant/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }

    location ~ "^/iportal/api/v1/accountant/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }

    location ~ "^/iportal/api/v1/accountant/.*/template-file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }

    location ~ "^/iportal/api/v1/accountant/template-file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }

    location /iportal/api/v1/accountant-receipt {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }

    location ~ "^/iportal/api/v1/accountant-receipt/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }
    location ~ "^/iportal/api/v1/accountant-receipt/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }
    
    location /iportal/api/v1/accountant-receipt-p2 {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-accountant;
    }

    location /iportal/api/v1 {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service;
    }

    location ~ "^/iportal/api/v1/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service2;
    }

    location ~ "^/iportal/api/v1/file/.*/download$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service;
        proxy_next_upstream error timeout http_404;
    }


    location /onecomm-napas/ipn {
        proxy_set_header Host null.onepay.vn;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_pass http://onecomm-napas;
    }

    location /iportal/api/v1/e-wallet {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-ewallet;
    }

    location ~ "^/iportal/api/v1/e-wallet/.*/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-ewallet;
    }

    location ~ "^/iportal/api/v1/e-wallet/file$" {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-ewallet;
    }

    location /ma-service/login {
        rewrite ^/ma-service(/.*)$ $1 break;
        proxy_pass http://ma_service;
    
	}
	
	location /ma-service/api/v1/financial-report {
		proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-backend-go;
    }

	
    location ~ "^/ma-service/api/v1/bnpl/.*/file$" {
        rewrite ^/ma-service/api/v1/(.+)$ /ma-bnpl/api/v1/$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-bnpl-backend;
    }

    location ~ "^/ma-service/api/v1/upos/.*/file$" {
        rewrite ^/ma-service/api/v1/(.+)$ /ma-upos/api/v1/$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-upos-backend;
    }

    location ~ "^/ma-service/api/v1/domestic/.*/file$" {
        rewrite ^/ma-service/api/v1/(.+)$ /ma-domestic/api/v1/$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-domestic-backend;
    }

    location ~ "^/ma-service/api/v1/qr/.*/file$" {
        rewrite ^/ma-service/api/v1/(.+)$ /ma-qr/api/v1/$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-qr-backend;
    }

    location ~ "^/ma-service/api/v1/vietqr/.*/file$" {
        rewrite ^/ma-service/api/v1/(.+)$ /ma-vietqr/api/v1/$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-vietqr-backend;
    }


    location ~ "^/ma-service/api/v1/onepayout/.*/file$" {
        rewrite ^/ma-service/api/v1/(.+)$ /ma-payout/api/v1/$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-payout-backend;
    }


    location ~ "^/ma-service/api/v1/international/.*/file$" {
        rewrite ^/ma-service/api/v1/(.+)$ /ma-international/api/v1/$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-international-backend;
    }

    location ~ "^/ma-service/api/v1/quicklink/.*/file$" {
        rewrite ^/ma-service/api/v1/(.+)$ /ma-quicklink/api/v1/$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-service-quicklink;
    }

    location ~ "^/ma-service/api/v1/promotion/.*/file$" {
        rewrite ^/ma-service/api/v1/(.+)$ /ma-pr/api/v1/$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-service-promotion;
    }

    location ~ "^/ma-service/api/v1/direct-debit/.*/file$" {
        rewrite ^/ma-service/api/v1/(.*) /ma-direct-debit/api/v1/$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-dd-backend;
    }

#    location ~ "^/ma-service/api/v1/pay-collect/.*/file$" {
#        rewrite ^/ma-service/api/v1/(.+)$ $1 break;
#        proxy_read_timeout 600;
#        proxy_connect_timeout 600;
#        proxy_send_timeout 600;
#        proxy_pass http://ma_service_paycollect;
#    }
	
	location ~ "^/ma-service/api/v1/report/general/file$" {
        rewrite ^/ma-service/api/v1(.+)$ /ma-report/api/v1$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma_service_general;
   }

	
    location ~ "^/ma-service/api/v1/.*/file$" {
        rewrite ^/ma-service/api/v1(.+)$ $1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma_service2;
    }

    location ~ "^/ma-service/api/v1/file/.*/download$" {
        rewrite ^/ma-service/api/v1(.+)$ $1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma_service2;
        proxy_next_upstream error timeout http_404;
    }

    location ~ "^/ma-service/api/v1/report/general.*$" {
        rewrite ^/ma-service/api/v1(.+)$ /ma-report/api/v1$1 break;
        proxy_pass http://ma_service_general;
    }

    location ~ "^/ma-service/api/v1/mobile-notification.*$" {
        rewrite ^/ma-service/api/v1(.+)$ $1 break;
        proxy_pass http://ma_service5;
    }

    location /ma-service/api/v1 {
        rewrite ^/ma-service/api/v1(.+)$ $1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma_service;
    }

   


    location ~ "^/ma-service/transaction/(international|domestic|mpay)/.*" {
        rewrite ^/ma-service(.+) $1 break;
        proxy_pass http://ma_service3;
    }
	
	location ~ "^/ma-service/api/v1/refund/(international|domestic|mpay)/.*" {
        rewrite ^/ma-service/api/v1(.+) $1 break;
        proxy_pass http://ma_service4;
    }

    location /ma-service/api/v1/bnpl/ {
        rewrite ^/ma-service/api/v1/(.*) /ma-bnpl/api/v1/$1 break;
        proxy_pass http://ma-bnpl-backend;
    }

    location /ma-service/api/v1/upos/ {
        rewrite ^/ma-service/api/v1/(.*) /ma-upos/api/v1/$1 break;
        proxy_pass http://ma-upos-backend;
    }
    location /ma-service/api/v1/domestic/ {
        rewrite ^/ma-service/api/v1/(.*) /ma-domestic/api/v1/$1 break;
        proxy_pass http://ma-domestic-backend;
    }
    location /ma-service/api/v1/international/ {
        rewrite ^/ma-service/api/v1/(.*) /ma-international/api/v1/$1 break;
        proxy_pass http://ma-international-backend;
    }
    location /ma-service/api/v1/onepayout/ {
        rewrite ^/ma-service/api/v1/(.*) /ma-payout/api/v1/$1 break;
        proxy_pass http://ma-payout-backend;
    }
    location /ma-service/api/v1/qr/ {
        rewrite ^/ma-service/api/v1/(.*) /ma-qr/api/v1/$1 break;
        proxy_pass http://ma-qr-backend;
    }
    location /ma-service/api/v1/vietqr/ {
        rewrite ^/ma-service/api/v1/(.*) /ma-vietqr/api/v1/$1 break;
        proxy_pass http://ma-vietqr-backend;
    }

    location /ma-service/api/v1/quicklink {
        rewrite ^/ma-service/api/v1/(.*) /ma-quicklink/api/v1/$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-service-quicklink;
    }

    location /ma-service/api/v1/promotion {
        rewrite ^/ma-service/api/v1/(.*) /ma-pr/api/v1/$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-service-promotion;
    }

    location /ma-service/api/v1/ma-app {
        rewrite ^/ma-service/api/v1/(.*) /ma-app/api/v1/$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-app-backend;
    }

    location /ma-service/api/v1/direct-debit {
        rewrite ^/ma-service/api/v1/(.*) /ma-direct-debit/api/v1/$1 break;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://ma-dd-backend;
    }

    location /ma-service/api/v1/mpay-notification {
        rewrite ^/ma-service/api/v1(/.*)$ /app-notification break;
        proxy_pass http://ma_service5;
    }


    location ~ ^/ufiles/(.*)$ {
        root /opt/ufiles/;
        try_files /ma/$1 /iportal/$1 =404;
    }

    location /statistic-service {
        #rewrite ^/statistic-service/api/v1(.+) $1 break;
        proxy_pass http://iportal_statistic_service;
    }
}

upstream onecomm-napas {
    server ************:80;
    server ************:80 backup;
    keepalive 32;
}

upstream api-15442-15443 {
    server ************:443;
    server ************:443 backup;
    keepalive 32;
}

upstream api-15440-15441 {
    server ************:443;
    server ************:443 backup;
    keepalive 32;
}
	

upstream keycloak {
    server ************:443;
    keepalive 32;
}

server {
    listen 80;
    server_name localhost 127.0.0.1;
    allow 127.0.0.1;
    ###################deny all;
    proxy_http_version 1.1;
    proxy_set_header Connection "";
    proxy_redirect off;

    #location /onecredit/execute {
    #    proxy_next_upstream error;
    #    proxy_pass http://onecredit_service;
    #}

    location /nginx_status {
        stub_status on;
        allow 127.0.0.1;
        ###################deny all;
    }

# mpgs https://ap-gateway.mastercard.com/api/rest/version/48
    location /api/rest/version/ {
        proxy_connect_timeout 10s;
        proxy_send_timeout 15s;
        proxy_read_timeout 20s;
        proxy_next_upstream error timeout non_idempotent;
        proxy_ssl_name ap-gateway.mastercard.com;
        proxy_set_header Connection "";
        proxy_set_header Host ap-gateway.mastercard.com;
        proxy_pass https://mpgs;
    }

    # query cybs
    location /tss/v2/ {
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_redirect off;
        proxy_ssl_name api.cybersource.com;
        proxy_set_header Host api.cybersource.com;
        proxy_pass https://api.cybersource.com;
    }

    location /onecredit/execute {
        proxy_http_version 1.1;
        proxy_redirect off;
        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;

        proxy_ssl_name pci.onepay.local;
        proxy_set_header Host pci.onepay.local;
        proxy_set_header Connection "";
        proxy_next_upstream error http_502 non_idempotent;
        proxy_pass https://pci-16444-16445;
    }

    location /iportal/api/v1/vietqr {
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_pass http://iportal-service-vietqr;
    }

    location /scheduler/api/v1 {

        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
        proxy_set_header Host api.onepay.local;
        proxy_set_header Connection "";
        proxy_pass https://onesched-service;
    }

    location /scheduler/api/v2 {
        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
        proxy_set_header Host api.onepay.local;
        proxy_set_header Connection "";

        proxy_pass https://onesched-service;
    }

    #location /zabbix {
    #   proxy_pass http://127.0.0.1:81;
    #}
    location /onecomm-napas/ipn {
        proxy_set_header Host null.onepay.vn;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_pass http://onecomm-napas;
    }

    location /onecomm-payservice/execute {
        proxy_pass http://onecomm_service;
    }

    location /oauth2/ {
        proxy_pass http://oauth2_service;
    }    
	# keycloak
    location /auth/realms {
        proxy_set_header Host api.onepay.local;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
		
        proxy_http_version 1.1;
        proxy_pass https://keycloak;
    }

    location /accounts/api/v1 {
        rewrite ^/accounts/api/v1(.*) $1 break;
        proxy_set_header Host null-secure.onepay.vn;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_pass http://oauth2_service_user;
    }

    #location /onesm/ {
    #    rewrite ^/onesm/api/v1(.*) $1 break;
    #    proxy_pass http://onesm_service;
    #}
    location ~ ^(/onesm/api/v1) {
        proxy_http_version 1.1;
        proxy_redirect off;
        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;

        proxy_ssl_name pci.onepay.local;
        proxy_set_header Host pci.onepay.local;
        proxy_set_header Connection "";
        proxy_next_upstream error timeout http_502 non_idempotent;
        proxy_pass https://pci-16444-16445;
    }

    location /ma-eventbus/ {
        rewrite ^/ma-eventbus/(.*)$ /$1 break;
        proxy_pass http://ma_eventbus;
    }

    #location /msp/api/v1 {
    #    proxy_pass http://msp;
    #}
    location /msp/api/v1/ {
        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name pci.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
        proxy_set_header Host pci.onepay.local;
        proxy_set_header Connection "";
        proxy_next_upstream error http_502 non_idempotent;
        proxy_pass https://pci-16444-16445;
    }

    location /msp-apple/api/v1 {
        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name pci.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
        proxy_set_header Host pci.onepay.local;
        proxy_set_header Connection "";
        proxy_next_upstream error http_502 non_idempotent;
        proxy_pass https://pci-16444-16445;
    }
	

    location /msp-pci-apple/api/v1 {
        rewrite ^/msp-pci-apple/api/v1(.*)$ /msp-apple/api/v1$1 break;
        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name pci.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
        proxy_set_header Host pci.onepay.local;
        proxy_set_header Connection "";
        proxy_pass https://pci-16444-16445;
    }
    
    location /quicklink-backend/quicklink2 {
        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name pci.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
        proxy_set_header Host pci.onepay.local;
        proxy_set_header Connection "";
        proxy_next_upstream error http_502 non_idempotent;
        proxy_pass https://71100-71101-7110-ssl;
    }

    location /onecomm-bankservice/onecomm-napas/ {
       proxy_pass http://onecomm_bank;
    }

    location /sms/api/v1 {
        proxy_pass http://smssp;
    }

    location /paygate/api/v1/vpc/merchants/ {
        proxy_pass http://wsp;
    }

    location /portal-service/api/v1 {
        proxy_pass http://portal_service;
    }

    location /ipn/api/v1/ {
        proxy_pass http://ipn;
    }


    location /ipn/api/v2/messages {
        rewrite ^/ipn/api/v2/(.+) /ipn/api/v1/$1 break;
        proxy_pass http://ipn2;
    }

    location /ma-service/api/v1 {
        #rewrite ^/ma-service/api/v1(.+) $1 break;
        proxy_pass http://ma_service_local;
    } 
    location ~ "^/ma-service/api/v1/mobile-notification.*$" {
        rewrite ^/ma-service/api/v1(.+)$ $1 break;
        proxy_pass http://ma_service5;
    }

    location /quicklink-backend/quicklink {
        proxy_pass http://quicklink_service;
    }

    location /prod/quicklink-backend/quicklink {
        rewrite ^/prod(.+) $1 break;
        proxy_pass http://quicklink_service_prod;
    }

    location /dispute-backend/dispute {
        rewrite ^/prod(.+) $1 break;
        proxy_pass http://dispute_service;
    }

    location /merchant-account-backend/merchant-account {
        proxy_pass http://merchant_account_service;
    }

    location /portal/ {
        proxy_pass http://portal_web;
    }

    location /portal/img {
        rewrite ^/portal/img(.*) $1 break;
        root /opt/onesched/classes/template;
    }

    location /paycollect/api/v1 {
        proxy_pass http://paycollect;
    }
    location /onepayout/api/v1 {
        proxy_pass http://onepayout-service;
    }

    location /onepayout/api/v2 {
        proxy_pass http://onepayout-service;
    }

    location /advance/api/v1 {
	proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
        proxy_set_header Host api.onepay.local;
        proxy_set_header Connection "";
        proxy_next_upstream error http_502 non_idempotent;
        proxy_pass https://advance-service;
        #proxy_pass http://advance-service;
    }
    location /payment2/detail {
        root /opt/iportal-service/classes/templates;
    }
    location ~ ^/ldp-backend/(.*)/api/v1/payments$ {
        proxy_pass http://landing-page;
    }

    location /ldp/api/v1 {
        proxy_pass http://landing-page;
    }
    
    location /msp-extend/api/v1/ {
        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
        proxy_set_header Host api.onepay.local;
        proxy_set_header Connection "";
        proxy_next_upstream error http_502 non_idempotent;
        #proxy_pass https://api-15442-15443;
        #proxy_pass https://api-15440-15441;
        proxy_pass https://api-16444-16445;
    }   

    location /esp-connector-sacombank/api/v1 {
        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name api.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
        proxy_set_header Host api.onepay.local;
        proxy_set_header Connection "";
        proxy_next_upstream error http_502 non_idempotent;
        proxy_pass https://api-15440-15441;
    }

    #Card Helper
    location /card-helper/api/v1/recharge {
        proxy_ssl_certificate /etc/nginx/conf.d/api.onepay.local.cer;
        proxy_ssl_certificate_key /etc/nginx/conf.d/api.onepay.local.key;
        proxy_ssl_trusted_certificate /etc/nginx/conf.d/onepayrootca.cer;
        proxy_ssl_name pci.onepay.local;
        proxy_ssl_verify on;
        proxy_ssl_server_name on;
        proxy_set_header Host pci.onepay.local;
        proxy_set_header Connection "";
        proxy_set_header Authorization "Basic aXBvcnRhbC13ZWI6NGozNGopIyUz";
        proxy_next_upstream error http_502 non_idempotent;
        proxy_pass https://pci-16444-16445;
    } 
}

server {
    listen 80;
    server_name archive.ubuntu.com nginx.org;
    proxy_set_header Host $host;
    proxy_http_version 1.1;
    proxy_set_header Connection "";
    #resolver        *******;

    #location ~ .*/repodata/repomd.xml$ {
    #    proxy_pass      https://$host;
    #}

    location / {
        proxy_pass http://***********:80;
        #include         /etc/nginx/proxy.conf;
    }

    location /portal/img {
        rewrite ^/portal/img(.*) $1 break;
        root /opt/advance-service/classes/template;
    }

}

package com.onepay.keycloak.email;

import org.keycloak.Config;
import org.keycloak.email.EmailTemplateProvider;
import org.keycloak.email.EmailTemplateProviderFactory;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.KeycloakSessionFactory;

public class MaEmailTemplateProviderFactory implements EmailTemplateProviderFactory {
    
    public static final String PROVIDER_ID = "ma-email-template";
    
    @Override
    public EmailTemplateProvider create(KeycloakSession session) {
        return new MaEmailTemplateProvider(session, null, null);
    }
    
    @Override
    public void init(Config.Scope config) {
        // No initialization needed
    }
    
    @Override
    public void postInit(KeycloakSessionFactory factory) {
        // No post-init needed
    }
    
    @Override
    public void close() {
        // No cleanup needed
    }
    
    @Override
    public String getId() {
        return PROVIDER_ID;
    }
}

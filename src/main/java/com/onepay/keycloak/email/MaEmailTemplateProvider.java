package com.onepay.keycloak.email;

import org.keycloak.email.EmailException;
import org.keycloak.email.EmailTemplateProvider;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.RealmModel;
import org.keycloak.models.UserModel;
import org.keycloak.theme.Theme;
import org.keycloak.theme.ThemeProvider;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

/**
 * Custom Email Template Provider for MA Portal
 * Overrides default Keycloak email templates
 */
public class MaEmailTemplateProvider implements EmailTemplateProvider {
    
    private static final Logger LOG = Logger.getLogger(MaEmailTemplateProvider.class.getName());
    
    private final KeycloakSession session;
    private final RealmModel realm;
    private final UserModel user;
    private final Map<String, Object> attributes = new HashMap<>();
    
    public MaEmailTemplateProvider(KeycloakSession session, RealmModel realm, UserModel user) {
        this.session = session;
        this.realm = realm;
        this.user = user;
    }
    
    @Override
    public EmailTemplateProvider setRealm(RealmModel realm) {
        return new MaEmailTemplateProvider(session, realm, user);
    }
    
    @Override
    public EmailTemplateProvider setUser(UserModel user) {
        return new MaEmailTemplateProvider(session, realm, user);
    }
    
    @Override
    public EmailTemplateProvider setAttribute(String name, Object value) {
        attributes.put(name, value);
        return this;
    }
    
    @Override
    public void sendExecuteActions(String link, long validityInSecs) throws EmailException {
        LOG.info("=== MA CUSTOM EMAIL TEMPLATE PROVIDER ===");
        LOG.info("Sending execute actions email with custom template");
        LOG.info("Link: " + link);
        LOG.info("Validity: " + validityInSecs + " seconds");
        LOG.info("User: " + (user != null ? user.getEmail() : "null"));
        LOG.info("Realm: " + (realm != null ? realm.getName() : "null"));
        
        try {
            // Use Keycloak's built-in email template provider instead of custom implementation
            EmailTemplateProvider defaultProvider = session.getProvider(EmailTemplateProvider.class);
            
            LOG.info("Using Keycloak's built-in email template provider");
            LOG.info("Default provider class: " + defaultProvider.getClass().getName());
            
            // Set up the default provider with our context
            defaultProvider.setRealm(realm)
                .setUser(user)
                .setAttribute("realmName", realm.getDisplayName() != null ? realm.getDisplayName() : realm.getName());
            
            // Add custom attributes
            for (Map.Entry<String, Object> entry : attributes.entrySet()) {
                defaultProvider.setAttribute(entry.getKey(), entry.getValue());
            }
            
            LOG.info("Calling default provider sendExecuteActions()");
            defaultProvider.sendExecuteActions(link, validityInSecs);
            
            LOG.info("Custom email template processing completed successfully");
            
        } catch (Exception e) {
            LOG.severe("Error in custom email template: " + e.getMessage());
            e.printStackTrace();
            throw new EmailException("Failed to send email with custom template", e);
        }
    }
    
    @Override
    public void sendPasswordReset(String link, long expirationInSeconds) throws EmailException {
        LOG.info("=== MA CUSTOM PASSWORD RESET EMAIL ===");
        LOG.info("Sending password reset email with custom template");
        LOG.info("Link: " + link);
        LOG.info("Expiration: " + expirationInSeconds + " seconds");
        
        // Convert to validity in seconds
        long validityInSecs = expirationInSeconds;
        sendExecuteActions(link, validityInSecs);
    }
    
    @Override
    public void sendVerifyEmail(String link, long expirationInSeconds) throws EmailException {
        LOG.info("=== MA CUSTOM VERIFY EMAIL ===");
        LOG.info("Sending verify email with custom template");
        sendPasswordReset(link, expirationInSeconds);
    }
    
    @Override
    public void sendEmailUpdateConfirmation(String link, long expirationInSeconds, String newEmail) throws EmailException {
        LOG.info("=== MA CUSTOM EMAIL UPDATE CONFIRMATION ===");
        LOG.info("Sending email update confirmation with custom template");
        sendPasswordReset(link, expirationInSeconds);
    }
    
    @Override
    public void sendSmtpTestEmail(Map<String, String> config, UserModel user) throws EmailException {
        LOG.info("=== MA CUSTOM SMTP TEST EMAIL ===");
        LOG.info("Sending SMTP test email with custom template");
        // Implementation for SMTP test email
    }
    
    @Override
    public void sendEvent(org.keycloak.events.Event event) throws EmailException {
        LOG.info("=== MA CUSTOM SEND EVENT ===");
        LOG.info("Sending event email with custom template");
    }
    
    @Override
    public void send(String subject, String body, Map<String, Object> bodyAttributes) throws EmailException {
        LOG.info("=== MA CUSTOM SEND EMAIL ===");
        LOG.info("Sending email with custom template");
        LOG.info("Subject: " + subject);
    }
    
    @Override
    public void send(String subject, List<Object> subjectAttributes, String body, Map<String, Object> bodyAttributes) throws EmailException {
        LOG.info("=== MA CUSTOM SEND EMAIL WITH SUBJECT ATTRIBUTES ===");
        LOG.info("Sending email with custom template");
        LOG.info("Subject: " + subject);
    }
    
    @Override
    public void sendOrgInviteEmail(org.keycloak.models.OrganizationModel organization, String link, long expirationInSeconds) throws EmailException {
        LOG.info("=== MA CUSTOM ORG INVITE EMAIL ===");
        LOG.info("Sending organization invite email with custom template");
    }
    
    @Override
    public EmailTemplateProvider setAuthenticationSession(org.keycloak.sessions.AuthenticationSessionModel authenticationSession) {
        return this;
    }
    
    @Override
    public void sendConfirmIdentityBrokerLink(String link, long expirationInSeconds) throws EmailException {
        LOG.info("=== MA CUSTOM IDENTITY BROKER CONFIRM EMAIL ===");
        LOG.info("Sending identity broker confirm email with custom template");
    }
    
    @Override
    public void close() {
        // Cleanup if needed
    }
    
    /**
     * Custom formatter for link expiration
     */
    public static class LinkExpirationFormatter {
        public String format(int seconds) {
            if (seconds < 60) {
                return seconds + " giây";
            } else if (seconds < 3600) {
                int minutes = seconds / 60;
                return minutes + " phút";
            } else {
                int hours = seconds / 3600;
                return hours + " giờ";
            }
        }
    }
}

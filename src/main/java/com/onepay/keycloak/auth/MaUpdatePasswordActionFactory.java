package com.onepay.keycloak.auth;

import org.keycloak.Config;
import org.keycloak.authentication.RequiredActionFactory;
import org.keycloak.authentication.RequiredActionProvider;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.KeycloakSessionFactory;
import java.util.logging.Logger;

public class MaUpdatePasswordActionFactory implements RequiredActionFactory {

    private static final Logger LOG = Logger.getLogger(MaUpdatePasswordActionFactory.class.getName());
    public static final String PROVIDER_ID = "MA_UPDATE_PASSWORD";

    @Override
    public RequiredActionProvider create(KeycloakSession session) {
        LOG.info("=== [MA] MaUpdatePasswordActionFactory.create() called ===");
        return new MaUpdatePasswordAction();
    }

    @Override
    public void init(Config.Scope config) {
        LOG.info("=== [MA] Initializing MaUpdatePasswordActionFactory ===");
    }

    @Override
    public void postInit(KeycloakSessionFactory factory) {
        LOG.info("=== [MA] PostInit MaUpdatePasswordActionFactory ===");
    }

    @Override
    public void close() {}

    @Override
    public String getId() {
        LOG.info("=== [MA] MaUpdatePasswordActionFactory.getId() -> " + PROVIDER_ID + " ===");
        return PROVIDER_ID;
    }

    @Override
    public String getDisplayText() {
        return "MA Update Reset Password Form";
    }

    public MaUpdatePasswordActionFactory() {
        LOG.info("=== [MA] MaUpdatePasswordActionFactory Constructor ===");
    }

}

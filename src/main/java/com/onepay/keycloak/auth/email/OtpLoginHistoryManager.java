package com.onepay.keycloak.auth.email;

import com.onepay.keycloak.auth.config.AuthConfigManager;
import com.onepay.keycloak.auth.util.ClientIpUtil;
import org.keycloak.authentication.AuthenticationFlowContext;
import org.keycloak.models.UserModel;

import java.sql.*;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Logger;
import jakarta.ws.rs.core.Cookie;
import ua_parser.Parser;
import ua_parser.Client;

/**
 * Manager for OTP login history table
 * <PERSON><PERSON><PERSON> thông tin đăng nhập OTP để skip OTP cho lần sau
 */
public class OtpLoginHistoryManager {

    private static final Logger log = Logger.getLogger(OtpLoginHistoryManager.class.getName());

    // Database configuration - now loaded from AuthConfigManager
    // Removed hardcoded values for better maintainability

    /**
     * Check if user can skip OTP based on login history with relaxed logic
     * Logic nới lỏng: Nếu browser/OS version khác nhưng cùng browser/OS thì vẫn
     * skip OTP
     */
    public static boolean canSkipOtp(AuthenticationFlowContext context, UserModel user) {
        log.info("=== OTP SKIP CHECK START ===");
        log.info("User: " + user.getUsername() + " (ID: " + user.getId() + ")");

        // Get cookie value from request (user-specific cookie)
        String cookieValue = getCookieValue(context, user);
        if (cookieValue == null) {
            log.info("No cookie found for user: " + user.getUsername() + " - SKIP OTP = FALSE");
            return false;
        }

        log.info("Cookie found for user: " + user.getUsername());

        // Parse User-Agent để lấy thông tin browser/OS
        String userAgent = context.getHttpRequest().getHttpHeaders().getHeaderString("User-Agent");
        BrowserInfo browserInfo = parseUserAgent(userAgent);

        // BƯỚC 1 & 2: Kiểm tra cookie chính xác hoặc browser/OS tương tự (gộp chung)
        OtpLoginRecord loginRecord = getOtpLoginRecord(user.getId(), cookieValue, browserInfo.browser, browserInfo.os);
        if (loginRecord != null) {
            return processOtpLoginResult(context, user, cookieValue, browserInfo, loginRecord);
        }

        return false;
    }

    /**
     * Record successful OTP login with cookie limit management
     * Giới hạn tối đa 10 cookies per user, deactivate oldest records when limit
     * exceeded
     */
    public static void recordOtpLogin(AuthenticationFlowContext context, UserModel user) {
        try (Connection conn = getConnection()) {
            // Get existing cookie value from request
            String encodedCookie = getCookieValue(context, user);
            if (encodedCookie == null) {
                log.warning("No cookie found when recording OTP login for user: " + user.getUsername());
                return;
            }

            String deviceFingerprint = CookieDeviceManager.generateDeviceFingerprint(context);

            // Parse User-Agent để lấy thông tin browser/OS
            String userAgent = context.getHttpRequest().getHttpHeaders().getHeaderString("User-Agent");
            BrowserInfo browserInfo = parseUserAgent(userAgent);
            String ipAddress = ClientIpUtil.getRealClientIp(context);

            // BƯỚC 1: Kiểm tra và deactivate records cũ nếu vượt quá 10 cookies
            enforceCookieLimit(conn, user.getId());

            // BƯỚC 2: Kiểm tra trạng thái record hiện tại
            RecordStatus status = checkRecordStatus(user.getId(), encodedCookie);
            log.info("Record status for user " + user.getUsername() + ": " + status);

            int rowsAffected = 0;

            // BƯỚC 3: Xử lý theo trạng thái
            switch (status) {
                case ACTIVE:
                    // Record đang active -> Update bình thường
                    rowsAffected = updateActiveRecord(conn, user.getId(), encodedCookie, 
                            browserInfo, userAgent, ipAddress, deviceFingerprint);
                    if (rowsAffected > 0) {
                        log.info("OTP login record updated for user: " + user.getUsername());
                    }
                    break;

                case DEACTIVATED:
                    // Record bị deactivate -> Renew cookie trước, rồi tạo record mới với cookie mới
                    CookieDeviceManager.clearTrustedDeviceCookie(context, user);
                    String newCookie = CookieDeviceManager.setTrustedDeviceCookie(context, user);
                    log.info("Cookie renewed for reactivated device: " + user.getUsername());
                    // Lấy cookie mới sau khi renew
                    if (newCookie != null) {
                        rowsAffected = insertNewRecord(conn, user.getId(), newCookie, 
                                browserInfo, userAgent, ipAddress, deviceFingerprint);
                        if (rowsAffected > 0) {
                            log.info("OTP login record created for user: " + user.getUsername() + 
                                    " - Device was previously deactivated, created new record with fresh cookie");
                        }
                    } else {
                        log.warning("Failed to get new cookie after renewal for user: " + user.getUsername());
                    }
                    break;

                case NOT_FOUND:
                    // Chưa có record -> Insert mới
                    rowsAffected = insertNewRecord(conn, user.getId(), encodedCookie, 
                            browserInfo, userAgent, ipAddress, deviceFingerprint);
                    if (rowsAffected > 0) {
                        log.info("OTP login record inserted for user: " + user.getUsername());
                    }
                    break;
            }

            if (rowsAffected == 0) {
                log.warning("No database changes made for user: " + user.getUsername());
            }

        } catch (SQLException e) {
            log.warning("Error recording OTP login for user: " + user.getUsername() + " - " + e.getMessage());
        }
    }



    // ================================================================
    // DATA CLASSES
    // ================================================================

    /**
     * Record status enum
     */
    private enum RecordStatus {
        ACTIVE,      // Record exists and is active
        DEACTIVATED, // Record exists but is deactivated
        NOT_FOUND    // No record found
    }

    /**
     * Browser info class
     */
    private static class BrowserInfo {
        String browser = "Unknown";
        String browserVersion = "Unknown";
        String os = "Unknown";
        String osVersion = "Unknown";
        String device = "Unknown";
    }

    /**
     * OTP login record data class - gộp cả exact cookie và similar browser
     */
    private static class OtpLoginRecord {
        Timestamp lastLogin;
        String oldCookie;
        String oldBrowserVersion;
        String oldOsVersion;
        boolean isExactCookieMatch;
    }

    /**
     * Parse User-Agent string to extract browser/OS info using UAParser
     * Provides comprehensive browser/OS detection with 98-99% coverage
     */
    private static BrowserInfo parseUserAgent(String userAgent) {
        BrowserInfo info = new BrowserInfo();

        if (userAgent == null || userAgent.trim().isEmpty()) {
            info.browser = "Unknown";
            info.os = "Unknown";
            info.device = "Unknown";
            return info;
        }

        try {
            Parser parser = new Parser();
            Client client = parser.parse(userAgent);
            
            // Parse Browser
            info.browser = client.userAgent.family != null ? client.userAgent.family : "Unknown";
            if (client.userAgent.major != null) {
                info.browserVersion = client.userAgent.major;
                if (client.userAgent.minor != null) {
                    info.browserVersion += "." + client.userAgent.minor;
                }
                if (client.userAgent.patch != null) {
                    info.browserVersion += "." + client.userAgent.patch;
                }
            } else {
                info.browserVersion = "Unknown";
            }
            
            // Parse OS
            info.os = client.os.family != null ? client.os.family : "Unknown";
            if (client.os.major != null) {
                info.osVersion = client.os.major;
                if (client.os.minor != null) {
                    info.osVersion += "." + client.os.minor;
                }
                if (client.os.patch != null) {
                    info.osVersion += "." + client.os.patch;
                }
            } else {
                info.osVersion = "Unknown";
            }
            
            // Parse Device - Map to Mobile/Tablet/Desktop
            if (client.device.family != null) {
                String deviceFamily = client.device.family.toLowerCase();
                if (deviceFamily.contains("mobile") || deviceFamily.contains("phone") || 
                    deviceFamily.contains("android") || deviceFamily.contains("iphone")) {
                    info.device = "Mobile";
                } else if (deviceFamily.contains("tablet") || deviceFamily.contains("ipad")) {
                    info.device = "Tablet";
                } else {
                    info.device = "Desktop";
                }
            } else {
                // Fallback: determine device type from OS
                String osFamily = info.os.toLowerCase();
                if (osFamily.contains("android") || osFamily.contains("ios")) {
                    info.device = "Mobile";
                } else {
                    info.device = "Desktop";
                }
            }
            
            log.info("UAParser detected - Browser: " + info.browser + " " + info.browserVersion + 
                    ", OS: " + info.os + " " + info.osVersion + ", Device: " + info.device);
            
        } catch (Exception e) {
            log.warning("Error parsing User-Agent with UAParser: " + e.getMessage() + 
                       " - User-Agent: " + userAgent);
            // Fallback to basic detection
            info.browser = "Unknown";
            info.os = "Unknown";
            info.device = "Unknown";
        }

        return info;
    }

    /**
     * Extract version number from User-Agent string
     */
    private static String extractVersion(String userAgent, String prefix) {
        int start = userAgent.indexOf(prefix);
        if (start == -1)
            return "Unknown";

        start += prefix.length();
        int end = start;
        while (end < userAgent.length() && (Character.isDigit(userAgent.charAt(end)) || userAgent.charAt(end) == '.')) {
            end++;
        }

        return userAgent.substring(start, end);
    }

    /**
     * Extract Android version from User-Agent string
     * Handles various Android version formats like "Android 13", "Android 12.1", etc.
     */
    private static String extractAndroidVersion(String userAgent) {
        // Look for "android " followed by version number
        int start = userAgent.indexOf("android ");
        if (start == -1)
            return "Unknown";

        start += "android ".length();
        int end = start;
        
        // Extract version number (digits and dots)
        while (end < userAgent.length() && (Character.isDigit(userAgent.charAt(end)) || userAgent.charAt(end) == '.')) {
            end++;
        }

        String version = userAgent.substring(start, end);
        return version.isEmpty() ? "Unknown" : version;
    }

    /**
     * So sánh version mới có lớn hơn hoặc bằng version cũ không
     * Ví dụ: "139.0.0.2" >= "139.0.0.1" → true
     * "139.0.0.1" >= "139.0.0.2" → false
     */
    private static boolean isVersionNewerOrEqual(String newVersion, String oldVersion) {
        if (newVersion == null || oldVersion == null) {
            return false;
        }

        if ("Unknown".equals(newVersion) || "Unknown".equals(oldVersion)) {
            return false;
        }

        try {
            // Chuyển version thành array số
            String[] newParts = newVersion.split("\\.");
            String[] oldParts = oldVersion.split("\\.");

            // So sánh từng phần
            int maxLength = Math.max(newParts.length, oldParts.length);
            for (int i = 0; i < maxLength; i++) {
                int newPart = i < newParts.length ? Integer.parseInt(newParts[i]) : 0;
                int oldPart = i < oldParts.length ? Integer.parseInt(oldParts[i]) : 0;

                if (newPart > oldPart) {
                    return true; // Version mới lớn hơn
                } else if (newPart < oldPart) {
                    return false; // Version mới nhỏ hơn
                }
                // Nếu bằng nhau, tiếp tục so sánh phần tiếp theo
            }

            return true; // Tất cả phần đều bằng nhau
        } catch (NumberFormatException e) {
            log.warning("Error comparing versions: " + newVersion + " vs " + oldVersion + " - " + e.getMessage());
            return false;
        }
    }


    /**
     * Get cookie value from request (user-specific cookie)
     */
    private static String getCookieValue(AuthenticationFlowContext context, UserModel user) {
        try {
            String themeCookieName = AuthConfigManager.getCookieName(context);
            String userCookieName = themeCookieName + "_" + user.getUsername();
            log.info("Theme: " + AuthConfigManager.detectTheme(context) + ", Looking for cookie with name: " + userCookieName);

            Cookie deviceCookie = context.getHttpRequest().getHttpHeaders().getCookies()
                    .get(userCookieName);
            if (deviceCookie != null) {
                log.info("Found cookie: " + userCookieName + " = " + deviceCookie.getValue());
                return deviceCookie.getValue();
            } else {
                log.info("Cookie not found: " + userCookieName);
                // Log all available cookies for debugging
                Map<String, Cookie> allCookies = context.getHttpRequest().getHttpHeaders().getCookies();
                log.info("Available cookies: " + allCookies.keySet());
            }
        } catch (Exception e) {
            log.warning("Error getting cookie value: " + e.getMessage());
        }
        return null;
    }

    /**
     * Get database connection using AuthConfigManager
     */
    private static Connection getConnection() throws SQLException {
        String dbUrl = AuthConfigManager.getDatabaseUrl();
        String dbUser = AuthConfigManager.getDatabaseUser();
        String dbPassword = AuthConfigManager.getDatabasePassword();
        
        log.info("Connecting to database: " + dbUrl + " with user: " + dbUser);
        return DriverManager.getConnection(dbUrl, dbUser, dbPassword);
    }

    // ================================================================
    // QUERY METHODS - Chỉ làm nhiệm vụ query database
    // ================================================================

    /**
     * Check current status of OTP login record for given user and cookie
     * @param userId User ID
     * @param cookieValue Cookie value
     * @return RecordStatus indicating current state
     */
    private static RecordStatus checkRecordStatus(String userId, String cookieValue) {
        try (Connection conn = getConnection()) {
            String sql = "SELECT b_is_active FROM tb_otp_login_history " +
                    "WHERE s_user_id = ? AND s_cookie = ? " +
                    "ORDER BY t_last_login_activity DESC LIMIT 1";

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, userId);
                stmt.setString(2, cookieValue);

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        boolean isActive = rs.getBoolean("b_is_active");
                        return isActive ? RecordStatus.ACTIVE : RecordStatus.DEACTIVATED;
                    }
                }
            }
        } catch (SQLException e) {
            log.warning("Error checking record status for user: " + userId + " - " + e.getMessage());
        }
        return RecordStatus.NOT_FOUND;
    }

    /**
     * Update existing active record
     * @param conn Database connection
     * @param userId User ID
     * @param cookieValue Cookie value
     * @param browserInfo Browser information
     * @param userAgent User agent string
     * @param ipAddress IP address
     * @param deviceFingerprint Device fingerprint
     * @return Number of rows updated
     */
    private static int updateActiveRecord(Connection conn, String userId, String cookieValue, 
            BrowserInfo browserInfo, String userAgent, String ipAddress, String deviceFingerprint) {
        try {
            String sql = "UPDATE tb_otp_login_history SET " +
                    "s_browser = ?, s_browser_version = ?, s_os = ?, s_os_version = ?, " +
                    "s_device = ?, s_user_agent = ?, s_ip_address = ?, s_device_fingerprint = ?, " +
                    "t_last_login_activity = CURRENT_TIMESTAMP, t_updated_at = CURRENT_TIMESTAMP " +
                    "WHERE s_user_id = ? AND s_cookie = ? AND b_is_active = TRUE";

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, browserInfo.browser);
                stmt.setString(2, browserInfo.browserVersion);
                stmt.setString(3, browserInfo.os);
                stmt.setString(4, browserInfo.osVersion);
                stmt.setString(5, browserInfo.device);
                stmt.setString(6, userAgent);
                stmt.setString(7, ipAddress);
                stmt.setString(8, deviceFingerprint);
                stmt.setString(9, userId);
                stmt.setString(10, cookieValue);

                return stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.warning("Error updating active record for user: " + userId + " - " + e.getMessage());
            return 0;
        }
    }


    /**
     * Insert new record
     * @param conn Database connection
     * @param userId User ID
     * @param cookieValue Cookie value
     * @param browserInfo Browser information
     * @param userAgent User agent string
     * @param ipAddress IP address
     * @param deviceFingerprint Device fingerprint
     * @return Number of rows inserted
     */
    private static int insertNewRecord(Connection conn, String userId, String cookieValue, 
            BrowserInfo browserInfo, String userAgent, String ipAddress, String deviceFingerprint) {
        try {
            String recordId = UUID.randomUUID().toString();
            String sql = "INSERT INTO tb_otp_login_history (" +
                    "s_id, s_user_id, s_cookie, s_browser, s_browser_version, s_os, s_os_version, " +
                    "s_device, s_user_agent, s_ip_address, s_device_fingerprint, t_last_login_activity, b_is_active" +
                    ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, TRUE)";

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, recordId);
                stmt.setString(2, userId);
                stmt.setString(3, cookieValue);
                stmt.setString(4, browserInfo.browser);
                stmt.setString(5, browserInfo.browserVersion);
                stmt.setString(6, browserInfo.os);
                stmt.setString(7, browserInfo.osVersion);
                stmt.setString(8, browserInfo.device);
                stmt.setString(9, userAgent);
                stmt.setString(10, ipAddress);
                stmt.setString(11, deviceFingerprint);

                return stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.warning("Error inserting new record for user: " + userId + " - " + e.getMessage());
            return 0;
        }
    }

    /**
     * Query OTP login record - gộp cả exact cookie và similar browser/OS
     * Ưu tiên exact cookie match trước, nếu không có thì tìm similar browser/OS
     */
    private static OtpLoginRecord getOtpLoginRecord(String userId, String cookieValue, String browser, String os) {
        try (Connection conn = getConnection()) {
            String sql = "SELECT t_last_login_activity, s_cookie, s_browser_version, s_os_version " +
                    "FROM tb_otp_login_history " +
                    "WHERE s_user_id = ? AND b_is_active = TRUE AND s_cookie = ? " +
                    "ORDER BY t_last_login_activity DESC " +
                    "LIMIT 1";

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, userId);
                stmt.setString(2, cookieValue);

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        OtpLoginRecord record = new OtpLoginRecord();
                        record.lastLogin = rs.getTimestamp("t_last_login_activity");
                        record.oldCookie = rs.getString("s_cookie");
                        record.oldBrowserVersion = rs.getString("s_browser_version");
                        record.oldOsVersion = rs.getString("s_os_version");
                        record.isExactCookieMatch = true; // vì query là exact cookie
                        return record;
                    }
                }
            }
        } catch (SQLException e) {
            log.warning("Error querying OTP login record for user: " + userId + " - " + e.getMessage());
        }
        return null;
    }


    /**
     * Enforce cookie limit per user (configurable max active cookies)
     * Deactivate oldest records when limit exceeded
     */
    private static void enforceCookieLimit(Connection conn, String userId) {
        try {
            // Lấy config từ AuthConfigManager
            int maxCookiesPerUser = AuthConfigManager.getCookieMaxPerUser();

            // Kiểm tra số lượng active cookies hiện tại
            String countSql = "SELECT COUNT(*) FROM tb_otp_login_history " +
                    "WHERE s_user_id = ? AND b_is_active = TRUE";

            int activeCount = 0;
            try (PreparedStatement countStmt = conn.prepareStatement(countSql)) {
                countStmt.setString(1, userId);
                try (ResultSet rs = countStmt.executeQuery()) {
                    if (rs.next()) {
                        activeCount = rs.getInt(1);
                    }
                }
            }

            // Nếu đã đạt giới hạn, deactivate oldest ones
            if (activeCount >= maxCookiesPerUser) {
                int recordsToDeactivate = activeCount - (maxCookiesPerUser - 1); // Giữ lại (max-1), deactivate oldest

                String deactivateSql = "UPDATE tb_otp_login_history SET b_is_active = FALSE, t_updated_at = CURRENT_TIMESTAMP " +
                        "WHERE s_id IN (" +
                        "SELECT s_id FROM tb_otp_login_history " +
                        "WHERE s_user_id = ? AND b_is_active = TRUE " +
                        "ORDER BY t_last_login_activity ASC " +
                        "LIMIT ?" +
                        ")";

                try (PreparedStatement deactivateStmt = conn.prepareStatement(deactivateSql)) {
                    deactivateStmt.setString(1, userId);
                    deactivateStmt.setInt(2, recordsToDeactivate);

                    int deactivatedCount = deactivateStmt.executeUpdate();
                    log.info("Deactivated " + deactivatedCount + " oldest cookie records for user: " + userId +
                            " (limit: " + maxCookiesPerUser + " active cookies)");
                }
            }
        } catch (SQLException e) {
            log.warning("Error enforcing cookie limit for user: " + userId + " - " + e.getMessage());
        }
    }

    /**
     * Update cookie on version change
     */
    private static int updateCookieOnVersionChange(String userId, String oldCookie, String newCookie,
            String browser, String os, String browserVersion, String osVersion,
            String userAgent, String ipAddress, String deviceFingerprint) {
        try (Connection conn = getConnection()) {
            String sql = "UPDATE tb_otp_login_history SET " +
                    "s_cookie = ?, s_browser_version = ?, s_os_version = ?, " +
                    "s_user_agent = ?, s_ip_address = ?, s_device_fingerprint = ?, " +
                    "t_last_login_activity = CURRENT_TIMESTAMP, t_updated_at = CURRENT_TIMESTAMP " +
                    "WHERE s_user_id = ? AND s_cookie = ? AND s_browser = ? AND s_os = ? AND b_is_active = TRUE";

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, newCookie);
                stmt.setString(2, browserVersion);
                stmt.setString(3, osVersion);
                stmt.setString(4, userAgent);
                stmt.setString(5, ipAddress);
                stmt.setString(6, deviceFingerprint);
                stmt.setString(7, userId);
                stmt.setString(8, oldCookie);
                stmt.setString(9, browser);
                stmt.setString(10, os);

                return stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.warning("Error updating cookie on version change for user: " + userId + " - " + e.getMessage());
        }
        return 0;
    }

    // ================================================================
    // LOGIC METHODS - Xử lý logic business
    // ================================================================

    /**
     * Process OTP login result - gộp logic của exact cookie và similar browser
     */
    private static boolean processOtpLoginResult(AuthenticationFlowContext context, UserModel user,
            String cookieValue, BrowserInfo browserInfo, OtpLoginRecord record) {
        long timeDiff = System.currentTimeMillis() - record.lastLogin.getTime();
        long maxAge = AuthConfigManager.getDeviceTrackingMaxAgeMillis(); // 90 ngày

        if (timeDiff < maxAge) {
            if (record.isExactCookieMatch) {
                // BƯỚC 1: Exact cookie match - skip OTP ngay lập tức
                log.info("User " + user.getUsername() + " can skip OTP - exact cookie match, last login: " +
                        (timeDiff / 1000 / 60 / 60) + " hours ago");
                return true;
            } else {
                // BƯỚC 2: Similar browser/OS - cần check version upgrade
                boolean browserVersionOk = isVersionNewerOrEqual(browserInfo.browserVersion, record.oldBrowserVersion);
                boolean osVersionOk = isVersionNewerOrEqual(browserInfo.osVersion, record.oldOsVersion);

                if (browserVersionOk && osVersionOk) {
                    log.info("User " + user.getUsername() + " can skip OTP - version upgrade detected, " +
                            "browser: " + browserInfo.browser + " " + record.oldBrowserVersion + " → "
                            + browserInfo.browserVersion +
                            ", OS: " + browserInfo.os + " " + record.oldOsVersion + " → " + browserInfo.osVersion +
                            ", last login: " + (timeDiff / 1000 / 60 / 60) + " hours ago");

                    // Cập nhật cookie mới cho browser/OS version mới
                    String deviceFingerprint = CookieDeviceManager.generateDeviceFingerprint(context);
                    String userAgent = context.getHttpRequest().getHttpHeaders().getHeaderString("User-Agent");
                    String ipAddress = ClientIpUtil.getRealClientIp(context);

                    int updatedRows = updateCookieOnVersionChange(user.getId(), record.oldCookie, cookieValue,
                            browserInfo.browser, browserInfo.os, browserInfo.browserVersion, browserInfo.osVersion,
                            userAgent, ipAddress, deviceFingerprint);

                    if (updatedRows > 0) {
                        log.info("Updated cookie for user: " + user.getUsername() +
                                " - Browser: " + browserInfo.browser + " " + browserInfo.browserVersion +
                                " - OS: " + browserInfo.os + " " + browserInfo.osVersion);
                    } else {
                        log.warning("No records updated for cookie change - user: " + user.getUsername());
                    }

                    return true;
                } else {
                    log.info("User " + user.getUsername() + " cannot skip OTP - version downgrade detected, " +
                            "browser: " + browserInfo.browser + " " + record.oldBrowserVersion + " → "
                            + browserInfo.browserVersion +
                            ", OS: " + browserInfo.os + " " + record.oldOsVersion + " → " + browserInfo.osVersion);
                    return false;
                }
            }
        } else {
            // Record expired
            String matchType = record.isExactCookieMatch ? "exact cookie" : "similar browser/OS";
            log.info("User " + user.getUsername() + " cannot skip OTP - " + matchType + " expired: " +
                    (timeDiff / 1000 / 60 / 60) + " hours ago");
            return false;
        }
    }

    /**
     * Check if user has any device record in database (for device alert)
     * @param context AuthenticationFlowContext
     * @param user UserModel
     * @return true if user has any active device record
     */
    public static boolean hasDeviceRecord(AuthenticationFlowContext context, UserModel user) {
        try (Connection conn = getConnection()) {
            String sql = "SELECT COUNT(*) FROM tb_otp_login_history " +
                    "WHERE s_user_id = ? AND b_is_active = TRUE";

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, user.getId());

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        int count = rs.getInt(1);
                        boolean hasRecord = count > 0;
                        log.info("User " + user.getUsername() + " has " + count + " active device record(s) in database");
                        return hasRecord;
                    }
                }
            }
        } catch (SQLException e) {
            log.warning("Error checking device record for user: " + user.getUsername() + " - " + e.getMessage());
        }
        return false;
    }

    /**
     * Update last login activity when user skips OTP
     * This ensures that frequently used devices have newer timestamps
     * @param context AuthenticationFlowContext
     * @param user UserModel
     */
    public static void updateLastLoginActivity(AuthenticationFlowContext context, UserModel user) {
        try (Connection conn = getConnection()) {
            String cookieValue = getCookieValue(context, user);
            if (cookieValue == null) {
                log.info("No cookie found when updating login activity for user: " + user.getUsername());
                return;
            }

            String sql = "UPDATE tb_otp_login_history SET " +
                    "t_last_login_activity = CURRENT_TIMESTAMP, t_updated_at = CURRENT_TIMESTAMP " +
                    "WHERE s_user_id = ? AND s_cookie = ? AND b_is_active = TRUE";

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, user.getId());
                stmt.setString(2, cookieValue);

                int rowsUpdated = stmt.executeUpdate();
                if (rowsUpdated > 0) {
                    log.info("Updated last login activity for user: " + user.getUsername() + 
                            " (skip OTP)");
                } else {
                    log.info("No records updated for login activity - user: " + user.getUsername());
                }
            }
        } catch (SQLException e) {
            log.warning("Error updating last login activity for user: " + user.getUsername() + " - " + e.getMessage());
        }
    }

    /**
     * Check if user has EVER logged in before (any record, active or inactive)
     * Used to determine if this is user's first login ever
     * @param context AuthenticationFlowContext
     * @param user UserModel
     * @return true if user has any login history (first login = false)
     */
    public static boolean hasEverLoggedIn(AuthenticationFlowContext context, UserModel user) {
        try (Connection conn = getConnection()) {
            String sql = "SELECT COUNT(*) FROM tb_otp_login_history " +
                    "WHERE s_user_id = ?";

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, user.getId());

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        int count = rs.getInt(1);
                        boolean hasEverLoggedIn = count > 0;
                        log.info("User " + user.getUsername() + " has " + count + " total login record(s) in database (ever logged in: " + hasEverLoggedIn + ")");
                        return hasEverLoggedIn;
                    }
                }
            }
        } catch (SQLException e) {
            log.warning("Error checking user login history for user: " + user.getUsername() + " - " + e.getMessage());
        }
        return false;
    }

    /**
     * Check if current device should trigger device alert
     * Logic: Chỉ gửi alert khi thực sự là thiết bị mới hoàn toàn
     * KHÔNG gửi alert cho:
     * 1. User lần đầu tiên đăng nhập (chưa hề đăng nhập lần nào)
     * 2. Thiết bị đã từng được trust (kể cả inactive - do bị đẩy ra)
     * 3. Thiết bị có cookie hết hạn nhưng đã từng được trust
     * 
     * @param context AuthenticationFlowContext
     * @param user UserModel
     * @return true if should send device alert
     */
    public static boolean shouldSendDeviceAlert(AuthenticationFlowContext context, UserModel user) {
        try (Connection conn = getConnection()) {
            // BƯỚC 1: Kiểm tra xem user có phải lần đầu tiên đăng nhập không
            boolean hasEverLoggedIn = hasEverLoggedIn(context, user);
            if (!hasEverLoggedIn) {
                log.info("User " + user.getUsername() + " should NOT receive device alert - " +
                        "this is user's first login ever (no login history)");
                return false;
            }

            // Parse User-Agent để lấy thông tin browser/OS hiện tại
            String userAgent = context.getHttpRequest().getHttpHeaders().getHeaderString("User-Agent");
            BrowserInfo currentBrowserInfo = parseUserAgent(userAgent);
            
            // Get cookie value from request
            String cookieValue = getCookieValue(context, user);
            
            // BƯỚC 2: Query để kiểm tra:
            // 1. Có record ACTIVE với exact cookie match không?
            // 2. Có record ACTIVE với similar browser/OS không?
            // 3. Có record INACTIVE với exact cookie match không? (thiết bị bị đẩy ra)
            // 4. Có record INACTIVE với similar browser/OS không? (cookie hết hạn)
            String sql = "SELECT " +
                    "(SELECT COUNT(*) FROM tb_otp_login_history WHERE s_user_id = ? AND b_is_active = TRUE " +
                    "AND s_cookie = ? AND s_browser = ? AND s_os = ?) as active_count, " +
                    "(SELECT COUNT(*) FROM tb_otp_login_history WHERE s_user_id = ? AND " +
                    "s_cookie = ? AND s_browser = ? AND s_os = ?) as current_device_count";

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, user.getId()); // active_count query - user_id
                stmt.setString(2, cookieValue); // active_count query - cookie match
                stmt.setString(3, currentBrowserInfo.browser); // active_count query - browser match
                stmt.setString(4, currentBrowserInfo.os); // active_count query - os match
                stmt.setString(5, user.getId()); // current_device_count query - user_id
                stmt.setString(6, cookieValue); // current_device_count query - cookie match
                stmt.setString(7, currentBrowserInfo.browser); // current_device_count query - browser match
                stmt.setString(8, currentBrowserInfo.os); // current_device_count query - os match

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        int activeCount = rs.getInt("active_count");
                        int currentDeviceCount = rs.getInt("current_device_count");
                        
                        // Chỉ gửi alert khi:
                        // - User đã từng đăng nhập trước đó (hasEverLoggedIn = true)
                        // - VÀ không có record active nào với cookie+browser+OS hiện tại (activeCount == 0)
                        // - VÀ không có record nào với cookie+browser+OS hiện tại (currentDeviceCount == 0) - thiết bị hoàn toàn mới
                        // Logic: Bỏ qua alert chỉ cho lần đầu tiên user đăng nhập (hasEverLoggedIn = false)
                        // Còn lại, nếu thiết bị mới (currentDeviceCount == 0) thì gửi alert
                        boolean shouldAlert = (activeCount == 0 && currentDeviceCount == 0);
                        
                        if (shouldAlert) {
                            log.info("User " + user.getUsername() + " should receive device alert - " +
                                    "completely new device for existing user (browser: " + currentBrowserInfo.browser + 
                                    ", OS: " + currentBrowserInfo.os + ")");
                        } else if (activeCount > 0) {
                            log.info("User " + user.getUsername() + " should NOT receive device alert - " +
                                    "found " + activeCount + " active device record(s)");
                        } else if (currentDeviceCount > 0) {
                            log.info("User " + user.getUsername() + " should NOT receive device alert - " +
                                    "device was previously trusted but now inactive (cookie: " + cookieValue + 
                                    ", browser: " + currentBrowserInfo.browser + ", OS: " + currentBrowserInfo.os + ")");
                        }
                        
                        return shouldAlert;
                    }
                }
            }
        } catch (SQLException e) {
            log.warning("Error checking device alert for user: " + user.getUsername() + " - " + e.getMessage());
        }
        return true; // Default to alert if error
    }

}

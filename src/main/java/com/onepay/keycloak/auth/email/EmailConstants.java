package com.onepay.keycloak.auth.email;

import lombok.experimental.UtilityClass;

@UtilityClass
public class EmailConstants {
	public static final String CODE = "emailCode";
	public static final String CODE_LENGTH = "length";
	public static final String CODE_TTL = "ttl";
	public static final String CODE_ATTEMPTS = "emailCodeAttempts";
	public static final String EMAIL_CODE_LAST_SENT = "emailCodeLastSent";
	public static final long RESEND_INTERVAL_MILLIS = 60_000L; // 1 phút
	public static final int DEFAULT_LENGTH = 6;
	public static final int DEFAULT_TTL = 300; // 5 phút
	public static final int MAX_ATTEMPTS = 5;
}

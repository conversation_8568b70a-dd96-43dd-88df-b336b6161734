package com.onepay.keycloak.auth.email;

import com.onepay.keycloak.auth.config.AuthConfigManager;
import org.keycloak.authentication.AuthenticationFlowContext;
import org.keycloak.models.UserModel;

import jakarta.ws.rs.core.Cookie;
import jakarta.ws.rs.core.NewCookie;
import jakarta.ws.rs.core.Response;
import org.keycloak.http.HttpResponse;
import java.util.Base64;
import java.util.logging.Logger;

/**
 * Utility class for managing device authentication cookies
 * This is a supplementary system to the existing device tracking
 * 
 * Cookie system: 180 days (for IP change scenarios)
 * Device tracking: 180 days (existing system, extended)
 */
public class CookieDeviceManager {

    private static final Logger log = Logger.getLogger(CookieDeviceManager.class.getName());

    // Cookie configuration - now loaded from config
    // Note: DEVICE_COOKIE_NAME is now dynamic based on theme, so we get it per request
    private static final int COOKIE_MAX_AGE = AuthConfigManager.getCookieMaxAgeSeconds();
    private static final String COOKIE_PATH = AuthConfigManager.getCookiePath();
    private static final boolean COOKIE_SECURE = AuthConfigManager.isCookieSecure();
    private static final boolean COOKIE_HTTP_ONLY = AuthConfigManager.isCookieHttpOnly();

    /**
     * Generate a device fingerprint based on User-Agent only (no IP)
     * This ensures the same device is recognized even when IP changes
     */
    public static String generateDeviceFingerprint(AuthenticationFlowContext context) {
        String userAgent = context.getHttpRequest().getHttpHeaders().getHeaderString("User-Agent");
        if (userAgent == null || userAgent.trim().isEmpty()) {
            userAgent = "unknown";
        }

        // Create a stable fingerprint based on User-Agent only
        String fingerprint = userAgent.trim();
        String hashedFingerprint = Integer.toHexString(fingerprint.hashCode());

        log.info("Generated device fingerprint: " + hashedFingerprint + " for User-Agent: " + userAgent);
        return hashedFingerprint;
    }

    /**
     * Check if the current device is trusted based on cookie
     * This is supplementary to the existing device tracking system
     */
    public static boolean isDeviceTrustedByCookie(AuthenticationFlowContext context, UserModel user) {
        try {
            log.info("=== COOKIE DEVICE MANAGER START ===");
            log.info("User: " + (user != null ? user.getUsername() : "NULL"));
            // Create theme-specific and user-specific cookie name
            String themeCookieName = AuthConfigManager.getCookieName(context);
            String userCookieName = themeCookieName + "_" + user.getUsername();
            log.info("Theme: " + AuthConfigManager.detectTheme(context) + ", Cookie name: " + userCookieName);

            String deviceFingerprint = generateDeviceFingerprint(context);

            // Get cookie from request
            Cookie deviceCookie = context.getHttpRequest().getHttpHeaders().getCookies().get(userCookieName);

            if (deviceCookie == null) {
                log.info("No device cookie found for user: " + user.getUsername());
                return false;
            }

            String cookieValue = deviceCookie.getValue();
            if (cookieValue == null || cookieValue.trim().isEmpty()) {
                log.info("Empty device cookie for user: " + user.getUsername());
                return false;
            }

            // Decode and validate cookie
            String decodedValue = new String(Base64.getDecoder().decode(cookieValue));
            String[] parts = decodedValue.split("\\|");

            if (parts.length != 3) {
                log.info("Invalid cookie format for user: " + user.getUsername());
                return false;
            }

            String cookieUsername = parts[0];
            String cookieFingerprint = parts[1];
            String cookieTimestamp = parts[2];

            // Validate username matches
            if (!user.getUsername().equals(cookieUsername)) {
                log.info("Cookie username mismatch for user: " + user.getUsername());
                return false;
            }

            // Validate device fingerprint matches
            if (!deviceFingerprint.equals(cookieFingerprint)) {
                log.info("Device fingerprint mismatch for user: " + user.getUsername() +
                        " (expected: " + deviceFingerprint + ", got: " + cookieFingerprint + ")");
                return false;
            }

            // Validate timestamp (check if cookie is not expired - 180 days)
            long timestamp = Long.parseLong(cookieTimestamp);
            long now = System.currentTimeMillis();
            long cookieAge = now - timestamp;
            long maxAge = COOKIE_MAX_AGE * 1000L; // Convert to milliseconds

            if (cookieAge > maxAge) {
                log.info("Device cookie expired for user: " + user.getUsername() +
                        " (age: " + (cookieAge / 1000 / 60 / 60) + " hours)");
                return false;
            }

            log.info("Device is trusted via cookie for user: " + user.getUsername() +
                    " (cookie age: " + (cookieAge / 1000 / 60 / 60) + " hours)");
            return true;

        } catch (Exception e) {
            log.warning("Error validating device cookie for user: " + user.getUsername() + " - " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Set a trusted device cookie after successful OTP verification
     * This supplements the existing device tracking system
     */
    public static String setTrustedDeviceCookie(AuthenticationFlowContext context, UserModel user) {
        String encodedValue = "";
        try {
            // Clear any existing cookie before creating a new one
            String deviceFingerprint = generateDeviceFingerprint(context);
            String cookieValue = createCookieValue(user.getUsername(), deviceFingerprint);
            log.info("Cookie value: " + cookieValue);

            // Encode cookie value
            encodedValue = Base64.getEncoder().encodeToString(cookieValue.getBytes());
            log.info("Encoded cookie value: " + encodedValue);
            // Create NewCookie object
            // Create theme-specific and user-specific cookie name
            String themeCookieName = AuthConfigManager.getCookieName(context);
            String userCookieName = themeCookieName + "_" + user.getUsername();

            NewCookie newCookie = new NewCookie(
                    userCookieName, // name
                    encodedValue, // value
                    COOKIE_PATH, // path
                    null, // domain
                    null, // comment
                    COOKIE_MAX_AGE, // maxAge
                    COOKIE_SECURE, // secure
                    COOKIE_HTTP_ONLY // httpOnly
            );

            // Set cookie using correct Keycloak API
            HttpResponse httpResponse = context.getSession().getContext().getHttpResponse();
            httpResponse.setHeader("Set-Cookie", newCookie.toString());

            log.info("Set trusted device cookie for user: " + user.getUsername() +
                    " with fingerprint: " + deviceFingerprint + " (valid for 180 days)");
            return encodedValue;
        } catch (Exception e) {
            log.warning("Error setting device cookie for user: " + user.getUsername() + " - " + e.getMessage());
        }
        return null;
    }

    /**
     * Clear the trusted device cookie (for logout or security reasons)
     */
    public static void clearTrustedDeviceCookie(AuthenticationFlowContext context, UserModel user) {
        try {
            // Create NewCookie with Max-Age=0 to clear the cookie
            // Create theme-specific and user-specific cookie name
            String themeCookieName = AuthConfigManager.getCookieName(context);
            String userCookieName = themeCookieName + "_" + user.getUsername();

            NewCookie clearCookie = new NewCookie(
                    userCookieName, // name
                    "", // value (empty)
                    COOKIE_PATH, // path
                    null, // domain
                    null, // comment
                    0, // maxAge (0 = delete)
                    COOKIE_SECURE, // secure
                    COOKIE_HTTP_ONLY // httpOnly
            );

            // Clear cookie using correct Keycloak API
            HttpResponse httpResponse = context.getSession().getContext().getHttpResponse();
            httpResponse.setHeader("Set-Cookie", clearCookie.toString());

            log.info("Cleared trusted device cookie");

        } catch (Exception e) {
            log.warning("Error clearing device cookie - " + e.getMessage());
        }
    }

    /**
     * Create cookie value with username, device fingerprint, and timestamp
     */
    private static String createCookieValue(String username, String deviceFingerprint) {
        long timestamp = System.currentTimeMillis();
        return username + "|" + deviceFingerprint + "|" + timestamp;
    }

    /**
     * Get device information for logging/debugging
     */
    public static String getDeviceInfo(AuthenticationFlowContext context) {
        String userAgent = context.getHttpRequest().getHttpHeaders().getHeaderString("User-Agent");
        String deviceFingerprint = generateDeviceFingerprint(context);

        return String.format("User-Agent: %s, Device Fingerprint: %s",
                userAgent != null ? userAgent : "unknown", deviceFingerprint);
    }
}

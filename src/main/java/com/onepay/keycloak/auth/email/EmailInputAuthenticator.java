package com.onepay.keycloak.auth.email;

import org.keycloak.authentication.AuthenticationFlowContext;
import org.keycloak.authentication.Authenticator;
import org.keycloak.authentication.AuthenticatorFactory;
import org.keycloak.events.Errors;
import org.keycloak.forms.login.LoginFormsProvider;
import org.keycloak.models.AuthenticationExecutionModel;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.KeycloakSessionFactory;
import org.keycloak.models.RealmModel;
import org.keycloak.models.UserModel;
import org.keycloak.models.utils.FormMessage;
import org.keycloak.provider.ProviderConfigProperty;
import org.keycloak.sessions.AuthenticationSessionModel;
import org.keycloak.Config;

import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;
import java.util.List;
import java.util.logging.Logger;

public class EmailInputAuthenticator implements Authenticator {

    private static final Logger log = Logger.getLogger(EmailInputAuthenticator.class.getName());

    public static final String EMAIL_PARAM = "email";
    public static final String EMAIL_INPUT_STEP = "email_input_step";

    @Override
    public void authenticate(AuthenticationFlowContext context) {
        log.info("=== EmailInputAuthenticator.authenticate START ===");
        log.info("User: " + context.getUser().getUsername());

        UserModel user = context.getUser();
        String email = user.getEmail();
        if (email != null && !email.trim().isEmpty()) {
            // Nếu đã có email, bỏ qua bước nhập email
            context.getAuthenticationSession().setAuthNote("user_email", email.trim());
            log.info("User already has email, skipping input: " + email);
            context.success();
            return;
        }

        // Hiển thị form nhập email
        log.info("Showing email input form");
        challenge(context, null);
    }

    @Override
    public void action(AuthenticationFlowContext context) {
        log.info("=== EmailInputAuthenticator.action START ===");

        MultivaluedMap<String, String> formData = context.getHttpRequest().getDecodedFormParameters();
        String email = formData.getFirst(EMAIL_PARAM);

        log.info("Email from form: " + email);

        if (email == null || email.trim().isEmpty()) {
            log.warning("Email is empty");
            challenge(context, "missing-email");
            return;
        }

        // Validate email format
        if (!isValidEmail(email)) {
            log.warning("Invalid email format: " + email);
            challenge(context, "invalid-email");
            return;
        }

        // Lưu email vào session
        context.getAuthenticationSession().setAuthNote("user_email", email.trim());

        // Cập nhật email cho user và lưu vĩnh viễn
        UserModel user = context.getUser();
        user.setEmail(email.trim());

        // Đánh dấu email đã được verify (vì user đã nhập và sẽ nhận OTP)
        user.setEmailVerified(true);

        log.info("Email saved permanently to user profile: " + email.trim());
        log.info("Email verified status set to: true");

        context.success();
        log.info("=== EmailInputAuthenticator.action END ===");
    }

    private void challenge(AuthenticationFlowContext context, String error) {
        log.info("=== EmailInputAuthenticator.challenge START ===");

        LoginFormsProvider form = context.form().setExecution(context.getExecution().getId());

        if (error != null) {
            form.setError(error);
        }

        Response response = form.createForm("email-input.ftl");
        context.challenge(response);

        log.info("Email input form created and challenge set");
        log.info("=== EmailInputAuthenticator.challenge END ===");
    }

    private boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }

        // Basic email validation
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return email.trim().matches(emailRegex);
    }

    @Override
    public boolean requiresUser() {
        return true;
    }

    @Override
    public boolean configuredFor(KeycloakSession session, RealmModel realm, UserModel user) {
        return true;
    }

    @Override
    public void setRequiredActions(KeycloakSession session, RealmModel realm, UserModel user) {
        // NOOP
    }

    @Override
    public void close() {
        // NOOP
    }

    public static class Factory implements AuthenticatorFactory {
        public static final String PROVIDER_ID = "email-input-authenticator";
        public static final EmailInputAuthenticator SINGLETON = new EmailInputAuthenticator();

        @Override
        public String getId() {
            return PROVIDER_ID;
        }

        @Override
        public String getDisplayType() {
            return "Email Input";
        }

        @Override
        public String getReferenceCategory() {
            return null;
        }

        @Override
        public boolean isConfigurable() {
            return false;
        }

        @Override
        public AuthenticationExecutionModel.Requirement[] getRequirementChoices() {
            return REQUIREMENT_CHOICES;
        }

        @Override
        public boolean isUserSetupAllowed() {
            return false;
        }

        @Override
        public String getHelpText() {
            return "Email input authenticator for OTP flow.";
        }

        @Override
        public List<ProviderConfigProperty> getConfigProperties() {
            return null;
        }

        @Override
        public Authenticator create(KeycloakSession session) {
            return SINGLETON;
        }

        @Override
        public void init(Config.Scope config) {
        }

        @Override
        public void postInit(KeycloakSessionFactory factory) {
        }

        @Override
        public void close() {
        }
    }
}
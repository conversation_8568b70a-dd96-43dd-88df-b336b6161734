package com.onepay.keycloak.auth.email;

import com.onepay.keycloak.auth.config.AuthConfigManager;
import org.keycloak.authentication.AuthenticationFlowContext;
import org.keycloak.authentication.Authenticator;
import org.keycloak.authentication.AuthenticatorFactory;
import org.keycloak.models.AuthenticationExecutionModel;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.KeycloakSessionFactory;
import org.keycloak.models.RealmModel;
import org.keycloak.models.UserModel;
import org.keycloak.provider.ProviderConfigProperty;
import org.keycloak.forms.login.LoginFormsProvider;
import org.keycloak.email.EmailException;
import org.keycloak.email.EmailTemplateProvider;
import org.keycloak.events.Errors;
import org.keycloak.sessions.AuthenticationSessionModel;
import org.keycloak.Config;

import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class EmailOtpCombinedAuthenticator implements Authenticator {
    private static final Logger log = Logger.getLogger(EmailOtpCombinedAuthenticator.class.getName());
    private static final String EMAIL_PARAM = "email";
    private static final String OTP_PARAM = "otp";
    private static final String TRUST_DEVICE_PARAM = "trustDevice";
    private static final String OTP_SESSION_KEY = "combined_otp_code";
    private static final String OTP_TTL_SESSION_KEY = "combined_otp_ttl";
    private static final String OTP_LAST_SENT_KEY = "combined_otp_last_sent";
    private static final String OTP_ATTEMPTS_KEY = "combined_otp_attempts";
    private static final int OTP_LENGTH = AuthConfigManager.getOtpLength();
    private static final int OTP_TTL_SECONDS = AuthConfigManager.getOtpTtlSeconds();
    private static final long RESEND_INTERVAL_MILLIS = AuthConfigManager.getOtpResendIntervalMillis();
    private static final int MAX_OTP_ATTEMPTS = AuthConfigManager.getOtpMaxAttempts();
    private static final AuthenticationExecutionModel.Requirement[] REQUIREMENT_CHOICES = {
            AuthenticationExecutionModel.Requirement.REQUIRED,
            AuthenticationExecutionModel.Requirement.ALTERNATIVE,
            AuthenticationExecutionModel.Requirement.DISABLED
    };

    @Override
    public void authenticate(AuthenticationFlowContext context) {
        log.info("=== AUTHENTICATE START ===");
        UserModel user = context.getUser();
        boolean skipOtp = false;
        // BƯỚC 1: Kiểm tra cookie và tự động tạo nếu chưa có (để hỗ trợ user xóa cookies)
        try {
            log.info("Calling CookieDeviceManager.isDeviceTrustedByCookie for user: " + user.getUsername());
            boolean hasCookie = CookieDeviceManager.isDeviceTrustedByCookie(context, user);
            if (!hasCookie) {
                // Tự động tạo cookie để có thể check database với browser/OS tương tự
                log.info("No cookie found for user: " + user.getUsername() + " - creating cookie for database check");
                CookieDeviceManager.setTrustedDeviceCookie(context, user);
            } else {
                log.info("Cookie found for user: " + user.getUsername() + " - cookie OK");
            }
        } catch (Exception e) {
            log.warning("Error in cookie check for user: " + user.getUsername() + " - " + e.getMessage());
            e.printStackTrace();
        }

        String deviceAttrKey = "";
        // BƯỚC 2: Gửi userid xuống database để query
        try {
            log.info("Calling OtpLoginHistoryManager.canSkipOtp for user: " + user.getUsername());
            boolean canSkipOtp = OtpLoginHistoryManager.canSkipOtp(context, user);
            if (canSkipOtp) {
                log.info("User can skip OTP based on database record: " + user.getUsername());
                skipOtp = true;
            } else {
                log.info("User cannot skip OTP based on database record: " + user.getUsername());
            }
        } catch (Exception e) {
            log.warning("Error calling OtpLoginHistoryManager.canSkipOtp: " + e.getMessage());
            e.printStackTrace();
        }

        if (!skipOtp) {
            // Device alert logic - check for new device and send alert
            checkAndSendDeviceAlert(context, user);
        }

        String email = user.getEmail();
        log.info("Final skipOtp value for user: " + user.getUsername() + " - " + skipOtp);
        if (skipOtp) {
            log.info("email: " + email);
            if (email == null || email.trim().isEmpty()) {
                user.removeRequiredAction("VERIFY_PROFILE");
                log.info("Removed VERIFY_PROFILE required action for user: " + user.getUsername());

                // Clear device tracking attributes to force OTP on next login since no email
                // available
                user.removeAttribute(deviceAttrKey);
                user.removeAttribute("first_device");
                log.info("Cleared device tracking attributes for user without email: " + user.getUsername());

                // Show email input form instead of success to collect email in same session
                log.info("Showing email input form for user without email: " + user.getUsername());
                showForm(context, false, null);
                return;
            }
            log.info("Skipping OTP for user: " + user.getUsername() + " - device already authenticated");
            
            // Update last login activity when skipping OTP
            OtpLoginHistoryManager.updateLastLoginActivity(context, user);
            
            context.success();
            return;
        }
        log.info("Proceeding to OTP challenge for user: " + user.getUsername());

        // Original logic for email/OTP handling
        String sentEmail = context.getAuthenticationSession().getAuthNote(EMAIL_PARAM);
        boolean hasEmail = (email != null && !email.trim().isEmpty())
                || (sentEmail != null && !sentEmail.trim().isEmpty());
        boolean otpSent = context.getAuthenticationSession().getAuthNote(OTP_SESSION_KEY) != null;

        // Nếu user đã có email thì luôn sinh OTP mới và gửi mail
        if (email != null && !email.trim().isEmpty()) {
            String code = generateOtpCode();
            context.getAuthenticationSession().setAuthNote(OTP_SESSION_KEY, code);
            context.getAuthenticationSession().setAuthNote(OTP_TTL_SESSION_KEY,
                    String.valueOf(System.currentTimeMillis() + OTP_TTL_SECONDS * 1000L));
            context.getAuthenticationSession().setAuthNote(OTP_LAST_SENT_KEY,
                    String.valueOf(System.currentTimeMillis()));
            sendOtpEmail(context, email.trim(), code);
            showForm(context, true, null);
            return;
        }

        if (!hasEmail || !otpSent) {
            // Hiển thị form nhập email (và ẩn OTP)
            showForm(context, false, null);
        } else {
            // Hiển thị form nhập OTP
            showForm(context, true, null);
        }
    }

    @Override
    public void action(AuthenticationFlowContext context) {
        MultivaluedMap<String, String> formData = context.getHttpRequest().getDecodedFormParameters();
        String email = formData.getFirst(EMAIL_PARAM);
        String otp = formData.getFirst(OTP_PARAM);
        String trustDevice = formData.getFirst(TRUST_DEVICE_PARAM);
        AuthenticationSessionModel session = context.getAuthenticationSession();
        UserModel user = context.getUser();

        // Handle resend OTP request
        if (formData.containsKey("resend")) {
            // SECURITY: Check attempts BEFORE allowing resend
            String attemptsKey = OTP_ATTEMPTS_KEY + "_" + user.getUsername();
            String attemptsStr = session.getAuthNote(attemptsKey);
            int attempts = attemptsStr != null ? Integer.parseInt(attemptsStr) : 0;

            if (attempts >= MAX_OTP_ATTEMPTS) {
                log.warning(
                        "Resend blocked for user " + user.getUsername() + " due to " + attempts + " failed attempts");
                context.getEvent().user(context.getUser()).error(Errors.INVALID_USER_CREDENTIALS);
                showForm(context, true, "too-many-attempts-no-resend");
                return;
            }

            String lastSentStr = session.getAuthNote(OTP_LAST_SENT_KEY);
            long now = System.currentTimeMillis();
            if (lastSentStr != null) {
                long lastSent = Long.parseLong(lastSentStr);
                if (now - lastSent < RESEND_INTERVAL_MILLIS) {
                    showForm(context, true, "You can only resend the code after 1 minute!");
                    return;
                }
            }

            // Resend OTP
            String userEmail = user.getEmail();
            if (userEmail == null || userEmail.isEmpty()) {
                userEmail = session.getAuthNote(EMAIL_PARAM);
            }
            if (userEmail != null && !userEmail.isEmpty()) {
                String code = generateOtpCode();
                session.setAuthNote(OTP_SESSION_KEY, code);
                session.setAuthNote(OTP_TTL_SESSION_KEY,
                        String.valueOf(System.currentTimeMillis() + OTP_TTL_SECONDS * 1000L));
                session.setAuthNote(OTP_LAST_SENT_KEY, String.valueOf(now));
                sendOtpEmail(context, userEmail, code);
                log.info("OTP resent successfully for user: " + user.getUsername() + " (attempts: " + attempts + ")");
                showForm(context, true, "OTP code has been resent to your email.");
                return;
            }
        }

        // Nếu chưa có email, xử lý nhập email
        if ((user.getEmail() == null || user.getEmail().isEmpty()) && (session.getAuthNote(EMAIL_PARAM) == null)) {
            if (email == null || email.trim().isEmpty()) {
                showForm(context, false, "missing-email");
                return;
            }
            if (!isValidEmail(email)) {
                showForm(context, false, "invalid-email");
                return;
            }
            // Lưu email vào user và session
            user.setEmail(email.trim());
            user.setEmailVerified(true);
            session.setAuthNote(EMAIL_PARAM, email.trim());
            // Gửi OTP
            String code = generateOtpCode();
            session.setAuthNote(OTP_SESSION_KEY, code);
            session.setAuthNote(OTP_TTL_SESSION_KEY,
                    String.valueOf(System.currentTimeMillis() + OTP_TTL_SECONDS * 1000L));
            session.setAuthNote(OTP_LAST_SENT_KEY, String.valueOf(System.currentTimeMillis()));
            sendOtpEmail(context, email.trim(), code);
            showForm(context, true, null);
            return;
        }

        // Nếu đã có email, xử lý nhập OTP
        String userEmail = user.getEmail();
        if (userEmail == null || userEmail.isEmpty()) {
            userEmail = session.getAuthNote(EMAIL_PARAM);
        }
        String code = session.getAuthNote(OTP_SESSION_KEY);
        String ttlStr = session.getAuthNote(OTP_TTL_SESSION_KEY);

        if (otp == null || otp.trim().isEmpty()) {
            showForm(context, true, "missing-otp");
            return;
        }
        if (code == null || ttlStr == null) {
            showForm(context, true, "otp-not-sent");
            return;
        }

        // Đếm số lần nhập sai OTP theo user
        String attemptsKey = OTP_ATTEMPTS_KEY + "_" + user.getUsername();
        String attemptsStr = session.getAuthNote(attemptsKey);
        int attempts = attemptsStr != null ? Integer.parseInt(attemptsStr) : 0;

        // SECURITY FIX: Check attempts TRƯỚC khi validate OTP
        // Nếu đã vượt quá số lần cho phép, block luôn dù OTP có đúng hay không
        if (attempts >= MAX_OTP_ATTEMPTS) {
            log.warning("User " + user.getUsername() + " blocked due to " + attempts + " failed OTP attempts");
            context.getEvent().user(context.getUser()).error(Errors.INVALID_USER_CREDENTIALS);
            showForm(context, true, "too-many-attempts");
            return;
        }

        if (otp.equals(code)) {
            // Kiểm tra OTP có hết hạn không
            if (Long.parseLong(ttlStr) < System.currentTimeMillis()) {
                // OTP expired - KHÔNG tăng attempts vì đây không phải lỗi của user
                context.getEvent().user(context.getUser()).error(Errors.EXPIRED_CODE);
                showForm(context, true, "expired-otp");
                return;
            } else {
                // OTP valid - xác thực thành công
                log.info("OTP validation successful for user: " + user.getUsername());

                // Clear OTP session data và reset attempts
                clearOtpSessionData(session, user);

                // BƯỚC 3: OTP thành công - Xử lý device trust
                boolean shouldTrustDevice = "true".equals(trustDevice);
                log.info("OTP validation successful for user: " + user.getUsername() + 
                        " - Trust device: " + shouldTrustDevice);

                if (shouldTrustDevice) {
                    // User chọn "Don't ask again on this device" - Lưu database record
                    log.info("User chose to trust device - saving database record");
                    
                    // Cookie đã được tạo ở BƯỚC 1, chỉ cần lưu database record
                    OtpLoginHistoryManager.recordOtpLogin(context, user);
                    
                    log.info("Device trusted and database record saved (90 days) for user: " + user.getUsername());
                } else {
                    // User không chọn trust device - Xóa cookie và không lưu database record
                    log.info("User chose NOT to trust device - clearing cookie and no database record created");
                    
                    // Xóa cookie vì user không muốn trust device
                    CookieDeviceManager.clearTrustedDeviceCookie(context, user);
                }

                context.success();
            }
        } else {
            // OTP invalid - tăng số lần nhập sai
            attempts++;
            session.setAuthNote(attemptsKey, String.valueOf(attempts));
            log.info("Invalid OTP attempt #" + attempts + "/" + MAX_OTP_ATTEMPTS + " for user: " + user.getUsername());

            // Note: Không cần check attempts >= MAX_OTP_ATTEMPTS ở đây nữa
            // vì đã check ở đầu function rồi
            context.getEvent().user(context.getUser()).error(Errors.INVALID_USER_CREDENTIALS);
            showForm(context, true, "invalid-otp");
            return;
        }
    }

    private void clearOtpSessionData(AuthenticationSessionModel session, UserModel user) {
        log.info("=== clearOtpSessionData START ===");
        log.info("Clearing OTP session data for user: " + user.getUsername());

        // Clear tất cả OTP-related auth notes
        String attemptsKey = OTP_ATTEMPTS_KEY + "_" + user.getUsername();

        log.info("Removing auth notes:");
        log.info("- OTP code key: " + OTP_SESSION_KEY);
        log.info("- OTP TTL key: " + OTP_TTL_SESSION_KEY);
        log.info("- OTP last sent key: " + OTP_LAST_SENT_KEY);
        log.info("- Email param key: " + EMAIL_PARAM);
        log.info("- Attempts key: " + attemptsKey);

        session.removeAuthNote(OTP_SESSION_KEY);
        session.removeAuthNote(OTP_TTL_SESSION_KEY);
        session.removeAuthNote(OTP_LAST_SENT_KEY);
        session.removeAuthNote(EMAIL_PARAM);
        session.removeAuthNote(attemptsKey); // Reset số lần nhập sai

        log.info("All OTP session data cleared for user: " + user.getUsername());
        log.info("=== clearOtpSessionData END ===");
    }

    private void showForm(AuthenticationFlowContext context, boolean showOtp, String error) {
        LoginFormsProvider form = context.form();
        UserModel user = context.getUser();
        String email = user.getEmail();
        if (email == null || email.isEmpty()) {
            email = context.getAuthenticationSession().getAuthNote(EMAIL_PARAM);
        }
        form.setAttribute("showOtp", showOtp);
        form.setAttribute("userEmail", email);

        // Check if buttons should be disabled due to too many attempts
        boolean disableButtons = false;
        if (error != null) {
            form.setError(error);
            // Disable both submit and resend if error is related to too many attempts
            if (error.equals("too-many-attempts") || error.equals("too-many-attempts-no-resend")) {
                disableButtons = true;
            }
        }
        form.setAttribute("disableResend", disableButtons);
        form.setAttribute("disableSubmit", disableButtons);
        
        // Show trust device checkbox only when showing OTP form
        form.setAttribute("showTrustDevice", showOtp);

        Response resp = form.createForm("email-otp-combined.ftl");
        context.challenge(resp);
    }

    private boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty())
            return false;
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return email.trim().matches(emailRegex);
    }

    private String generateOtpCode() {
        int code = (int) (Math.random() * Math.pow(10, OTP_LENGTH));
        return String.format("%0" + OTP_LENGTH + "d", code);
    }

    private void sendOtpEmail(AuthenticationFlowContext context, String email, String code) {
        log.info("=== SENDING OTP EMAIL ===");
        log.info("To: " + email);
        log.info("OTP Code: " + code);
        log.info("Subject: " + AuthConfigManager.getEmailSubjectOtp());
        log.info("Template: " + AuthConfigManager.getEmailTemplateOtp());
        
        try {
            EmailTemplateProvider emailProvider = context.getSession().getProvider(EmailTemplateProvider.class);
            emailProvider.setRealm(context.getRealm());
            emailProvider.setUser(context.getUser());

            Map<String, Object> mailBodyAttributes = new HashMap<>();
            mailBodyAttributes.put("username", context.getUser().getUsername());
            mailBodyAttributes.put("code", code);
            mailBodyAttributes.put("ttl", OTP_TTL_SECONDS);

            List<Object> subjectParams = List.of(context.getRealm().getDisplayName() != null
                    ? context.getRealm().getDisplayName()
                    : context.getRealm().getName());

            emailProvider.send(AuthConfigManager.getEmailSubjectOtp(), subjectParams,
                    AuthConfigManager.getEmailTemplateOtp(),
                    mailBodyAttributes);
            
            log.info("OTP email sent successfully to: " + email);
        } catch (EmailException e) {
            log.severe("Failed to send OTP email: " + e.getMessage());
            context.form().setError("email-send-failed");
        } catch (Exception e) {
            log.severe("Unexpected error sending OTP email: " + e.getMessage());
            context.form().setError("email-send-failed");
        }
    }

    /**
     * Check for new device and send alert email if needed
     * This method uses intelligent device detection based on cookie and database records
     */
    private void checkAndSendDeviceAlert(AuthenticationFlowContext context, UserModel user) {
        try {
            log.info("Checking device alert for user: " + user.getUsername());

            // Use intelligent device detection logic
            boolean shouldSendAlert = OtpLoginHistoryManager.shouldSendDeviceAlert(context, user);
            
            if (shouldSendAlert) {
                if (AuthConfigManager.isDeviceAlertEnabled()) {
                    log.info("Sending alert email for new device to user: " + user.getUsername());
                    sendDeviceAlertEmail(context, user);
                } else {
                    log.info("Device alert disabled - not sending email to user: " + user.getUsername());
                }
            } else {
                log.info("Known device - no alert needed for user: " + user.getUsername());
            }
        } catch (Exception e) {
            log.warning("Error in device alert check for user: " + user.getUsername() + " - " + e.getMessage());
        }
    }

    private void sendDeviceAlertEmail(AuthenticationFlowContext context, UserModel user) {
        try {
            EmailTemplateProvider emailProvider = context.getSession().getProvider(EmailTemplateProvider.class);
            emailProvider.setRealm(context.getRealm());
            emailProvider.setUser(user);

            String subject = AuthConfigManager.getEmailSubjectDeviceAlert();
            Map<String, Object> mailBodyAttributes = new HashMap<>();
            mailBodyAttributes.put("username", user.getUsername());
            
            // Get device info from User-Agent for email template
            String userAgent = context.getHttpRequest().getHttpHeaders().getHeaderString("User-Agent");
            mailBodyAttributes.put("userAgent", userAgent != null ? userAgent : "Unknown");

            List<Object> subjectParams = List.of("OnePay Invoice");
            emailProvider.send(subject, subjectParams, AuthConfigManager.getEmailTemplateDeviceAlert(),
                    mailBodyAttributes);

            log.info("Device alert email sent successfully to user: " + user.getUsername());
        } catch (EmailException e) {
            log.log(Level.SEVERE, String.format("Failed to send device alert email. user=%s", user.getUsername()), e);
        } catch (Exception e) {
            log.log(Level.SEVERE, String.format("Failed to send device alert email. user=%s", user.getUsername()), e);
        }
    }



    @Override
    public boolean requiresUser() {
        return true;
    }

    @Override
    public boolean configuredFor(KeycloakSession session, RealmModel realm, UserModel user) {
        return true;
    }

    @Override
    public void setRequiredActions(KeycloakSession session, RealmModel realm, UserModel user) {
    }

    @Override
    public void close() {
    }

    // Factory class
    public static class Factory implements AuthenticatorFactory {
        public static final String PROVIDER_ID = "email-otp-combined-authenticator";
        public static final EmailOtpCombinedAuthenticator SINGLETON = new EmailOtpCombinedAuthenticator();

        @Override
        public String getId() {
            return PROVIDER_ID;
        }

        @Override
        public String getDisplayType() {
            return "Email + OTP Combined";
        }

        @Override
        public String getReferenceCategory() {
            return null;
        }

        @Override
        public boolean isConfigurable() {
            return false;
        }

        @Override
        public AuthenticationExecutionModel.Requirement[] getRequirementChoices() {
            return REQUIREMENT_CHOICES;
        }

        @Override
        public boolean isUserSetupAllowed() {
            return false;
        }

        @Override
        public String getHelpText() {
            return "Combined email input and OTP verification in one step.";
        }

        @Override
        public List<ProviderConfigProperty> getConfigProperties() {
            return null;
        }

        @Override
        public Authenticator create(KeycloakSession session) {
            return SINGLETON;
        }

        @Override
        public void init(Config.Scope config) {
        }

        @Override
        public void postInit(KeycloakSessionFactory factory) {
        }

        @Override
        public void close() {
        }
    }
}
package com.onepay.keycloak.auth;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Utility class for sanitizing usernames with Vietnamese characters and special characters
 */
public class UsernameSanitizerUtil {
    
    // Map chuyển đổi ký tự tiếng Việt có dấu thành không dấu
    private static final Map<Character, Character> VIETNAMESE_CHAR_MAP = new HashMap<>();
    
    static {
        // Chữ thường
        VIETNAMESE_CHAR_MAP.put('à', 'a'); VIETNAMESE_CHAR_MAP.put('á', 'a'); VIETNAMESE_CHAR_MAP.put('ả', 'a'); 
        VIETNAMESE_CHAR_MAP.put('ã', 'a'); VIETNAMESE_CHAR_MAP.put('ạ', 'a');
        VIETNAMESE_CHAR_MAP.put('ă', 'a'); VIETNAMESE_CHAR_MAP.put('ằ', 'a'); VIETNAMESE_CHAR_MAP.put('ắ', 'a'); 
        VIETNAMESE_CHAR_MAP.put('ẳ', 'a'); VIETNAMESE_CHAR_MAP.put('ẵ', 'a'); VIETNAMESE_CHAR_MAP.put('ặ', 'a');
        VIETNAMESE_CHAR_MAP.put('â', 'a'); VIETNAMESE_CHAR_MAP.put('ầ', 'a'); VIETNAMESE_CHAR_MAP.put('ấ', 'a'); 
        VIETNAMESE_CHAR_MAP.put('ẩ', 'a'); VIETNAMESE_CHAR_MAP.put('ẫ', 'a'); VIETNAMESE_CHAR_MAP.put('ậ', 'a');
        
        VIETNAMESE_CHAR_MAP.put('è', 'e'); VIETNAMESE_CHAR_MAP.put('é', 'e'); VIETNAMESE_CHAR_MAP.put('ẻ', 'e'); 
        VIETNAMESE_CHAR_MAP.put('ẽ', 'e'); VIETNAMESE_CHAR_MAP.put('ẹ', 'e');
        VIETNAMESE_CHAR_MAP.put('ê', 'e'); VIETNAMESE_CHAR_MAP.put('ề', 'e'); VIETNAMESE_CHAR_MAP.put('ế', 'e'); 
        VIETNAMESE_CHAR_MAP.put('ể', 'e'); VIETNAMESE_CHAR_MAP.put('ễ', 'e'); VIETNAMESE_CHAR_MAP.put('ệ', 'e');
        
        VIETNAMESE_CHAR_MAP.put('ì', 'i'); VIETNAMESE_CHAR_MAP.put('í', 'i'); VIETNAMESE_CHAR_MAP.put('ỉ', 'i'); 
        VIETNAMESE_CHAR_MAP.put('ĩ', 'i'); VIETNAMESE_CHAR_MAP.put('ị', 'i');
        
        VIETNAMESE_CHAR_MAP.put('ò', 'o'); VIETNAMESE_CHAR_MAP.put('ó', 'o'); VIETNAMESE_CHAR_MAP.put('ỏ', 'o'); 
        VIETNAMESE_CHAR_MAP.put('õ', 'o'); VIETNAMESE_CHAR_MAP.put('ọ', 'o');
        VIETNAMESE_CHAR_MAP.put('ô', 'o'); VIETNAMESE_CHAR_MAP.put('ồ', 'o'); VIETNAMESE_CHAR_MAP.put('ố', 'o'); 
        VIETNAMESE_CHAR_MAP.put('ổ', 'o'); VIETNAMESE_CHAR_MAP.put('ỗ', 'o'); VIETNAMESE_CHAR_MAP.put('ộ', 'o');
        VIETNAMESE_CHAR_MAP.put('ơ', 'o'); VIETNAMESE_CHAR_MAP.put('ờ', 'o'); VIETNAMESE_CHAR_MAP.put('ớ', 'o'); 
        VIETNAMESE_CHAR_MAP.put('ở', 'o'); VIETNAMESE_CHAR_MAP.put('ỡ', 'o'); VIETNAMESE_CHAR_MAP.put('ợ', 'o');
        
        VIETNAMESE_CHAR_MAP.put('ù', 'u'); VIETNAMESE_CHAR_MAP.put('ú', 'u'); VIETNAMESE_CHAR_MAP.put('ủ', 'u'); 
        VIETNAMESE_CHAR_MAP.put('ũ', 'u'); VIETNAMESE_CHAR_MAP.put('ụ', 'u');
        VIETNAMESE_CHAR_MAP.put('ư', 'u'); VIETNAMESE_CHAR_MAP.put('ừ', 'u'); VIETNAMESE_CHAR_MAP.put('ứ', 'u'); 
        VIETNAMESE_CHAR_MAP.put('ử', 'u'); VIETNAMESE_CHAR_MAP.put('ữ', 'u'); VIETNAMESE_CHAR_MAP.put('ự', 'u');
        
        VIETNAMESE_CHAR_MAP.put('ỳ', 'y'); VIETNAMESE_CHAR_MAP.put('ý', 'y'); VIETNAMESE_CHAR_MAP.put('ỷ', 'y'); 
        VIETNAMESE_CHAR_MAP.put('ỹ', 'y'); VIETNAMESE_CHAR_MAP.put('ỵ', 'y');
        
        VIETNAMESE_CHAR_MAP.put('đ', 'd');
        
        // Chữ hoa
        VIETNAMESE_CHAR_MAP.put('À', 'A'); VIETNAMESE_CHAR_MAP.put('Á', 'A'); VIETNAMESE_CHAR_MAP.put('Ả', 'A'); 
        VIETNAMESE_CHAR_MAP.put('Ã', 'A'); VIETNAMESE_CHAR_MAP.put('Ạ', 'A');
        VIETNAMESE_CHAR_MAP.put('Ă', 'A'); VIETNAMESE_CHAR_MAP.put('Ằ', 'A'); VIETNAMESE_CHAR_MAP.put('Ắ', 'A'); 
        VIETNAMESE_CHAR_MAP.put('Ẳ', 'A'); VIETNAMESE_CHAR_MAP.put('Ẵ', 'A'); VIETNAMESE_CHAR_MAP.put('Ặ', 'A');
        VIETNAMESE_CHAR_MAP.put('Â', 'A'); VIETNAMESE_CHAR_MAP.put('Ầ', 'A'); VIETNAMESE_CHAR_MAP.put('Ấ', 'A'); 
        VIETNAMESE_CHAR_MAP.put('Ẩ', 'A'); VIETNAMESE_CHAR_MAP.put('Ẫ', 'A'); VIETNAMESE_CHAR_MAP.put('Ậ', 'A');
        
        VIETNAMESE_CHAR_MAP.put('È', 'E'); VIETNAMESE_CHAR_MAP.put('É', 'E'); VIETNAMESE_CHAR_MAP.put('Ẻ', 'E'); 
        VIETNAMESE_CHAR_MAP.put('Ẽ', 'E'); VIETNAMESE_CHAR_MAP.put('Ẹ', 'E');
        VIETNAMESE_CHAR_MAP.put('Ê', 'E'); VIETNAMESE_CHAR_MAP.put('Ề', 'E'); VIETNAMESE_CHAR_MAP.put('Ế', 'E'); 
        VIETNAMESE_CHAR_MAP.put('Ể', 'E'); VIETNAMESE_CHAR_MAP.put('Ễ', 'E'); VIETNAMESE_CHAR_MAP.put('Ệ', 'E');
        
        VIETNAMESE_CHAR_MAP.put('Ì', 'I'); VIETNAMESE_CHAR_MAP.put('Í', 'I'); VIETNAMESE_CHAR_MAP.put('Ỉ', 'I'); 
        VIETNAMESE_CHAR_MAP.put('Ĩ', 'I'); VIETNAMESE_CHAR_MAP.put('Ị', 'I');
        
        VIETNAMESE_CHAR_MAP.put('Ò', 'O'); VIETNAMESE_CHAR_MAP.put('Ó', 'O'); VIETNAMESE_CHAR_MAP.put('Ỏ', 'O'); 
        VIETNAMESE_CHAR_MAP.put('Õ', 'O'); VIETNAMESE_CHAR_MAP.put('Ọ', 'O');
        VIETNAMESE_CHAR_MAP.put('Ô', 'O'); VIETNAMESE_CHAR_MAP.put('Ồ', 'O'); VIETNAMESE_CHAR_MAP.put('Ố', 'O'); 
        VIETNAMESE_CHAR_MAP.put('Ổ', 'O'); VIETNAMESE_CHAR_MAP.put('Ỗ', 'O'); VIETNAMESE_CHAR_MAP.put('Ộ', 'O');
        VIETNAMESE_CHAR_MAP.put('Ơ', 'O'); VIETNAMESE_CHAR_MAP.put('Ờ', 'O'); VIETNAMESE_CHAR_MAP.put('Ớ', 'O'); 
        VIETNAMESE_CHAR_MAP.put('Ở', 'O'); VIETNAMESE_CHAR_MAP.put('Ỡ', 'O'); VIETNAMESE_CHAR_MAP.put('Ợ', 'O');
        
        VIETNAMESE_CHAR_MAP.put('Ù', 'U'); VIETNAMESE_CHAR_MAP.put('Ú', 'U'); VIETNAMESE_CHAR_MAP.put('Ủ', 'U'); 
        VIETNAMESE_CHAR_MAP.put('Ũ', 'U'); VIETNAMESE_CHAR_MAP.put('Ụ', 'U');
        VIETNAMESE_CHAR_MAP.put('Ư', 'U'); VIETNAMESE_CHAR_MAP.put('Ừ', 'U'); VIETNAMESE_CHAR_MAP.put('Ứ', 'U'); 
        VIETNAMESE_CHAR_MAP.put('Ử', 'U'); VIETNAMESE_CHAR_MAP.put('Ữ', 'U'); VIETNAMESE_CHAR_MAP.put('Ự', 'U');
        
        VIETNAMESE_CHAR_MAP.put('Ỳ', 'Y'); VIETNAMESE_CHAR_MAP.put('Ý', 'Y'); VIETNAMESE_CHAR_MAP.put('Ỷ', 'Y'); 
        VIETNAMESE_CHAR_MAP.put('Ỹ', 'Y'); VIETNAMESE_CHAR_MAP.put('Ỵ', 'Y');
        
        VIETNAMESE_CHAR_MAP.put('Đ', 'D');
    }

    /**
     * Chuyển đổi ký tự tiếng Việt có dấu thành chữ cái tiếng Anh
     * 
     * @param input Chuỗi đầu vào có thể chứa ký tự tiếng Việt
     * @return Chuỗi đã được chuyển đổi
     */
    public static String convertVietnameseToEnglish(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        
        StringBuilder result = new StringBuilder();
        for (char c : input.toCharArray()) {
            Character replacement = VIETNAMESE_CHAR_MAP.get(c);
            result.append(replacement != null ? replacement : c);
        }
        return result.toString();
    }

    /**
     * Sanitize username theo regex ^[a-zA-Z0-9._-]+$
     * Chuyển đổi ký tự tiếng Việt và thay thế ký tự đặc biệt bằng dấu gạch dưới
     * 
     * @param username Username cần sanitize
     * @return Username đã được sanitize
     */
    public static String sanitizeUsername(String username) {
        if (username == null || username.isEmpty()) {
            return "user";
        }

        username = username.toLowerCase();
        
        // Chuyển đổi ký tự tiếng Việt thành chữ cái tiếng Anh
        String converted = convertVietnameseToEnglish(username);
        
        // Regex để kiểm tra ký tự hợp lệ
        Pattern validCharPattern = Pattern.compile("^[a-zA-Z0-9._-]+$");
        
        // Nếu username đã hợp lệ, trả về luôn
        if (validCharPattern.matcher(converted).matches()) {
            return converted;
        }
        
        // Thay thế các ký tự không hợp lệ bằng dấu gạch dưới
        StringBuilder result = new StringBuilder();
        for (char c : converted.toCharArray()) {
            if (Character.isLetterOrDigit(c) || c == '.' || c == '-' || c == '_') {
                result.append(c);
            } else {
                result.append('_');
            }
        }
        
        // Loại bỏ dấu gạch dưới liên tiếp và ở đầu/cuối
        String sanitized = result.toString().replaceAll("_+", "_").replaceAll("^_+|_+$", "");
        
        // Đảm bảo username không rỗng
        if (sanitized.isEmpty()) {
            sanitized = "user";
        }
        
        return sanitized;
    }

    /**
     * Tạo Keycloak username từ username gốc và merchant ID
     * 
     * @param originalUsername Username gốc (có thể chứa tiếng Việt)
     * @param merchantId Merchant ID
     * @return Keycloak username đã được format
     */
    public static String createKeycloakUsername(String originalUsername, String merchantId) {
        String sanitizedUsername = sanitizeUsername(originalUsername);
        return sanitizedUsername + "_" + merchantId.toLowerCase();
    }
} 
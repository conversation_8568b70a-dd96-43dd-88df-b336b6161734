package com.onepay.keycloak.auth;

import org.keycloak.authentication.Authenticator;
import org.keycloak.authentication.AuthenticatorFactory;
import org.keycloak.Config.Scope;
import org.keycloak.models.AuthenticationExecutionModel.Requirement;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.KeycloakSessionFactory;
import org.keycloak.provider.ProviderConfigProperty;
import com.google.auto.service.AutoService;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

@AutoService(AuthenticatorFactory.class)
public class MaUserAuthenticatorFactory implements AuthenticatorFactory {

    public static final String PROVIDER_ID = "ma-user-authenticator";

    private static final Logger LOG = Logger.getLogger(MaUserAuthenticatorFactory.class.getName());

    private static final MaUserAuthenticator SINGLETON = new MaUserAuthenticator();

    @Override
    public String getId() {
        return PROVIDER_ID;
    }

    @Override
    public Authenticator create(KeycloakSession session) {
        return SINGLETON;
    }

    @Override
    public String getDisplayType() {
        return "MA User Authenticator";
    }

    @Override
    public String getReferenceCategory() {
        return "Login";
    }

    @Override
    public boolean isConfigurable() {
        return false;
    }

    @Override
    public Requirement[] getRequirementChoices() {
        return new Requirement[] { Requirement.REQUIRED, Requirement.ALTERNATIVE, Requirement.DISABLED };
    }

    @Override
    public boolean isUserSetupAllowed() {
        return false;
    }

    @Override
    public List<ProviderConfigProperty> getConfigProperties() {
        return new ArrayList<>();
    }

    @Override
    public String getHelpText() {
        return "Authenticates MA users by checking username/email and password with OneAM password encoding.";
    }

    @Override
    public void init(Scope config) {
    }

    @Override
    public void postInit(KeycloakSessionFactory factory) {
    }

    @Override
    public void close() {
    }
}

package com.onepay.keycloak.auth;

import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import java.security.spec.KeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.logging.Logger;

public class KeycloakPasswordHashMain {

    private static final Logger LOG = Logger.getLogger(KeycloakPasswordHashMain.class.getName());

    public static void main(String[] args) {
        try {
            // Password MD5 từ form đăng nhập
            String md5Password = "B380069315CB3130137E228F87415DA0";
            // LOG.info("Password MD5 từ form: " + md5Password);

            // Tạo salt ngẫu nhiên (trong thực tế, Keycloak sẽ tạo salt này)
            byte[] salt = Base64.getDecoder().decode("+LmBB2RqCDKL+waHTQZZLg==");
            LOG.info("Salt (Base64): " + Base64.getEncoder().encodeToString(salt));

            // Tạo hash PBKDF2-SHA512 với 210000 iterations
            SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA512");
            KeySpec spec = new PBEKeySpec(md5Password.toCharArray(), salt, 210000, 512);
            byte[] hash = factory.generateSecret(spec).getEncoded();
            String valueBase64 = Base64.getEncoder().encodeToString(hash);
            LOG.info("Generated Value (Base64): " + valueBase64);

            // Tạo secret data
            Map<String, Object> secretData = new HashMap<>();
            secretData.put("value", valueBase64);
            secretData.put("salt", Base64.getEncoder().encodeToString(salt));
            secretData.put("additionalParameters", new HashMap<>());

            // Tạo credential data
            Map<String, Object> credentialData = new HashMap<>();
            credentialData.put("hashIterations", 210000);
            credentialData.put("algorithm", "pbkdf2-sha512");
            credentialData.put("additionalParameters", new HashMap<>());

            // Chuyển thành JSON
            ObjectMapper mapper = new ObjectMapper();
            String secretDataJson = mapper.writeValueAsString(secretData);
            String credentialDataJson = mapper.writeValueAsString(credentialData);

            LOG.info("\nSecret Data JSON:");
            LOG.info(secretDataJson);
            LOG.info("\nCredential Data JSON:");
            LOG.info(credentialDataJson);

            // Verify với giá trị từ database
            String storedSecretData = "{\"value\":\"pbolXEQB0eICy3wQck36Br+cS/GiRrbktKESNdnwW9c57jijzyyYLH26zW3JssGQxvoj4fnsOOOApDF97xvdJg==\",\"salt\":\"+LmBB2RqCDKL+waHTQZZLg==\",\"additionalParameters\":{}}";
            String storedCredentialData = "{\"hashIterations\":210000,\"algorithm\":\"pbkdf2-sha512\",\"additionalParameters\":{}}";

            LOG.info("\nSo sánh với giá trị từ database:");
            LOG.info("Secret Data khớp: " + secretDataJson.equals(storedSecretData));
            LOG.info("Credential Data khớp: " + credentialDataJson.equals(storedCredentialData));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
} 
package com.onepay.keycloak.auth;

import org.keycloak.authentication.AuthenticationFlowContext;
import org.keycloak.authentication.Authenticator;
import org.keycloak.email.EmailException;
import org.keycloak.email.EmailTemplateProvider;
import com.onepay.keycloak.email.MaEmailTemplateProvider;
import org.keycloak.events.Errors;
import org.keycloak.events.EventType;
import org.keycloak.models.ClientModel;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.RealmModel;
import org.keycloak.models.UserModel;
import org.keycloak.models.utils.KeycloakModelUtils;
import org.keycloak.services.Urls;
import org.keycloak.services.managers.AuthenticationManager;
import org.keycloak.services.messages.Messages;
import org.keycloak.sessions.AuthenticationSessionModel;
import org.keycloak.util.TokenUtil;

import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriBuilder;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;
import org.keycloak.authentication.actiontoken.execactions.ExecuteActionsActionToken;

import java.util.Collections;
import java.util.List;
/**
 * Custom Reset Credentials Authenticator for MA Portal
 * Matches OneAM behavior: shows error if email not found
 */
public class MaResetCredentialsAuthenticator implements Authenticator {

    private static final Logger LOG = Logger.getLogger(MaResetCredentialsAuthenticator.class.getName());
    
    // Email validation regex (same as OneAM Utils.isEmail)
    private static final String EMAIL_REGEX = "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$";

    @Override
    public void authenticate(AuthenticationFlowContext context) {
        LOG.info("=== MA RESET CREDENTIALS AUTHENTICATOR START ===");
        
        // Show reset password form
        context.challenge(
            context.form()
                .createPasswordReset()
        );
    }

    @Override
    public void action(AuthenticationFlowContext context) {
        LOG.info("=== Processing reset password request ===");
        
        MultivaluedMap<String, String> formData = context.getHttpRequest().getDecodedFormParameters();
        String username = formData.getFirst("username");
        
        LOG.info("Reset password request for: " + username);
        
        // 1. Validate email format (match OneAM)
        if (username == null || username.trim().isEmpty()) {
            LOG.warning("Username is empty");
            context.getEvent().error(Errors.USERNAME_MISSING);
            context.challenge(
                context.form()
                    .setError("missingUsernameMessage")
                    .createPasswordReset()
            );
            return;
        }
        
        if (!username.matches(EMAIL_REGEX)) {
            LOG.warning("Invalid email format: " + username);
            context.getEvent().error(Errors.INVALID_EMAIL);
            context.challenge(
                context.form()
                    .setError("wrongEmailFormat")
                    .setAttribute("username", username)
                    .createPasswordReset()
            );
            return;
        }
        
        // 2. Find user by email (match OneAM behavior)
        UserModel user = context.getSession().users().getUserByEmail(context.getRealm(), username.toLowerCase());
        
        if (user == null) {
            // Try by username as fallback
            user = context.getSession().users().getUserByUsername(context.getRealm(), username.toLowerCase());
        }
        
        if (user == null) {
            // Match OneAM: return error if email not found
            LOG.warning("Email not found in system: " + username);
            context.getEvent()
                .clone()
                .event(EventType.RESET_PASSWORD_ERROR)
                .detail("username", username)
                .error(Errors.USER_NOT_FOUND);
            
            context.challenge(
                context.form()
                    .setError("emailNotFound")
                    .setAttribute("username", username)
                    .createPasswordReset()
            );
            return;
        }
        
        // 3. Check if user is enabled (match OneAM)
        if (!user.isEnabled()) {
            LOG.warning("User is disabled: " + username);
            context.getEvent().error(Errors.USER_DISABLED);
            context.challenge(
                context.form()
                    .setError("accountDisabled")
                    .setAttribute("username", username)
                    .createPasswordReset()
            );
            return;
        }
        
        // 4. Send reset password email
        LOG.info("User found and enabled, sending reset password email");
        
        // Test email configuration first
        // testEmailConfiguration(context);
        
        try {
            LOG.info("=== SENDING RESET PASSWORD EMAIL ===");
            LOG.info("User ID: " + user.getId());
            LOG.info("User Email: " + user.getEmail());
            
            // IMPORTANT: Set required action BEFORE sending email
            user.removeRequiredAction(UserModel.RequiredAction.UPDATE_PASSWORD.name());
            user.addRequiredAction(MaUpdatePasswordActionFactory.PROVIDER_ID);
            
            LOG.info("Added UPDATE_PASSWORD required action for user");
            user.getRequiredActionsStream().forEach(action -> LOG.info(" - " + action));
            
            // Use Keycloak's built-in sendExecuteActions - it handles everything
            int validityInSecs = context.getRealm().getActionTokenGeneratedByUserLifespan();
            
            // FIX: Use proper redirect URI for password reset, not client redirect URI
            String baseUri = context.getSession().getContext().getUri().getBaseUri().toString();
            String realmName = context.getRealm().getName();
            String clientId = context.getAuthenticationSession().getClient().getClientId();
            AuthenticationSessionModel authSession = context.getAuthenticationSession();
            KeycloakSession session = context.getSession();
            String tabId = authSession.getTabId(); // 🔹 Rất quan trọng, phải có tabId
            String clientData = null; // nếu không có dữ liệu bổ sung
            long nowInSeconds = System.currentTimeMillis() / 1000L;
            long exp = nowInSeconds + validityInSecs + 30000;
            String issuer = Urls.realmIssuer(context.getSession().getContext().getUri().getBaseUri(), realmName);

            
            // Construct proper redirect URI for password reset - redirect to reset password page
            String redirectUri = baseUri + "realms/" + realmName + "/login-actions/action-token";
            
            LOG.info("=== REDIRECT URI CONFIGURATION ===");
            LOG.info("Base URI: " + baseUri);
            LOG.info("Realm name: " + realmName);
            LOG.info("Client ID: " + clientId);
            LOG.info("Constructed redirect URI: " + redirectUri);
            LOG.info("=== END REDIRECT URI CONFIGURATION ===");
            
            LOG.info("=== EMAIL LINK CONFIGURATION ===");
            LOG.info("Redirect URI (after password reset): " + redirectUri);
            LOG.info("Token validity: " + validityInSecs + " seconds (" + (validityInSecs/60) + " minutes)");
            LOG.info("Realm name: " + context.getRealm().getName());
            LOG.info("Realm display name: " + context.getRealm().getDisplayName());
            LOG.info("Base URI: " + context.getSession().getContext().getUri().getBaseUri());
            LOG.info("Client ID: " + context.getAuthenticationSession().getClient().getClientId());
            
            // Check email configuration
            LOG.info("=== EMAIL CONFIGURATION CHECK ===");
            LOG.info("Realm email enabled: " + context.getRealm().isLoginWithEmailAllowed());
            LOG.info("User email verified: " + user.isEmailVerified());
            LOG.info("User email: " + user.getEmail());


            // debug
            RealmModel realm = context.getRealm();
            ClientModel client = authSession.getClient();
            LOG.info("realm: " + (realm != null ? realm.getName() : "null"));
            LOG.info("authSession: " + (authSession != null ? authSession.getTabId() : "null"));
            LOG.info("client: " + (client != null ? client.getClientId() : "null"));
            LOG.info("redirectUri: " + authSession.getRedirectUri());
            LOG.info("authSession.parentSession=" + authSession.getParentSession().getId());
            LOG.info("issuer=" + issuer);

            LOG.info("=== END EMAIL CONFIGURATION CHECK ===");
            
            // The link will be: <baseUri>/realms/<realm>/login-actions/action-token?key=<token>&client_id=<client>
            // After user clicks link and updates password, will redirect to: redirectUri
            LOG.info("Expected reset password link format: " + baseUri + 
                     "realms/" + realmName + "/login-actions/action-token?key=<token>&client_id=" + clientId);
            LOG.info("After password reset, user will be redirected to: " + redirectUri);
            LOG.info("=== END EMAIL LINK CONFIGURATION ===");
            
            // Use Keycloak's built-in email template provider with enhanced logging
            EmailTemplateProvider emailProvider = context.getSession().getProvider(EmailTemplateProvider.class);
            
            // Check email provider configuration
            LOG.info("=== EMAIL PROVIDER DEBUG ===");
            LOG.info("emailProvider: " + emailProvider);
            if (emailProvider == null) {
                LOG.severe("EmailTemplateProvider is NULL! This indicates a configuration issue.");
                LOG.severe("Please check:");
                LOG.severe("  1. Keycloak email configuration in Admin Console");
                LOG.severe("  2. SMTP settings are properly configured");
                LOG.severe("  3. Email provider is enabled in realm settings");
                throw new EmailException("EmailTemplateProvider is not available. Please check email configuration.");
            }
            LOG.info("Email provider class: " + emailProvider.getClass().getName());
            LOG.info("=== END EMAIL PROVIDER DEBUG ===");
            
             // --- 1. Tạo action token
            ExecuteActionsActionToken token = new ExecuteActionsActionToken(
                    user.getId(),
                    (int) exp,
                    List.of(MaUpdatePasswordActionFactory.PROVIDER_ID),
                    user.getEmail(),
                    clientId
            );
            LOG.info("ExecuteActionsActionToken token: " + token);

            // GẮN ISSUER VÀ AUDIENCE
            try {
                java.lang.reflect.Field issuerField =
                    org.keycloak.representations.JsonWebToken.class.getDeclaredField("issuer");
                issuerField.setAccessible(true);
                issuerField.set(token, issuer);
            } catch (Exception e) {
                e.printStackTrace();
            }

            token.setRedirectUri(authSession.getRedirectUri());

            // --- 2. Mã hoá token
            String tokenString = session.tokens().encode(token);
            LOG.info("tokenString: " + tokenString);

            // --- 3. gen base uri
            UriBuilder builder = Urls.actionTokenBuilder(
                session.getContext().getUri().getBaseUri(),
                tokenString,
                clientId,
                tabId,
                clientData
            );

            // --- 4. Tạo link đầy đủ (chuẩn Keycloak 26)
            String resetLink = builder.build(realm.getName()).toString();

            LOG.info("Generated Reset Password Link: " + resetLink);
            LOG.info("Token expires in: " + validityInSecs + " seconds (" + (validityInSecs / 60) + " minutes)");

            LOG.info("check required actions for user:");
            user.getRequiredActionsStream().forEach(action -> LOG.info(" - " + action));

            try {
                LOG.info("Calling sendExecuteActions() with parameters:");
                LOG.info("  - redirectUri: " + redirectUri);
                LOG.info("  - validityInSecs: " + validityInSecs);
                LOG.info("  - realmName: " + context.getRealm().getDisplayName());
                
                // Log before sending email
                LOG.info("=== BEFORE SENDING EMAIL ===");
                LOG.info("User: " + user.getEmail());
                LOG.info("Realm: " + context.getRealm().getName());
                LOG.info("Client: " + context.getAuthenticationSession().getClient().getClientId());
                
                emailProvider.setRealm(context.getRealm())
                    .setUser(user)
                    .setAttribute("realmName", context.getRealm().getDisplayName())
                    .sendExecuteActions(resetLink, (long) validityInSecs);

                LOG.info("sendExecuteActions() completed without exception");
                
                // Log the expected token URL pattern
                LOG.info("=== TOKEN URL PATTERN ===");
                LOG.info("Expected token URL will be in format:");
                LOG.info("  " + baseUri + "realms/" + realmName + "/login-actions/action-token?key=<GENERATED_TOKEN>&client_id=" + clientId);
                LOG.info("The <GENERATED_TOKEN> will be created by Keycloak internally");
                LOG.info("After user clicks the link, they will be redirected to: " + redirectUri);
                LOG.info("=== END TOKEN URL PATTERN ===");
                
            } catch (EmailException e) {
                LOG.severe("EmailException in sendExecuteActions: " + e.getMessage());
                LOG.severe("EmailException details: " + e.getClass().getName());
                if (e.getCause() != null) {
                    LOG.severe("EmailException cause: " + e.getCause().getMessage());
                }
                e.printStackTrace();
                throw e;
            } catch (Exception e) {
                LOG.severe("Unexpected exception in sendExecuteActions: " + e.getMessage());
                LOG.severe("Exception type: " + e.getClass().getName());
                if (e.getCause() != null) {
                    LOG.severe("Exception cause: " + e.getCause().getMessage());
                }
                e.printStackTrace();
                throw e;
            }
            
            context.getEvent()
                .event(EventType.SEND_RESET_PASSWORD)
                .user(user)
                .detail("username", username)
                .detail("email", user.getEmail())
                .success();
            
            LOG.info("Reset password email sent successfully to: " + user.getEmail());
            
            // Show success page (info.ftl) - DON'T call context.success()
            context.challenge(context.form().createInfoPage());
            
        } catch (Exception e) {
            LOG.severe("Failed to send reset password email: " + e.getMessage());
            e.printStackTrace();
            
            context.getEvent().error(Errors.EMAIL_SEND_FAILED);
            context.challenge(
                context.form()
                    .setError("emailSendError")
                    .setAttribute("username", username)
                    .createPasswordReset()
            );
        }
    }

    @Override
    public boolean requiresUser() {
        return false;
    }

    @Override
    public boolean configuredFor(KeycloakSession session, RealmModel realm, UserModel user) {
        return true;
    }

    @Override
    public void setRequiredActions(KeycloakSession session, RealmModel realm, UserModel user) {
        // Not needed for reset credentials
    }

    @Override
    public void close() {
        // Cleanup if needed
    }
    
    /**
     * Test email configuration and log detailed information
     */
    private void testEmailConfiguration(AuthenticationFlowContext context) {
        LOG.info("=== TESTING EMAIL CONFIGURATION ===");
        
        try {
            // Check if email is enabled in realm
            boolean emailEnabled = context.getRealm().isLoginWithEmailAllowed();
            LOG.info("Email login enabled: " + emailEnabled);
            
            // Test email provider
            EmailTemplateProvider emailProvider = context.getSession().getProvider(EmailTemplateProvider.class);
            if (emailProvider == null) {
                LOG.severe("EmailTemplateProvider is NULL!");
            } else {
                LOG.info("EmailTemplateProvider: " + emailProvider.getClass().getName());
            }
            
            // Check realm configuration
            LOG.info("Realm name: " + context.getRealm().getName());
            LOG.info("Realm display name: " + context.getRealm().getDisplayName());
            LOG.info("Realm email login allowed: " + context.getRealm().isLoginWithEmailAllowed());
            
        } catch (Exception e) {
            LOG.severe("Error testing email configuration: " + e.getMessage());
            e.printStackTrace();
        }
        
        LOG.info("=== END EMAIL CONFIGURATION TEST ===");
    }
}


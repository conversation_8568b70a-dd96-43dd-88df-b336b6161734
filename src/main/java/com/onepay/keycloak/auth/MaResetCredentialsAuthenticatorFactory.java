package com.onepay.keycloak.auth;

import org.keycloak.Config;
import org.keycloak.authentication.Authenticator;
import org.keycloak.authentication.AuthenticatorFactory;
import org.keycloak.models.AuthenticationExecutionModel;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.KeycloakSessionFactory;
import org.keycloak.provider.ProviderConfigProperty;

import java.util.Collections;
import java.util.List;

public class MaResetCredentialsAuthenticatorFactory implements AuthenticatorFactory {

    public static final String PROVIDER_ID = "ma-reset-credentials";
    private static final MaResetCredentialsAuthenticator SINGLETON = new MaResetCredentialsAuthenticator();

    @Override
    public String getDisplayType() {
        return "MA Reset Credentials";
    }

    public String getDisplayName() {
        return "MA Reset Credentials";
    }

    @Override
    public String getHelpText() {
        return "Reset credentials authenticator for MA Portal - validates email exists before sending reset email";
    }

    @Override
    public String getReferenceCategory() {
        return "resetCredentials";
    }

    @Override
    public boolean isConfigurable() {
        return false;
    }

    @Override
    public AuthenticationExecutionModel.Requirement[] getRequirementChoices() {
        return new AuthenticationExecutionModel.Requirement[]{
            AuthenticationExecutionModel.Requirement.REQUIRED
        };
    }

    @Override
    public boolean isUserSetupAllowed() {
        return false;
    }

    @Override
    public List<ProviderConfigProperty> getConfigProperties() {
        return Collections.emptyList();
    }

    @Override
    public Authenticator create(KeycloakSession session) {
        return SINGLETON;
    }

    @Override
    public void init(Config.Scope config) {
        // No initialization needed
    }

    @Override
    public void postInit(KeycloakSessionFactory factory) {
        // No post-init needed
    }

    @Override
    public void close() {
        // No cleanup needed
    }

    @Override
    public String getId() {
        return PROVIDER_ID;
    }
}


package com.onepay.keycloak.auth.util;

import org.keycloak.authentication.AuthenticationFlowContext;
import java.util.logging.Logger;

/**
 * Utility class for client IP detection
 * Handles various proxy/load balancer scenarios to get real client IP
 */
public class ClientIpUtil {
    
    private static final Logger log = Logger.getLogger(ClientIpUtil.class.getName());

    /**
     * Get real client IP address from request headers
     * Handles proxy/load balancer scenarios by checking multiple headers in priority order
     * 
     * @param context AuthenticationFlowContext
     * @return Real client IP address
     */
    public static String getRealClientIp(AuthenticationFlowContext context) {
        String ip = "";

        // Debug: Log all IP-related headers
        log.info("=== DEBUG IP HEADERS ===");
        String xForwardedFor = context.getHttpRequest().getHttpHeaders().getHeaderString("X-Forwarded-For");
        String xRealIp = context.getHttpRequest().getHttpHeaders().getHeaderString("X-Real-IP");
        String xClientIp = context.getHttpRequest().getHttpHeaders().getHeaderString("X-Client-IP");
        String cfConnectingIp = context.getHttpRequest().getHttpHeaders().getHeaderString("CF-Connecting-IP");
        String xOriginalForwardedFor = context.getHttpRequest().getHttpHeaders()
                .getHeaderString("X-Original-Forwarded-For");
        String trueClientIp = context.getHttpRequest().getHttpHeaders().getHeaderString("True-Client-IP");

        String directIp = "";
        if (context.getConnection() != null && context.getConnection().getRemoteAddr() != null) {
            directIp = context.getConnection().getRemoteAddr();
        }
        log.info("Direct connection IP: " + directIp);
        log.info("=== END DEBUG IP HEADERS ===");

        // Check True-Client-IP header first (highest priority)
        if (trueClientIp != null && !trueClientIp.trim().isEmpty()) {
            ip = trueClientIp.trim();
            log.info("Using IP from True-Client-IP: " + ip);
            return ip;
        }

        // Check X-Real-IP header (used by nginx and some other proxies) - HIGH PRIORITY
        if (xRealIp != null && !xRealIp.trim().isEmpty()) {
            ip = xRealIp.trim();
            log.info("Using IP from X-Real-IP: " + ip);
            return ip;
        }

        // Check X-Original-Forwarded-For header
        if (xOriginalForwardedFor != null && !xOriginalForwardedFor.trim().isEmpty()) {
            ip = xOriginalForwardedFor.split(",")[0].trim();
            log.info("Using IP from X-Original-Forwarded-For: " + ip);
            return ip;
        }

        // Check X-Forwarded-For header - but try to get the LAST IP which might be the
        // real client
        if (xForwardedFor != null && !xForwardedFor.trim().isEmpty()) {
            String[] ips = xForwardedFor.split(",");
            log.info("X-Forwarded-For contains " + ips.length + " IPs: " + xForwardedFor);

            // Try different strategies to find real client IP
            if (ips.length > 1) {
                // Check if any IP is not a private IP
                for (String candidateIp : ips) {
                    candidateIp = candidateIp.trim();
                    if (!isPrivateIp(candidateIp)) {
                        ip = candidateIp;
                        log.info("Using first non-private IP from X-Forwarded-For: " + ip);
                        return ip;
                    }
                }
            }

            // If all IPs are private, use the first one
            ip = ips[0].trim();
            log.info("Using first IP from X-Forwarded-For: " + ip);
            return ip;
        }

        // Check X-Client-IP header (used by some proxies)
        if (xClientIp != null && !xClientIp.trim().isEmpty()) {
            ip = xClientIp.trim();
            log.info("Using IP from X-Client-IP: " + ip);
            return ip;
        }

        // Check CF-Connecting-IP header (CloudFlare)
        if (cfConnectingIp != null && !cfConnectingIp.trim().isEmpty()) {
            ip = cfConnectingIp.trim();
            log.info("Using IP from CF-Connecting-IP: " + ip);
            return ip;
        }

        // Fallback to direct connection IP
        ip = directIp;
        log.info("Using direct connection IP (may be proxy): " + ip);

        return ip;
    }

    /**
     * Check if IP is private/internal
     * 
     * @param ip IP address to check
     * @return true if IP is private/internal
     */
    public static boolean isPrivateIp(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return true;
        }
        ip = ip.trim();

        // Check for private IP ranges
        return ip.startsWith("10.") ||
                ip.startsWith("192.168.") ||
                ip.startsWith("172.16.") || ip.startsWith("172.17.") || ip.startsWith("172.18.") ||
                ip.startsWith("172.19.") || ip.startsWith("172.20.") || ip.startsWith("172.21.") ||
                ip.startsWith("172.22.") || ip.startsWith("172.23.") || ip.startsWith("172.24.") ||
                ip.startsWith("172.25.") || ip.startsWith("172.26.") || ip.startsWith("172.27.") ||
                ip.startsWith("172.28.") || ip.startsWith("172.29.") || ip.startsWith("172.30.") ||
                ip.startsWith("172.31.") ||
                ip.startsWith("127.") ||
                ip.equals("localhost") ||
                ip.equals("::1");
    }
}

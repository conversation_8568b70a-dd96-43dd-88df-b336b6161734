package com.onepay.keycloak.auth.config;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import java.util.logging.Logger;
import org.keycloak.authentication.AuthenticationFlowContext;

/**
 * Configuration manager for OnePay authentication settings
 * Loads configuration from onepay-auth-config.properties
 */
public class AuthConfigManager {

    private static final Logger log = Logger.getLogger(AuthConfigManager.class.getName());
    private static final String CONFIG_FILE = "onepay-auth-config.properties";
    private static Properties properties;

    static {
        loadProperties();
    }

    private static void loadProperties() {
        properties = new Properties();
        try (InputStream input = AuthConfigManager.class.getClassLoader().getResourceAsStream(CONFIG_FILE)) {
            if (input == null) {
                log.warning("Configuration file " + CONFIG_FILE + " not found, using default values");
                loadDefaultProperties();
                return;
            }
            // Load with UTF-8 encoding to handle Vietnamese characters
            properties.load(new java.io.InputStreamReader(input, java.nio.charset.StandardCharsets.UTF_8));
            log.info("Loaded configuration from " + CONFIG_FILE);
        } catch (IOException e) {
            log.warning("Error loading configuration file: " + e.getMessage() + ", using default values");
            loadDefaultProperties();
        }
    }

    private static void loadDefaultProperties() {
        // Cookie Configuration
        properties.setProperty("cookie.name", "op_td");
        properties.setProperty("cookie.max.age.days", "7");
        properties.setProperty("cookie.path", "/");
        properties.setProperty("cookie.secure", "true");
        properties.setProperty("cookie.http.only", "true");
        properties.setProperty("cookie.same.site", "Lax");

        // Device Tracking Configuration
        properties.setProperty("device.tracking.max.age.days", "180");
        properties.setProperty("device.tracking.alert.enabled", "true");

        // OTP Configuration
        properties.setProperty("otp.length", "6");
        properties.setProperty("otp.ttl.seconds", "300");
        properties.setProperty("otp.max.attempts", "5");
        properties.setProperty("otp.resend.interval.seconds", "60");

        // Email Configuration
        properties.setProperty("email.subject.otp", "[OnePay Invoice] Mã xác thực OTP / OTP Code");
        properties.setProperty("email.subject.device.alert",
                "[OnePay Invoice] Cảnh báo đăng nhập từ thiết bị lạ / New device login detected");
        properties.setProperty("email.template.otp", "code-email.ftl");
        properties.setProperty("email.template.device.alert", "device-alert.ftl");

        // Security Configuration
        properties.setProperty("security.private.ip.ranges",
                "10.,192.168.,172.16.,172.17.,172.18.,172.19.,172.20.,172.21.,172.22.,172.23.,172.24.,172.25.,172.26.,172.27.,172.28.,172.29.,172.30.,172.31.,127.,localhost,::1");

        // Logging Configuration
        properties.setProperty("logging.enable.debug", "true");
        properties.setProperty("logging.enable.device.info", "true");
    }

    // Cookie Configuration
    public static String getCookieName() {
        return properties.getProperty("cookie.name", "op_td");
    }

    /**
     * Get theme-specific cookie name based on request context
     * @param context AuthenticationFlowContext to detect theme from URL
     * @return Theme-specific cookie name
     */
    public static String getCookieName(AuthenticationFlowContext context) {
        String theme = detectTheme(context);
        return getCookieName(theme);
    }

    /**
     * Get theme-specific cookie name
     * @param theme Theme name (ma)
     * @return Theme-specific cookie name
     */
    public static String getCookieName(String theme) {
        if ("ma".equals(theme)) {
            return properties.getProperty("cookie.name.ma", "op_td_ma");
        } else {
            // Fallback to default cookie name
            return properties.getProperty("cookie.name", "op_td_ma");
        }
    }

    /**
     * Detect theme from request URL
     * @param context AuthenticationFlowContext
     * @return Theme name (always "ma" for MA Portal)
     */
    public static String detectTheme(AuthenticationFlowContext context) {
        try {
            String requestPath = context.getHttpRequest().getUri().getPath();
            if (requestPath != null) {
                if (requestPath.contains("/auth-ma") || requestPath.contains("/ma")) {
                    return "ma";
                }
            }
        } catch (Exception e) {
            // Log error but don't fail
            System.err.println("Error detecting theme from request: " + e.getMessage());
        }
        // Default theme for MA
        return "ma";
    }

    public static int getCookieMaxAgeDays() {
        return Integer.parseInt(properties.getProperty("cookie.max.age.days", "7"));
    }

    public static int getCookieMaxAgeSeconds() {
        return getCookieMaxAgeDays() * 24 * 60 * 60;
    }

    public static String getCookiePath() {
        return properties.getProperty("cookie.path", "/");
    }

    public static boolean isCookieSecure() {
        return Boolean.parseBoolean(properties.getProperty("cookie.secure", "true"));
    }

    public static boolean isCookieHttpOnly() {
        return Boolean.parseBoolean(properties.getProperty("cookie.http.only", "true"));
    }

    public static String getCookieSameSite() {
        return properties.getProperty("cookie.same.site", "Lax");
    }

    public static int getCookieMaxPerUser() {
        return Integer.parseInt(properties.getProperty("cookie.max.per.user", "5"));
    }

    // Device Tracking Configuration
    public static int getDeviceTrackingMaxAgeDays() {
        return Integer.parseInt(properties.getProperty("device.tracking.max.age.days", "180"));
    }

    public static long getDeviceTrackingMaxAgeMillis() {
        return getDeviceTrackingMaxAgeDays() * 24L * 60L * 60L * 1000L;
    }

    public static boolean isDeviceTrackingAlertEnabled() {
        return Boolean.parseBoolean(properties.getProperty("device.tracking.alert.enabled", "true"));
    }

    public static boolean isDeviceAlertEnabled() {
        return Boolean.parseBoolean(properties.getProperty("device.alert.enabled", "true"));
    }

    // OTP Configuration
    public static int getOtpLength() {
        return Integer.parseInt(properties.getProperty("otp.length", "6"));
    }

    public static int getOtpTtlSeconds() {
        return Integer.parseInt(properties.getProperty("otp.ttl.seconds", "300"));
    }

    public static int getOtpMaxAttempts() {
        return Integer.parseInt(properties.getProperty("otp.max.attempts", "5"));
    }

    public static long getOtpResendIntervalMillis() {
        return Long.parseLong(properties.getProperty("otp.resend.interval.seconds", "60")) * 1000L;
    }

    // Email Configuration
    public static String getEmailSubjectOtp() {
        return properties.getProperty("email.subject.otp", "[OnePay MA] Mã xác thực OTP / OTP Code");
    }

    public static String getEmailSubjectDeviceAlert() {
        return properties.getProperty("email.subject.device.alert",
                "[OnePay MA] Cảnh báo đăng nhập từ thiết bị lạ / New device login detected");
    }

    public static String getEmailTemplateOtp() {
        return properties.getProperty("email.template.otp", "code-email.ftl");
    }

    public static String getEmailTemplateDeviceAlert() {
        return properties.getProperty("email.template.device.alert", "device-alert.ftl");
    }

    // Security Configuration
    public static String[] getPrivateIpRanges() {
        return properties.getProperty("security.private.ip.ranges", "10.,192.168.,127.,localhost,::1").split(",");
    }

    // Logging Configuration
    public static boolean isDebugEnabled() {
        return Boolean.parseBoolean(properties.getProperty("logging.enable.debug", "true"));
    }

    public static boolean isDeviceInfoEnabled() {
        return Boolean.parseBoolean(properties.getProperty("logging.enable.device.info", "true"));
    }

    // Database Configuration
    public static String getDatabaseUrl() {
        return properties.getProperty("database.url", "***************************************************************************");
    }

    public static String getDatabaseUser() {
        return properties.getProperty("database.user", "postgres");
    }

    public static String getDatabasePassword() {
        return properties.getProperty("database.password", "4n8c8f5t");
    }

    public static int getDatabaseConnectionTimeout() {
        return Integer.parseInt(properties.getProperty("database.connection.timeout", "30"));
    }

    public static int getDatabaseQueryTimeout() {
        return Integer.parseInt(properties.getProperty("database.query.timeout", "10"));
    }

    // Utility method to reload configuration (useful for testing)
    public static void reload() {
        loadProperties();
    }
}

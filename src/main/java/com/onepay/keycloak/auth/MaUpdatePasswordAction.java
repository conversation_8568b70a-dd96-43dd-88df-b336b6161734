package com.onepay.keycloak.auth;

import org.keycloak.models.*;
import org.keycloak.models.credential.PasswordUserCredentialModel;
import org.keycloak.authentication.RequiredActionContext;
import org.keycloak.authentication.RequiredActionProvider;
import org.keycloak.forms.login.LoginFormsProvider;

import jakarta.ws.rs.core.MultivaluedMap;
import java.util.logging.Logger;
import java.util.regex.Pattern;

public class MaUpdatePasswordAction implements RequiredActionProvider {

    private static final Logger LOG = Logger.getLogger(MaUpdatePasswordAction.class.getName());

    public MaUpdatePasswordAction() {
        LOG.info("=== [MA] MaUpdatePasswordAction Constructor ===");
    }

    // OneAM Password Encoding Keys (same as MaUserAuthenticator)
    private static final String ONEAM_PASSWORD_KEY_1 = "23BF1DC999734E6A6860BD56273AA455";
    private static final String ONEAM_PASSWORD_KEY_2 = "23BF1DC999734E6A6860BD56273AA455";

    // Password validation regex (same as OneAM)
    private static final Pattern PASSWORD_PATTERN = Pattern.compile(
        "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%?&])[A-Za-z\\d@$!%?&]{12,}$"
    );

    @Override
    public void evaluateTriggers(RequiredActionContext context) {
        LOG.info("=== [MA] MaUpdatePasswordAction.evaluateTriggers() called ===");
        // Check if user has this required action
        UserModel user = context.getUser();
        if (user != null && user.getRequiredActionsStream().anyMatch(action ->
            MaUpdatePasswordActionFactory.PROVIDER_ID.equals(action))) {
            LOG.info("User has MA_UPDATE_PASSWORD required action - will show form");
        }
    }

    @Override
    public void requiredActionChallenge(RequiredActionContext context) {
        LOG.info("=== [MA] Showing password update form ===");
        LOG.info("User: " + context.getUser().getUsername());
        LOG.info("User email: " + context.getUser().getEmail());
        LOG.info("Required actions: " + context.getUser().getRequiredActionsStream().toList());

        context.challenge(context.form().createForm("login-update-password.ftl"));
    }

    @Override
    public void processAction(RequiredActionContext context) {
        LOG.info("=== [MA] Processing password change ===");
        LOG.info("User: " + context.getUser().getUsername());

        MultivaluedMap<String, String> formData = context.getHttpRequest().getDecodedFormParameters();
        String newPassword = formData.getFirst("password-new");
        String confirmPassword = formData.getFirst("password-confirm");
        String maAction = formData.getFirst("ma-action");

        LOG.info("Form data received:");
        LOG.info("  - password-new: " + (newPassword != null ? "[PRESENT]" : "[MISSING]"));
        LOG.info("  - password-confirm: " + (confirmPassword != null ? "[PRESENT]" : "[MISSING]"));
        LOG.info("  - ma-action: " + maAction);

        // Validate required fields
        if (newPassword == null || newPassword.trim().isEmpty()) {
            LOG.warning("New password is missing");
            showError(context, "missingPasswordMessage", "New password is required");
            return;
        }

        if (confirmPassword == null || confirmPassword.trim().isEmpty()) {
            LOG.warning("Confirm password is missing");
            showError(context, "missingPasswordMessage", "Confirm password is required");
            return;
        }

        // Validate passwords match
        if (!newPassword.equals(confirmPassword)) {
            LOG.warning("Passwords do not match");
            showError(context, "passwordConfirmNotMatchMessage", "Passwords do not match");
            return;
        }

        // Validate password policy (OneAM rules)
        if (!isValidPassword(newPassword)) {
            LOG.warning("Password does not meet policy requirements");
            showError(context, "invalidPasswordMessage",
                "Password must be at least 12 characters with uppercase, lowercase, number and special character");
            return;
        }

        // Encode password using OneAM logic (level 12: double HMAC-SHA256)
        String encodedPassword = encodePasswordOneAM(newPassword, 12);
        LOG.info("Password encoded using OneAM logic (level 12)");

        UserModel user = context.getUser();

        try {
            // Create credential with OneAM encoded password
            PasswordUserCredentialModel credential = UserCredentialModel.password(encodedPassword, false);

            // Update password in Keycloak
            boolean updated = user.credentialManager().updateCredential(credential);
            LOG.info("Password updated in Keycloak: " + updated);

            if (updated) {
                // Remove the required action
                user.removeRequiredAction(MaUpdatePasswordActionFactory.PROVIDER_ID);
                LOG.info("Removed MA_UPDATE_PASSWORD required action");

                // Success - redirect to application
                context.success();
                LOG.info("Password reset completed successfully");
            } else {
                LOG.severe("Failed to update password in Keycloak");
                showError(context, "passwordUpdateFailedMessage", "Failed to update password");
            }

        } catch (Exception e) {
            LOG.severe("Exception during password update: " + e.getMessage());
            e.printStackTrace();
            showError(context, "passwordUpdateFailedMessage", "An error occurred while updating password");
        }
    }

    @Override
    public void close() {}

    // Helper methods

    private void showError(RequiredActionContext context, String errorKey, String errorMessage) {
        LOG.warning("Showing error: " + errorKey + " - " + errorMessage);
        context.challenge(context.form()
            .setError(errorKey)
            .createForm("login-update-password.ftl"));
    }

    private boolean isValidPassword(String password) {
        if (password == null || password.length() < 12) {
            return false;
        }
        return PASSWORD_PATTERN.matcher(password).matches();
    }

    // OneAM Password Encoding Functions (same as MaUserAuthenticator)

    private String encodePasswordOneAM(String password, int level) {
        LOG.info("=== ENCODING PASSWORD ===");
        LOG.info("Input password: [HIDDEN]");
        LOG.info("Encoding level: " + level);

        if (level == 1) {
            String result = hmacSHA256ToHex(ONEAM_PASSWORD_KEY_1, password);
            LOG.info("Level 1 result: " + result);
            return result;
        }

        if (level == 12) {
            // Level 12: Apply key1 first, then key2
            String step1 = hmacSHA256ToHex(ONEAM_PASSWORD_KEY_1, password);
            LOG.info("Step 1 (Key1 + password): " + step1);

            String step2 = hmacSHA256ToHex(ONEAM_PASSWORD_KEY_2, step1);
            LOG.info("Step 2 (Key2 + step1): " + step2);
            LOG.info("Final encoded password: " + step2);

            return step2;
        }

        if (level == 2) {
            // Level 2: Apply key2 directly (assuming password already has key1 applied)
            String result = hmacSHA256ToHex(ONEAM_PASSWORD_KEY_2, password);
            LOG.info("Level 2 result: " + result);
            return result;
        }

        return "";
    }

    private String hmacSHA256ToHex(String key, String data) {
        try {
            LOG.info("  -> hmacSHA256ToHex: key=" + key + ", data=[HIDDEN]");
            // Decode hex key to bytes (like OneAM)
            byte[] keyBytes = hexStringToBytes(key);
            javax.crypto.Mac mac = javax.crypto.Mac.getInstance("HmacSHA256");
            javax.crypto.spec.SecretKeySpec secretKeySpec = new javax.crypto.spec.SecretKeySpec(keyBytes, "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] hash = mac.doFinal(data.getBytes());
            String result = bytesToHex(hash);
            LOG.info("  -> hmacSHA256ToHex result: " + result);
            return result;
        } catch (Exception e) {
            LOG.severe("Error calculating HMAC-SHA256: " + e.getMessage());
            return "";
        }
    }

    private byte[] hexStringToBytes(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                                 + Character.digit(hex.charAt(i+1), 16));
        }
        return data;
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X", b));
        }
        return result.toString();
    }

}
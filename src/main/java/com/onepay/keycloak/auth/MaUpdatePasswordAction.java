package com.onepay.keycloak.auth;

import org.keycloak.models.*;
import org.keycloak.models.credential.PasswordUserCredentialModel;
import org.keycloak.authentication.RequiredActionContext;
import org.keycloak.authentication.RequiredActionProvider;

import java.util.logging.Logger;

public class MaUpdatePasswordAction implements RequiredActionProvider {

    private static final Logger LOG = Logger.getLogger(MaUpdatePasswordAction.class.getName());

    @Override
    public void evaluateTriggers(RequiredActionContext context) {
        // không cần gì thêm
    }

    @Override
    public void requiredActionChallenge(RequiredActionContext context) {
        LOG.info("=== [MA] Showing password update form ===");
        context.challenge(context.form().createForm("login-update-password.ftl"));
    }

    @Override
    public void processAction(RequiredActionContext context) {
        LOG.info("=== [MA] Processing password change ===");

        var formData = context.getHttpRequest().getDecodedFormParameters();
        var newPass = formData.getFirst("password-new");

        if (newPass == null || newPass.trim().isEmpty()) {
            context.challenge(context.form()
                .setError("missingPasswordMessage")
                .createForm("login-update-password.ftl"));
            return;
        }

        var user = context.getUser();
        PasswordUserCredentialModel credential = UserCredentialModel.password(newPass, false);

        // Cập nhật password
        boolean updated = user.credentialManager().updateCredential(credential);
        LOG.info("Password updated: " + updated);

        user.removeRequiredAction(MaUpdatePasswordActionFactory.PROVIDER_ID);
        context.success();
    }

    @Override
    public void close() {}

}
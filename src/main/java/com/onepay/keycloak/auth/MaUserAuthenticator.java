package com.onepay.keycloak.auth;

import org.keycloak.authentication.AuthenticationFlowContext;
import org.keycloak.authentication.AuthenticationFlowError;
import org.keycloak.authentication.Authenticator;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.RealmModel;
import org.keycloak.models.UserModel;
import org.keycloak.events.Errors;
import org.keycloak.models.UserCredentialModel;
import org.keycloak.credential.CredentialModel;
import org.keycloak.forms.login.LoginFormsProvider;
import org.keycloak.models.utils.FormMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.ws.rs.core.MultivaluedMap;
import java.util.logging.Logger;

public class MaUserAuthenticator implements Authenticator {

    private static final Logger LOG = Logger.getLogger(MaUserAuthenticator.class.getName());

    public static final String USERNAME_PARAM = "username";
    public static final String PASSWORD_PARAM = "password";

    @Override
    public void authenticate(AuthenticationFlowContext context) {
        LOG.info("=== MA USER AUTHENTICATOR START ===");
        LOG.fine("MaUserAuthenticator: authenticate() method invoked.");

        // Kiểm tra nếu đã có user thì skip
        if (context.getUser() != null) {
            LOG.info("User already authenticated, skipping");
            context.attempted();
            return;
        }

        // Hiển thị form login
        LOG.info("Showing login form");
        context.challenge(
                context.form()
                        .setAttribute("username", "")
                        .createLoginUsernamePassword());
    }

    @Override
    public void action(AuthenticationFlowContext context) {
        LOG.info("=== Bat dau qua trinh xac thuc MA ===");
        LOG.fine("MaUserAuthenticator: action() method invoked.");

        MultivaluedMap<String, String> formData = context.getHttpRequest().getDecodedFormParameters();

        String usernameInput = formData.getFirst(USERNAME_PARAM);
        String password = formData.getFirst(PASSWORD_PARAM);

        LOG.info("Du lieu nhan duoc tu form:");
        LOG.info("- Username: " + usernameInput);
        LOG.info("- Password length: " + (password != null ? password.length() : 0));

        // Không cần sanitize vì đăng nhập bằng email
        String sanitizedUsername = usernameInput;
        LOG.info("- Username (email): " + sanitizedUsername);

        // Lưu username gốc để hiển thị trong email
        context.getAuthenticationSession().setAuthNote("original_username", usernameInput);

        if (usernameInput == null || usernameInput.isEmpty() || password == null || password.isEmpty()) {
            LOG.severe("Thieu thong tin bat buoc.");
            context.getEvent().error(Errors.INVALID_USER_CREDENTIALS);
            context.challenge(
                    context.form()
                            .setError("invalidUserMessage")
                            .setAttribute("username", usernameInput != null ? usernameInput : "")
                            .createLoginUsernamePassword());
            return;
        }

        // Tìm user trong Keycloak
        UserModel user = context.getSession().users().getUserByUsername(context.getRealm(), sanitizedUsername);
        if (user == null) {
            // Thử tìm bằng email
            user = context.getSession().users().getUserByEmail(context.getRealm(), sanitizedUsername);
        }

        if (user == null) {
            LOG.severe("Khong tim thay user: " + sanitizedUsername);
            context.getEvent().error(Errors.USER_NOT_FOUND);
            context.challenge(
                    context.form()
                            .setError("invalidUserMessage")
                            .setAttribute("username", usernameInput)
                            .createLoginUsernamePassword());
            return;
        }

        LOG.info("Tim thay user: " + user.getUsername() + " (ID: " + user.getId() + ")");

        // Kiểm tra user có enabled không
        if (!user.isEnabled()) {
            LOG.severe("User bi disable: " + user.getUsername());
            context.getEvent().error(Errors.USER_DISABLED);
            context.challenge(
                    context.form()
                            .setError("userDisabledMessage")
                            .setAttribute("username", usernameInput)
                            .createLoginUsernamePassword());
            return;
        }

        // Xác thực password theo OneAM logic (như invoice)
        if (!validatePasswordOneAM(user, password, context)) {
            LOG.severe("Sai mat khau cho user: " + user.getUsername());
            context.getEvent().error(Errors.INVALID_USER_CREDENTIALS);
            context.challenge(
                    context.form()
                            .setError("invalidUserMessage")
                            .setAttribute("username", usernameInput)
                            .createLoginUsernamePassword());
            return;
        }

        LOG.info("User validation successful: " + user.getUsername());
        context.setUser(user);
        context.success();
    }



    private boolean validatePasswordOneAM(UserModel user, String password, AuthenticationFlowContext context) {
        try {
            // Lấy thông tin credential từ database (như invoice)
            List<CredentialModel> credentials = user.credentialManager().getStoredCredentialsByTypeStream("password")
                    .collect(Collectors.toList());
            LOG.info("Number of stored credentials: " + credentials.size());

            if (credentials.isEmpty()) {
                LOG.severe("No password credential found for user");
                return false;
            }

            String credentialData = credentials.get(0).getSecretData();
            String credentialConfig = credentials.get(0).getCredentialData();
            LOG.info("Credential data from database: " + credentialData);
            LOG.info("Credential config from database: " + credentialConfig);

            // Parse JSON để lấy password hash và salt
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(credentialData);
            JsonNode configNode = mapper.readTree(credentialConfig);

            String storedHash = jsonNode.get("value").asText();
            String storedSalt = jsonNode.get("salt").asText();
            String algorithm = configNode.get("algorithm").asText();
            int hashIterations = configNode.get("hashIterations").asInt();

            LOG.info("Stored hash from database: " + storedHash);
            LOG.info("Stored salt from database: " + storedSalt);
            LOG.info("Algorithm: " + algorithm);
            LOG.info("Hash iterations: " + hashIterations);

            // Encode password theo OneAM logic (level 12: 2 lần)
            String encodedPassword = encodePasswordOneAM(password, 12);
            LOG.info("OneAM encoded password: " + encodedPassword);

            // Sử dụng Keycloak's built-in validation với encoded password
            UserCredentialModel userCredential = UserCredentialModel.password(encodedPassword);
            boolean isValid = user.credentialManager().isValid(userCredential);
            
            LOG.info("Keycloak credential validation result: " + isValid);
            
            return isValid;
        } catch (Exception e) {
            LOG.severe("Loi khi validate password: " + e.getMessage());
            return false;
        }
    }

    // OneAM Password Encoding Functions
    private static final String ONEAM_PASSWORD_KEY_1 = "23BF1DC999734E6A6860BD56273AA455";
    private static final String ONEAM_PASSWORD_KEY_2 = "23BF1DC999734E6A6860BD56273AA455";

    private String encodePasswordOneAM(String password, int level) {
        LOG.info("=== ENCODING PASSWORD ===");
        LOG.info("Input password: " + password);
        LOG.info("Encoding level: " + level);
        LOG.info("Key1: " + ONEAM_PASSWORD_KEY_1);
        LOG.info("Key2: " + ONEAM_PASSWORD_KEY_2);
        
        if (level == 1) {
            String result = hmacSHA256ToHex(ONEAM_PASSWORD_KEY_1, password);
            LOG.info("Level 1 result: " + result);
            return result;
        }

        if (level == 12) {
            // Level 12: Apply key1 first, then key2
            String step1 = hmacSHA256ToHex(ONEAM_PASSWORD_KEY_1, password);
            LOG.info("Step 1 (Key1 + password): " + step1);
            
            String step2 = hmacSHA256ToHex(ONEAM_PASSWORD_KEY_2, step1);
            LOG.info("Step 2 (Key2 + step1): " + step2);
            LOG.info("Final encoded password: " + step2);
            
            return step2;
        }

        if (level == 2) {
            // Level 2: Apply key2 directly (assuming password already has key1 applied)
            String result = hmacSHA256ToHex(ONEAM_PASSWORD_KEY_2, password);
            LOG.info("Level 2 result: " + result);
            return result;
        }

        return "";
    }

    private String hmacSHA256ToHex(String key, String data) {
        try {
            LOG.info("  -> hmacSHA256ToHex: key=" + key + ", data=" + data);
            // Decode hex key to bytes (như OneAM)
            byte[] keyBytes = hexStringToBytes(key);
            javax.crypto.Mac mac = javax.crypto.Mac.getInstance("HmacSHA256");
            javax.crypto.spec.SecretKeySpec secretKeySpec = new javax.crypto.spec.SecretKeySpec(keyBytes, "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] hash = mac.doFinal(data.getBytes());
            String result = bytesToHex(hash);
            LOG.info("  -> hmacSHA256ToHex result: " + result);
            return result;
        } catch (Exception e) {
            LOG.severe("Loi khi tinh HMAC-SHA256: " + e.getMessage());
            return "";
        }
    }
    
    private byte[] hexStringToBytes(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X", b));  // Chữ HOA để khớp với OneAM
        }
        return result.toString();
    }

    @Override
    public boolean requiresUser() {
        return false;
    }

    @Override
    public boolean configuredFor(KeycloakSession session, RealmModel realm, UserModel user) {
        return true;
    }

    @Override
    public void setRequiredActions(KeycloakSession session, RealmModel realm, UserModel user) {
        // Không cần required actions
    }

    @Override
    public void close() {
        // Cleanup resources if needed
    }
}
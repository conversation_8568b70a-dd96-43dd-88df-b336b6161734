# ================================================================
# OnePay MA Authentication Configuration
# ================================================================
# File này chứa tất cả cấu hình cho hệ thống xác thực OnePay MA
# Được sử dụng bởi AuthConfigManager để quản lý các tham số
# 
# CÁCH SỬ DỤNG:
# 1. Thay đổi giá trị trong file này
# 2. Restart Keycloak để áp dụng thay đổi
# 3. Các class sẽ tự động load config mới
# ================================================================

# ================================================================
# COOKIE CONFIGURATION - Cấu hình Cookie xác thực
# ================================================================
# Cookie này được tạo sau khi OTP thành công để bypass OTP trong 90 ngày
# Giải quyết vấn đề: Khi user chuyển từ WiFi sang mạng dây (IP thay đổi)
# 
# SỬ DỤNG: CookieDeviceManager.java, EmailOtpCombinedAuthenticator.java
# MỤC ĐÍCH: Giảm số lần phải nhập OTP cho user đã xác thực
# ================================================================
# Tên cookie cho MA theme
cookie.name.ma=op_td_ma
# Tên cookie mặc định (backward compatibility)
cookie.name=op_td_ma
# Thời hạn cookie (90 ngày)
cookie.max.age.days=90
# Path cookie hoạt động (toàn bộ domain)
cookie.path=/
# Chỉ gửi qua HTTPS (false cho test local, true cho production)
cookie.secure=false
# Không cho JavaScript truy cập (bảo mật)
cookie.http.only=true
# Bảo vệ CSRF (Lax/Strict/None)
cookie.same.site=Lax
# Số lượng cookie tối đa per user (5 cho production)
cookie.max.per.user=5

# ================================================================
# DEVICE TRACKING CONFIGURATION - Cấu hình theo dõi thiết bị
# ================================================================
# Hệ thống dựa trên cookie + User-Agent để nhận diện thiết bị
# Được mở rộng từ 7 ngày lên 90 ngày để tăng UX
# 
# SỬ DỤNG: EmailOtpCombinedAuthenticator.java, OtpLoginHistoryManager.java
# MỤC ĐÍCH: Theo dõi và cảnh báo thiết bị mới
# ================================================================
# Thời hạn device tracking (90 ngày)
device.tracking.max.age.days=90
# Có gửi email cảnh báo thiết bị mới không
device.tracking.alert.enabled=true
device.alert.enabled=true

# ================================================================
# OTP CONFIGURATION - Cấu hình mã OTP
# ================================================================
# Các tham số liên quan đến việc tạo và xác thực OTP
# 
# SỬ DỤNG: EmailOtpCombinedAuthenticator.java
# MỤC ĐÍCH: Kiểm soát độ dài, thời hạn và số lần thử OTP
# ================================================================
# Độ dài mã OTP (6 chữ số)
otp.length=6
# Thời hạn OTP (5 phút)
otp.ttl.seconds=300
# Số lần nhập sai tối đa trước khi block
otp.max.attempts=5
# Khoảng cách giữa các lần gửi lại OTP (1 phút)
otp.resend.interval.seconds=60

# ================================================================
# EMAIL CONFIGURATION - Cấu hình email
# ================================================================
# Các template và subject cho email OTP và cảnh báo thiết bị
# 
# SỬ DỤNG: EmailOtpCombinedAuthenticator.java
# MỤC ĐÍCH: Tùy chỉnh nội dung email gửi cho user
# ================================================================
email.subject.otp=[OnePay MA] Mã xác thực OTP / OTP Code
email.subject.device.alert=[OnePay MA] Cảnh báo đăng nhập từ thiết bị lạ / New device login detected
# Template email OTP (trong theme merchant-portal)
email.template.otp=code-email.ftl
# Template email cảnh báo thiết bị (trong theme merchant-portal)
email.template.device.alert=device-alert.ftl

# ================================================================
# SECURITY CONFIGURATION - Cấu hình bảo mật
# ================================================================
# Danh sách IP range nội bộ (không cảnh báo thiết bị mới)
# ================================================================
security.private.ip.ranges=10.,192.168.,172.16.,172.17.,172.18.,172.19.,172.20.,172.21.,172.22.,172.23.,172.24.,172.25.,172.26.,172.27.,172.28.,172.29.,172.30.,172.31.,127.,localhost,::1

# ================================================================
# DATABASE CONFIGURATION - Cấu hình database
# ================================================================
# Cấu hình kết nối database cho OTP login history
# MA dùng cùng database với Keycloak (localhost)
# 
# SỬ DỤNG: OtpLoginHistoryManager.java
# MỤC ĐÍCH: Lưu trữ lịch sử đăng nhập OTP để skip OTP cho lần sau
# ================================================================
# Database connection URL (MA dùng localhost, schema public)
database.url=**********************************************************************
# Database username
database.user=postgres
# Database password
database.password=4n8c8f5t
# Database connection timeout (seconds)
database.connection.timeout=30
# Database query timeout (seconds)
database.query.timeout=10

# ================================================================
# LOGGING CONFIGURATION - Cấu hình log
# ================================================================
# Bật/tắt các loại log để debug và monitor
# 
# SỬ DỤNG: Tất cả các class authentication
# MỤC ĐÍCH: Kiểm soát mức độ log để debug và production
# ================================================================
# Bật log debug chi tiết
logging.enable.debug=true
# Bật log thông tin thiết bị
logging.enable.device.info=true
<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans.xsd
                           http://www.springframework.org/schema/tx
                           http://www.springframework.org/schema/tx/spring-tx.xsd
                           http://www.springframework.org/schema/aop
                           http://www.springframework.org/schema/aop/spring-aop.xsd
                           http://www.springframework.org/schema/context
            http://www.springframework.org/schema/context/spring-context.xsd">
    <!--<context:property-placeholder location="classpath:config-app.properties"/>-->
    <bean id="appDataSource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="oracle.jdbc.driver.OracleDriver"/>
        <property name="url" value="#{systemProperties['app.db.url']}"/>
        <property name="username" value="#{systemProperties['app.db.username']}"/>
        <property name="password" value="#{systemProperties['app.db.password']}"/>
        <property name="connectionProperties" value="useUnicode=yes;characterEncoding=utf8;"/>
        <property name="initialSize" value="1"/>
        <property name="maxTotal" value="30"/>
        <property name="maxIdle" value="5"/>
        <property name="minIdle" value="1"/>
        <property name="maxWaitMillis" value="60000"/>
        <property name="validationQuery" value="select 1 from dual"/>
        <property name="testOnCreate" value="false"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="true"/>
        <property name="minEvictableIdleTimeMillis" value="1800000"/>
        <property name="timeBetweenEvictionRunsMillis" value="300000"/>
        <property name="removeAbandonedOnMaintenance" value="true"/>
        <property name="removeAbandonedTimeout" value="300"/>
        <property name="maxConnLifetimeMillis" value="18000000"/>
        <property name="lifo" value="true"/>
    </bean>
    <bean id="annotationSessionFactoryBean"
          class="org.springframework.orm.hibernate3.annotation.AnnotationSessionFactoryBean">
        <property name="dataSource" ref="appDataSource"/>
        <property name="hibernateProperties">
            <props>
                <prop key="hibernate.dialect">org.hibernate.dialect.Oracle10gDialect</prop>
                <prop key="hibernate.connection.useUnicode">true</prop>
                <prop key="hibernate.connection.charSet">UTF-8</prop>
                <prop key="hibernate.connection.characterEncoding">UTF-8</prop>
                <prop key="hibernate.show_sql">true</prop>
                <prop key="hibernate.format_sql">true</prop>
            </props>
        </property>
        <property name="packagesToScan" value="vn.onepay.oneam.entity"/>
    </bean>
    <bean id="txManager" class="org.springframework.orm.hibernate3.HibernateTransactionManager">
        <property name="sessionFactory" ref="annotationSessionFactoryBean"/>
    </bean>
    <tx:advice id="txAdvice" transaction-manager="txManager">
        <tx:attributes>
            <tx:method name="find*" read-only="true"/>
            <tx:method name="execute*" rollback-for="java.lang.Exception" propagation="NESTED"/>
            <tx:method name="delete*" rollback-for="java.lang.Exception"/>
            <tx:method name="update*" rollback-for="java.lang.Exception"/>
            <tx:method name="add*" rollback-for="java.lang.Exception"/>
            <tx:method name="tx*" rollback-for="java.lang.Exception"/>

        </tx:attributes>
    </tx:advice>
    <aop:config proxy-target-class="true">
        <aop:pointcut id="service1" expression="execution(* vn.onepay.oneam.service.*Service.*(..))"/>
        <aop:advisor advice-ref="txAdvice" pointcut-ref="service1"/>
    </aop:config>
    <bean id="hibernateDAO" class="vn.onepay.oneam.dao.HibernateDAO" scope="prototype">
        <property name="sessionFactory" ref="annotationSessionFactoryBean"/>
    </bean>
    <bean id="dbService" class="vn.onepay.oneam.service.DBService" scope="prototype">
        <property name="hibernateDAO" ref="hibernateDAO"/>
    </bean>
    <bean id="resourceService" class="vn.onepay.oneam.service.ResourceServiceImp" scope="prototype"/>
    <bean id="userService" class="vn.onepay.oneam.service.UserService" scope="prototype"/>

    <bean id="httpServer" class="vn.onepay.oneam.server.HttpServer" init-method="init" destroy-method="destroy">
        <constructor-arg value="#{systemProperties['app.port']}" type="int"/>
    </bean>

    <bean id="httpUserServer" class="vn.onepay.oneam.server.HttpServer" init-method="init" destroy-method="destroy">
        <constructor-arg value="#{systemProperties['app.port.user']}" type="int"/>
    </bean>
</beans>
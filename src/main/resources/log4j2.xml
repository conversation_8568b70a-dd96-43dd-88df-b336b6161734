<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info" name="${env:app}" packages="" monitorInterval="30">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n" />
        </Console>
        <RollingFile name="RollingFile" fileName="/var/log/${env:app}/${env:app}.log"
                     filePattern="/var/log/${env:app}/${env:app}.%d{yyyy-MM-dd}.log.gz">
            <PatternLayout>
                <Pattern>%d %p %c{1.} [%t] %m%n</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <!--<SizeBasedTriggeringPolicy size="250 MB"/>-->
            </Policies>
        </RollingFile>
        <Socket name="Graylog" protocol="udp" host="log.onepay.vn" port="12201">
            <GelfLayout compressionType="GZIP" compressionThreshold="1024">
                <KeyValuePair key="app" value="${env:app}"/>
            </GelfLayout>
        </Socket>
    </Appenders>
    <Loggers>
        <AsyncLogger name="vn.onepay" level="debug"/>
        <AsyncLogger name="com.onepay" level="debug"/>
        <AsyncRoot level="info">
            <!--<AppenderRef ref="Console" />-->
            <AppenderRef ref="RollingFile"/>
            <!-- <AppenderRef ref="Graylog"/> -->
        </AsyncRoot>
    </Loggers>
</Configuration>
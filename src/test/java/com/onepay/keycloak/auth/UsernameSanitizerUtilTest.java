package com.onepay.keycloak.auth;

/**
 * Test class for UsernameSanitizerUtil
 */
public class UsernameSanitizerUtilTest {
    
    public static void main(String[] args) {
        System.out.println("=== Test UsernameSanitizerUtil ===");
        
        // Test cases với tiếng <PERSON> có dấu
        testUsername("Nguyễn <PERSON>ăn <PERSON>", "Nguyen_Van_A");
        testUsername("Trần Thị B", "Tran_Thi_B");
        testUsername("Lê Văn C", "Le_Van_C");
        testUsername("Phạm Thị D", "Pham_Thi_D");
        
        // Test cases với ký tự đặc biệt
        testUsername("<EMAIL>", "user_domain.com");
        testUsername("user-name", "user-name");
        testUsername("user_name", "user_name");
        testUsername("user.name", "user.name");
        testUsername("user name", "user_name");
        testUsername("user!name", "user_name");
        testUsername("user#name", "user_name");
        testUsername("user$name", "user_name");
        testUsername("user%name", "user_name");
        testUsername("user&name", "user_name");
        testUsername("user*name", "user_name");
        testUsername("user+name", "user_name");
        testUsername("user=name", "user_name");
        testUsername("user[name]", "user_name");
        testUsername("user{name}", "user_name");
        testUsername("user(name)", "user_name");
        testUsername("user<name>", "user_name");
        testUsername("user\"name\"", "user_name");
        testUsername("user'name'", "user_name");
        testUsername("user`name`", "user_name");
        testUsername("user~name", "user_name");
        testUsername("user\\name", "user_name");
        testUsername("user/name", "user_name");
        testUsername("user|name", "user_name");
        testUsername("user:name", "user_name");
        testUsername("user;name", "user_name");
        testUsername("user,name", "user_name");
        testUsername("user?name", "user_name");
        
        // Test cases với dấu gạch dưới liên tiếp
        testUsername("user___name", "user_name");
        testUsername("___user___name___", "user_name");
        
        // Test cases với username rỗng hoặc null
        testUsername("", "user");
        testUsername(null, "user");
        
        // Test cases với Keycloak username
        testKeycloakUsername("Nguyễn Văn A", "MERCHANT001", "Nguyen_Van_A_merchant001");
        testKeycloakUsername("Trần Thị B", "merchant002", "Tran_Thi_B_merchant002");
    }
    
    private static void testUsername(String input, String expected) {
        String result = UsernameSanitizerUtil.sanitizeUsername(input);
        boolean passed = expected.equals(result);
        System.out.printf("Input: '%s' -> Output: '%s' (Expected: '%s') - %s%n", 
                         input, result, expected, passed ? "PASS" : "FAIL");
    }
    
    private static void testKeycloakUsername(String username, String merchantId, String expected) {
        String result = UsernameSanitizerUtil.createKeycloakUsername(username, merchantId);
        boolean passed = expected.equals(result);
        System.out.printf("Username: '%s', MerchantID: '%s' -> Output: '%s' (Expected: '%s') - %s%n", 
                         username, merchantId, result, expected, passed ? "PASS" : "FAIL");
    }
} 
-- ================================================================
-- Bảng lưu thông tin đăng nhập OTP của khách hàng
-- ================================================================
-- Mục đích: Lưu lại lần đăng nhập bằng OTP để skip OTP cho lần sau
-- Sử dụng: CookieDatabaseManager.java, EmailOtpCombinedAuthenticator.java
-- ================================================================

CREATE TABLE tb_otp_login_history (
    -- Primary key
    s_id VARCHAR(36) PRIMARY KEY,
    
    -- Thông tin user
    s_user_id VARCHAR(255) NOT NULL,
    
    -- Thông tin cookie/device
    s_cookie VARCHAR(500) NOT NULL,
    
    -- Thông tin browser
    s_browser VARCHAR(100),
    s_browser_version VARCHAR(50),
    
    -- Thông tin hệ điều hành
    s_os VARCHAR(100),
    s_os_version VARCHAR(50),
    
    -- Thông tin thiết bị
    s_device VARCHAR(200),
    
    -- Thời gian đăng nhập cuối cùng (bao gồm cả skip OTP)
    t_last_login_activity TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Thông tin bổ sung
    s_user_agent TEXT,
    s_ip_address VARCHAR(45),
    s_device_fingerprint VARCHAR(255),
    
    -- Metadata
    t_created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    t_updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    b_is_active BOOLEAN DEFAULT TRUE,
    
);

-- ================================================================
-- INDEXES để tối ưu performance
-- ================================================================

-- Index cho user_id (query chính)
CREATE INDEX idx_otp_login_user_id ON tb_otp_login_history(s_user_id);

-- Index cho thời gian đăng nhập (cleanup expired)
CREATE INDEX idx_otp_login_last_login ON tb_otp_login_history(t_last_login_activity);

-- Composite index cho query thường dùng
CREATE INDEX idx_otp_login_user_active ON tb_otp_login_history(s_user_id, b_is_active);

-- ================================================================
-- UNIQUE CONSTRAINTS
-- ================================================================

-- Mỗi user chỉ có 1 record active cho mỗi cookie
CREATE UNIQUE INDEX uk_otp_login_user_cookie ON tb_otp_login_history(s_user_id, s_cookie) 
WHERE b_is_active = TRUE;

-- ================================================================
-- TRIGGER để tự động update updated_at
-- ================================================================

CREATE OR REPLACE FUNCTION update_otp_login_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.t_updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_update_otp_login_updated_at
    BEFORE UPDATE ON tb_otp_login_history
    FOR EACH ROW
    EXECUTE FUNCTION update_otp_login_updated_at();

COMMENT ON TABLE tb_otp_login_history IS 'Lưu lịch sử đăng nhập OTP của khách hàng để skip OTP cho lần sau';
COMMENT ON COLUMN tb_otp_login_history.s_user_id IS 'ID của user trong Keycloak';
COMMENT ON COLUMN tb_otp_login_history.s_cookie IS 'Giá trị cookie đã encode (Base64)';
COMMENT ON COLUMN tb_otp_login_history.s_browser IS 'Tên browser (Chrome, Firefox, Safari...)';
COMMENT ON COLUMN tb_otp_login_history.s_browser_version IS 'Phiên bản browser';
COMMENT ON COLUMN tb_otp_login_history.s_os IS 'Hệ điều hành (Windows, macOS, Linux...)';
COMMENT ON COLUMN tb_otp_login_history.s_os_version IS 'Phiên bản hệ điều hành';
COMMENT ON COLUMN tb_otp_login_history.s_device IS 'Loại thiết bị (Desktop, Mobile, Tablet...)';
COMMENT ON COLUMN tb_otp_login_history.t_last_login_activity IS 'Thời gian đăng nhập cuối cùng (bao gồm cả skip OTP)';
COMMENT ON COLUMN tb_otp_login_history.s_user_agent IS 'User-Agent string đầy đủ';
COMMENT ON COLUMN tb_otp_login_history.s_ip_address IS 'IP address khi đăng nhập';
COMMENT ON COLUMN tb_otp_login_history.s_device_fingerprint IS 'Fingerprint của thiết bị (hash User-Agent)';

-- ================================================================
-- MIGRATION SCRIPT: Rename t_last_otp_login to t_last_login_activity
-- ================================================================
-- Mục đích: Đổi tên cột để phản ánh logic mới (track cả skip OTP)
-- Sử dụng: Chạy trên database đã có dữ liệu
-- ================================================================

-- BƯỚC 1: Đổi tên cột
ALTER TABLE tb_otp_login_history 
RENAME COLUMN t_last_otp_login TO t_last_login_activity;

-- BƯỚC 2: Cập nhật comment cho cột mới
COMMENT ON COLUMN tb_otp_login_history.t_last_login_activity IS 'Thời gian đăng nhập cuối cùng (bao gồm cả skip OTP)';

-- BƯỚC 3: Verify migration
-- Kiểm tra cấu trúc bảng sau khi migrate
SELECT column_name, data_type, column_default, is_nullable
FROM information_schema.columns 
WHERE table_name = 'tb_otp_login_history' 
AND column_name IN ('t_last_login_activity', 't_last_otp_login')
ORDER BY ordinal_position;

-- BƯỚC 4: Kiểm tra dữ liệu
-- Verify dữ liệu vẫn còn nguyên
SELECT COUNT(*) as total_records,
       COUNT(t_last_login_activity) as records_with_activity,
       MIN(t_last_login_activity) as earliest_activity,
       MAX(t_last_login_activity) as latest_activity
FROM tb_otp_login_history;

-- ================================================================
-- ROLLBACK SCRIPT (nếu cần quay lại)
-- ================================================================
-- Chỉ chạy nếu cần rollback
-- ALTER TABLE tb_otp_login_history 
-- RENAME COLUMN t_last_login_activity TO t_last_otp_login;
-- 
-- COMMENT ON COLUMN tb_otp_login_history.t_last_otp_login IS 'Thời gian đăng nhập OTP cuối cùng';
-- ================================================================

#!/bin/bash
cd /root/projects/onepay/ma-keycloak-26.3.4
mvn clean package -q


# Copy plugin JAR
cp target/keycloak-merchant-auth-1.0-SNAPSHOT.jar ./providers/


./bin/kc.sh build
./bin/kc.sh start-dev

# http://localhost:8080/auth-ma/realms/merchant-portal/protocol/openid-connect/auth?client_id=merchant-portal-client&redirect_uri=http://localhost:8081/login&response_type=code&scope=openid%20profile%20email  